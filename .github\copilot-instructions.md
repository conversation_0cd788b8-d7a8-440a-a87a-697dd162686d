<!-- Use this file to provide workspace-specific custom instructions to Copi<PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

# Windows Performance Optimization Toolkit

This workspace contains PowerShell scripts designed to diagnose and optimize Windows system performance. The toolkit focuses on improving system responsiveness, boot times, memory usage, and overall PC performance.

## Project Structure

- **System-Diagnostics.ps1** - Comprehensive system analysis and performance diagnostics
- **Performance-Optimizer.ps1** - Automated Windows optimizations (visual effects, power settings, services)
- **Disk-Cleanup.ps1** - Advanced disk cleanup operations and storage optimization
- **Startup-Manager.ps1** - Startup program management and boot time optimization
- **Memory-Optimizer.ps1** - RAM optimization and memory cache management
- **PC-Optimizer.ps1** - Master script that orchestrates all optimization tools

## Coding Guidelines

When working with this project:

1. **PowerShell Best Practices**:
   - Always check for Administrator privileges when required
   - Use proper error handling with try-catch blocks
   - Provide user feedback with colored console output
   - Include parameter validation and help documentation

2. **Safety First**:
   - Create system restore points before major changes
   - Backup registry keys before modification
   - Provide confirmation prompts for destructive operations
   - Include rollback mechanisms where possible

3. **User Experience**:
   - Use colored output for better readability (Green for success, Red for errors, Yellow for warnings)
   - Provide progress indicators for long-running operations
   - Include interactive modes with clear menu options
   - Export reports and logs for user reference

4. **Performance Optimization Focus**:
   - Target common Windows performance bottlenecks
   - Focus on safe, proven optimization techniques
   - Avoid aggressive optimizations that might break functionality
   - Provide clear explanations of what each optimization does

5. **Code Structure**:
   - Use functions for reusable code blocks
   - Include parameter blocks for script flexibility
   - Add comprehensive comments explaining complex operations
   - Follow consistent naming conventions

6. **Testing and Validation**:
   - Test scripts on different Windows versions (10/11)
   - Validate optimizations don't break core functionality
   - Include checks for system compatibility
   - Provide clear error messages and troubleshooting guidance

## Common Windows Optimization Areas

- Visual effects and animations
- Power management settings
- Windows services and startup programs  
- Memory management and virtual memory
- Disk cleanup and temporary file removal
- Registry optimizations
- Network and cache settings
- Background apps and telemetry

When adding new features, prioritize safety, user control, and clear documentation of changes made to the system.
