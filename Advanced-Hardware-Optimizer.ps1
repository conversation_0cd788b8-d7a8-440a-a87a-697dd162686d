# Advanced Hardware-Aware Optimization Script
# Applies sophisticated optimizations based on actual hardware configuration

param(
    [switch]$Interactive,
    [switch]$ApplyAll,
    [switch]$SSDOptimizations,
    [switch]$CPUOptimizations,
    [switch]$RAMOptimizations,
    [switch]$GPUOptimizations,
    [switch]$CreateBackup
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "Administrator privileges required for advanced optimizations."
    exit 1
}

Write-Host "🔧 Advanced Hardware-Aware Optimization" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Hardware Detection Functions
function Get-StorageInfo {
    $storageDevices = @()
    
    # Get physical drives
    $physicalDrives = Get-WmiObject -Class Win32_DiskDrive
    foreach ($drive in $physicalDrives) {
        $partitions = Get-WmiObject -Query "ASSOCIATORS OF {Win32_DiskDrive.DeviceID='$($drive.DeviceID)'} WHERE AssocClass=Win32_DiskDriveToDiskPartition"
        foreach ($partition in $partitions) {
            $logicalDisks = Get-WmiObject -Query "ASSOCIATORS OF {Win32_DiskPartition.DeviceID='$($partition.DeviceID)'} WHERE AssocClass=Win32_LogicalDiskToPartition"
            foreach ($logicalDisk in $logicalDisks) {
                $isSSD = $false
                
                # Multiple methods to detect SSD
                try {
                    # Method 1: Check media type via Get-PhysicalDisk
                    $physicalDisk = Get-PhysicalDisk | Where-Object { $_.DeviceID -eq $drive.Index } -ErrorAction SilentlyContinue
                    if ($physicalDisk) {
                        $isSSD = ($physicalDisk.MediaType -eq "SSD") -or ($physicalDisk.BusType -eq "NVMe")
                    }
                    
                    # Method 2: Check via WMI Win32_DiskDrive properties
                    if (-not $isSSD) {
                        $isSSD = ($drive.Model -like "*SSD*") -or ($drive.Model -like "*NVMe*") -or 
                                ($drive.InterfaceType -eq "IDE" -and $drive.Size -lt 2TB) -or
                                ($drive.Caption -like "*SSD*")
                    }
                    
                    # Method 3: Check rotation rate (SSDs typically report 0 or 1)
                    if (-not $isSSD -and $physicalDisk) {
                        $isSSD = ($physicalDisk.SpindleSpeed -eq 0) -or ($physicalDisk.SpindleSpeed -eq 1)
                    }
                } catch {
                    # Fallback: Assume SSD if size is reasonable and no rotation detected
                    $isSSD = ($drive.Size -lt 4TB) -and ($drive.Model -notlike "*WD*" -and $drive.Model -notlike "*Seagate*")
                }
                
                $storageDevices += [PSCustomObject]@{
                    DriveLetter = $logicalDisk.DeviceID
                    Model = $drive.Model
                    Size = [math]::Round($drive.Size / 1GB, 2)
                    IsSSD = $isSSD
                    InterfaceType = $drive.InterfaceType
                    PhysicalDisk = $physicalDisk
                }
            }
        }
    }
    
    return $storageDevices
}

function Get-CPUInfo {
    $cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
    $cpuFeatures = @{
        Name = $cpu.Name
        Cores = $cpu.NumberOfCores
        LogicalProcessors = $cpu.NumberOfLogicalProcessors
        Architecture = $cpu.Architecture
        MaxClockSpeed = $cpu.MaxClockSpeed
        Manufacturer = $cpu.Manufacturer
        IsIntel = $cpu.Manufacturer -like "*Intel*"
        IsAMD = $cpu.Manufacturer -like "*AMD*"
        SupportsHyperThreading = $cpu.NumberOfLogicalProcessors -gt $cpu.NumberOfCores
        IsLowPower = $cpu.Name -like "*U*" -or $cpu.Name -like "*Y*" -or $cpu.Name -like "*m3*"
    }
    
    return $cpuFeatures
}

function Get-GPUInfo {
    $gpus = @()
    $videoControllers = Get-WmiObject -Class Win32_VideoController
    
    foreach ($gpu in $videoControllers) {
        if ($gpu.Name -notlike "*Basic*" -and $gpu.Name -notlike "*VGA*") {
            $gpus += [PSCustomObject]@{
                Name = $gpu.Name
                DriverVersion = $gpu.DriverVersion
                IsNVIDIA = $gpu.Name -like "*NVIDIA*" -or $gpu.Name -like "*GeForce*"
                IsAMD = $gpu.Name -like "*AMD*" -or $gpu.Name -like "*Radeon*"
                IsIntel = $gpu.Name -like "*Intel*"
                VideoMemory = if ($gpu.AdapterRAM) { [math]::Round($gpu.AdapterRAM / 1MB, 0) } else { 0 }
            }
        }
    }
    
    return $gpus
}

function Get-RAMInfo {
    $ram = Get-WmiObject -Class Win32_PhysicalMemory
    $ramInfo = @{
        TotalRAM = [math]::Round(($ram | Measure-Object -Property Capacity -Sum).Sum / 1GB, 2)
        Modules = $ram.Count
        Speed = ($ram | Select-Object -First 1).Speed
        Type = ($ram | Select-Object -First 1).MemoryType
        IsHighSpeed = ($ram | Select-Object -First 1).Speed -ge 3000
        IsLowRAM = (($ram | Measure-Object -Property Capacity -Sum).Sum / 1GB) -lt 8
    }
    
    return $ramInfo
}

# Display hardware information
Write-Host "🔍 Hardware Detection Results:" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Green

$storageInfo = Get-StorageInfo
$cpuInfo = Get-CPUInfo
$gpuInfo = Get-GPUInfo
$ramInfo = Get-RAMInfo

Write-Host "`n💾 Storage Devices:"
foreach ($storage in $storageInfo) {
    $type = if ($storage.IsSSD) { "SSD" } else { "HDD" }
    Write-Host "  $($storage.DriveLetter) - $($storage.Model) - $($storage.Size) GB ($type)"
}

Write-Host "`n🖥️  CPU: $($cpuInfo.Name)"
Write-Host "  Cores: $($cpuInfo.Cores) | Logical: $($cpuInfo.LogicalProcessors) | Manufacturer: $($cpuInfo.Manufacturer)"

Write-Host "`n🎮 GPU(s):"
foreach ($gpu in $gpuInfo) {
    Write-Host "  $($gpu.Name) - $($gpu.VideoMemory) MB VRAM"
}

Write-Host "`n💿 RAM: $($ramInfo.TotalRAM) GB ($($ramInfo.Modules) modules) - $($ramInfo.Speed) MHz"

# SSD-Specific Optimizations
function Optimize-SSDSettings {
    param([array]$SSDs)
    
    if ($SSDs.Count -eq 0) {
        Write-Host "No SSDs detected - skipping SSD optimizations" -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n💿 Applying SSD-Specific Optimizations..." -ForegroundColor Green
    
    foreach ($ssd in $SSDs) {
        try {
            $driveLetter = $ssd.DriveLetter.Replace(":", "")
            
            # Disable defragmentation for SSDs
            Write-Host "  ✓ Disabling defragmentation for SSD $($ssd.DriveLetter)" -ForegroundColor Gray
            schtasks /Change /TN "Microsoft\Windows\Defrag\ScheduledDefrag" /Disable | Out-Null
            
            # Disable Last Access Time updates (reduces SSD writes)
            Write-Host "  ✓ Disabling Last Access Time updates" -ForegroundColor Gray
            fsutil behavior set DisableLastAccess 1 | Out-Null
            
            # Enable TRIM support
            Write-Host "  ✓ Enabling TRIM support" -ForegroundColor Gray
            fsutil behavior set DisableDeleteNotify 0 | Out-Null
            
            # Disable hibernation (saves SSD space and write cycles)
            Write-Host "  ✓ Disabling hibernation to save SSD writes" -ForegroundColor Gray
            powercfg.exe /hibernate off | Out-Null
            
            # Optimize prefetch for SSD (reduce to minimal)
            Write-Host "  ✓ Optimizing prefetch for SSD" -ForegroundColor Gray
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" -Name "EnablePrefetcher" -Value 1 -Force
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" -Name "EnableSuperfetch" -Value 0 -Force
            
            # Disable Windows Search indexing on SSD (optional - faster but less search functionality)
            Write-Host "  ✓ Optimizing Windows Search for SSD" -ForegroundColor Gray
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows Search" -Name "SetupCompletedSuccessfully" -Value 0 -Force -ErrorAction SilentlyContinue
            
            # Optimize page file for SSD
            Write-Host "  ✓ Optimizing page file for SSD" -ForegroundColor Gray
            # Smaller initial size for SSD to reduce wear
            $initialSize = [math]::Min([math]::Round($ramInfo.TotalRAM * 0.5 * 1024), 4096)  # Max 4GB initial
            $maximumSize = [math]::Round($ramInfo.TotalRAM * 1.5 * 1024)  # 1.5x RAM maximum
            
        } catch {
            Write-Host "  ⚠️  Error optimizing $($ssd.DriveLetter): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ SSD optimizations completed" -ForegroundColor Green
}

# CPU-Specific Optimizations
function Optimize-CPUSettings {
    param([object]$CPU)
    
    Write-Host "`n🖥️  Applying CPU-Specific Optimizations..." -ForegroundColor Green
    
    try {
        # Intel-specific optimizations
        if ($CPU.IsIntel) {
            Write-Host "  ✓ Applying Intel CPU optimizations" -ForegroundColor Gray
            
            # Enable Intel Turbo Boost
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Power" -Name "TurboBoostPolicy" -Value 0 -Force -ErrorAction SilentlyContinue
            
            # Optimize Intel SpeedStep
            powercfg.exe /setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 be337238-0d82-4146-a960-4f3749d470c7 0
        }
        
        # AMD-specific optimizations
        if ($CPU.IsAMD) {
            Write-Host "  ✓ Applying AMD CPU optimizations" -ForegroundColor Gray
            
            # Enable AMD Cool'n'Quiet
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Power" -Name "CoolNQuietPolicy" -Value 1 -Force -ErrorAction SilentlyContinue
        }
        
        # Multi-core optimizations
        if ($CPU.Cores -gt 4) {
            Write-Host "  ✓ Optimizing for multi-core CPU ($($CPU.Cores) cores)" -ForegroundColor Gray
            
            # Optimize processor scheduling for throughput
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl" -Name "Win32PrioritySeparation" -Value 24 -Force
            
            # Enable all CPU cores for boot
            bcdedit /set numproc $CPU.LogicalProcessors | Out-Null
        }
        
        # Hyper-Threading optimizations
        if ($CPU.SupportsHyperThreading) {
            Write-Host "  ✓ Optimizing for Hyper-Threading" -ForegroundColor Gray
            
            # Optimize thread scheduling
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\kernel" -Name "ThreadDpcEnable" -Value 1 -Force -ErrorAction SilentlyContinue
        }
        
        # Low-power CPU optimizations
        if ($CPU.IsLowPower) {
            Write-Host "  ✓ Applying low-power CPU optimizations" -ForegroundColor Gray
            
            # More aggressive power management
            powercfg.exe /setacvalueindex SCHEME_CURRENT 54533251-82be-4824-96c1-47b60b740d00 5d76a2ca-e8c0-402f-a133-2158492d58ad 1
        }
        
    } catch {
        Write-Host "  ⚠️  Error applying CPU optimizations: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "✅ CPU optimizations completed" -ForegroundColor Green
}

# RAM-Specific Optimizations
function Optimize-RAMSettings {
    param([object]$RAM)
    
    Write-Host "`n💿 Applying RAM-Specific Optimizations..." -ForegroundColor Green
    
    try {
        # High RAM system optimizations (16GB+)
        if ($RAM.TotalRAM -ge 16) {
            Write-Host "  ✓ Optimizing for high RAM system ($($RAM.TotalRAM) GB)" -ForegroundColor Gray
            
            # Disable paging executive to keep system code in RAM
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "DisablePagingExecutive" -Value 1 -Force
            
            # Increase system cache size
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "LargeSystemCache" -Value 1 -Force
            
            # Reduce page file size (system has plenty of RAM)
            $initialSize = [math]::Round($RAM.TotalRAM * 0.25 * 1024)  # 25% of RAM
            $maximumSize = [math]::Round($RAM.TotalRAM * 0.5 * 1024)   # 50% of RAM
            
        } elseif ($RAM.IsLowRAM) {
            Write-Host "  ✓ Optimizing for low RAM system ($($RAM.TotalRAM) GB)" -ForegroundColor Gray
            
            # More aggressive memory management
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "DisablePagingExecutive" -Value 0 -Force
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "LargeSystemCache" -Value 0 -Force
            
            # Larger page file for low RAM systems
            $initialSize = [math]::Round($RAM.TotalRAM * 1.5 * 1024)  # 1.5x RAM
            $maximumSize = [math]::Round($RAM.TotalRAM * 3 * 1024)    # 3x RAM
        }
        
        # High-speed RAM optimizations
        if ($RAM.IsHighSpeed) {
            Write-Host "  ✓ Optimizing for high-speed RAM ($($RAM.Speed) MHz)" -ForegroundColor Gray
            
            # Reduce memory timings in registry
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettings" -Value 1 -Force -ErrorAction SilentlyContinue
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "FeatureSettingsOverride" -Value 3 -Force -ErrorAction SilentlyContinue
        }
        
    } catch {
        Write-Host "  ⚠️  Error applying RAM optimizations: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "✅ RAM optimizations completed" -ForegroundColor Green
}

# GPU-Specific Optimizations
function Optimize-GPUSettings {
    param([array]$GPUs)
    
    Write-Host "`n🎮 Applying GPU-Specific Optimizations..." -ForegroundColor Green
    
    foreach ($gpu in $GPUs) {
        try {
            if ($gpu.IsNVIDIA) {
                Write-Host "  ✓ Applying NVIDIA optimizations for $($gpu.Name)" -ForegroundColor Gray
                
                # NVIDIA-specific registry tweaks
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" -Name "TdrLevel" -Value 0 -Force -ErrorAction SilentlyContinue
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" -Name "TdrDelay" -Value 60 -Force -ErrorAction SilentlyContinue
            }
            
            if ($gpu.IsAMD) {
                Write-Host "  ✓ Applying AMD optimizations for $($gpu.Name)" -ForegroundColor Gray
                
                # AMD-specific optimizations
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" -Name "TdrLevel" -Value 0 -Force -ErrorAction SilentlyContinue
            }
            
            if ($gpu.IsIntel) {
                Write-Host "  ✓ Applying Intel GPU optimizations for $($gpu.Name)" -ForegroundColor Gray
                
                # Intel integrated graphics optimizations
                Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\GraphicsDrivers" -Name "TdrLevel" -Value 0 -Force -ErrorAction SilentlyContinue
            }
            
        } catch {
            Write-Host "  ⚠️  Error optimizing GPU $($gpu.Name): $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ GPU optimizations completed" -ForegroundColor Green
}

# Network Optimizations based on hardware
function Optimize-NetworkSettings {
    Write-Host "`n🌐 Applying Network Optimizations..." -ForegroundColor Green
    
    try {
        # TCP/IP optimizations for modern hardware
        Write-Host "  ✓ Optimizing TCP/IP for modern hardware" -ForegroundColor Gray
        
        # Enable TCP window scaling
        netsh int tcp set global autotuninglevel=normal | Out-Null
        
        # Enable Compound TCP
        netsh int tcp set global chimney=enabled | Out-Null
        
        # Optimize receive side scaling
        netsh int tcp set global rss=enabled | Out-Null
        
        # Set optimal receive window
        netsh int tcp set global rsc=enabled | Out-Null
        
    } catch {
        Write-Host "  ⚠️  Error applying network optimizations: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "✅ Network optimizations completed" -ForegroundColor Green
}

# Advanced System Optimizations
function Optimize-AdvancedSettings {
    Write-Host "`n🔧 Applying Advanced System Optimizations..." -ForegroundColor Green
    
    try {
        # Optimize NTFS for performance
        Write-Host "  ✓ Optimizing NTFS settings" -ForegroundColor Gray
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "NtfsDisableLastAccessUpdate" -Value 1 -Force
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "NtfsAllowExtendedCharacterIn8dot3Name" -Value 0 -Force
        
        # Optimize kernel for desktop performance
        Write-Host "  ✓ Optimizing kernel for desktop performance" -ForegroundColor Gray
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\PriorityControl" -Name "IRQ8Priority" -Value 1 -Force -ErrorAction SilentlyContinue
        
        # Optimize memory management
        Write-Host "  ✓ Optimizing memory management" -ForegroundColor Gray
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "IoPageLockLimit" -Value 0xFFFFFFFF -Force -ErrorAction SilentlyContinue
        
        # Disable unnecessary Windows features based on hardware
        Write-Host "  ✓ Disabling hardware-incompatible Windows features" -ForegroundColor Gray
        
        # Disable Windows Media Player if dedicated GPU present
        if ($gpuInfo | Where-Object { -not $_.IsIntel }) {
            Disable-WindowsOptionalFeature -Online -FeatureName "WindowsMediaPlayer" -NoRestart -ErrorAction SilentlyContinue | Out-Null
        }
        
    } catch {
        Write-Host "  ⚠️  Error applying advanced optimizations: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    
    Write-Host "✅ Advanced optimizations completed" -ForegroundColor Green
}

# Main execution
if ($CreateBackup -or $ApplyAll) {
    Write-Host "📌 Creating System Restore Point..." -ForegroundColor Yellow
    try {
        Checkpoint-Computer -Description "Advanced Hardware Optimization - $(Get-Date)" -RestorePointType "MODIFY_SETTINGS"
        Write-Host "✅ System Restore Point created" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Could not create restore point: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Apply optimizations based on detected hardware
$ssds = $storageInfo | Where-Object { $_.IsSSD }

if ($ApplyAll -or $SSDOptimizations) {
    Optimize-SSDSettings $ssds
}

if ($ApplyAll -or $CPUOptimizations) {
    Optimize-CPUSettings $cpuInfo
}

if ($ApplyAll -or $RAMOptimizations) {
    Optimize-RAMSettings $ramInfo
}

if ($ApplyAll -or $GPUOptimizations) {
    Optimize-GPUSettings $gpuInfo
}

if ($ApplyAll) {
    Optimize-NetworkSettings
    Optimize-AdvancedSettings
}

Write-Host "`n🏁 Advanced Hardware-Aware Optimization Complete!" -ForegroundColor Cyan

# Display optimization summary
Write-Host "`n📋 Optimization Summary:" -ForegroundColor Yellow
Write-Host "========================" -ForegroundColor Yellow

if ($ssds.Count -gt 0) {
    Write-Host "✅ SSD-specific optimizations applied to $($ssds.Count) drive(s)"
}

Write-Host "✅ CPU optimizations applied for $($cpuInfo.Manufacturer) $($cpuInfo.Cores)-core processor"
Write-Host "✅ RAM optimizations applied for $($ramInfo.TotalRAM) GB system"
Write-Host "✅ GPU optimizations applied for $($gpuInfo.Count) graphics adapter(s)"

Write-Host "`n💡 Hardware-Specific Recommendations:" -ForegroundColor Green
if ($ssds.Count -gt 0) {
    Write-Host "• SSD optimizations: Reduced write operations, disabled defrag, enabled TRIM"
}
if ($ramInfo.TotalRAM -ge 16) {
    Write-Host "• High RAM: System cache prioritized, reduced page file usage"
}
if ($cpuInfo.Cores -gt 4) {
    Write-Host "• Multi-core: Optimized thread scheduling and core utilization"
}

Write-Host "`n🔄 Restart recommended to apply all hardware-specific optimizations." -ForegroundColor Yellow
