# Disk Cleanup Script
# Advanced cleanup operations to free up disk space

param(
    [switch]$Interactive,
    [switch]$DeepClean,
    [string]$TargetDrive = "C:",
    [switch]$IncludeBrowsers,
    [switch]$EmptyRecycleBin,
    [switch]$CleanLogs
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Some cleanup operations require Administrator privileges. Running with limited functionality."
}

Write-Host "🧹 Advanced Disk Cleanup" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""

function Get-FolderSize {
    param([string]$Path)
    if (Test-Path $Path) {
        try {
            $size = (Get-ChildItem -Path $Path -Recurse -Force -ErrorAction SilentlyContinue | Measure-Object -Property Length -Sum).Sum
            return [math]::Round($size / 1MB, 2)
        } catch {
            return 0
        }
    }
    return 0
}

function Confirm-Action {
    param([string]$Message)
    if ($Interactive) {
        $response = Read-Host "$Message (y/n)"
        return $response -eq 'y' -or $response -eq 'Y'
    }
    return $true
}

function Remove-ItemsSafely {
    param(
        [string]$Path,
        [string]$Description
    )
    
    if (Test-Path $Path) {
        $sizeBefore = Get-FolderSize $Path
        if ($sizeBefore -gt 0) {
            try {
                Get-ChildItem -Path $Path -Recurse -Force -ErrorAction SilentlyContinue | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                $sizeAfter = Get-FolderSize $Path
                $cleaned = $sizeBefore - $sizeAfter
                if ($cleaned -gt 0) {
                    Write-Host "  ✓ $Description`: Cleaned $cleaned MB" -ForegroundColor Green
                    return $cleaned
                }
            } catch {
                Write-Host "  ⚠️  Could not clean $Description`: $($_.Exception.Message)" -ForegroundColor Yellow
            }
        }
    }
    return 0
}

$totalCleaned = 0

# Display initial disk space
Write-Host "📊 Current Disk Usage for $TargetDrive" -ForegroundColor Green
try {
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $TargetDrive }
    $totalSize = [math]::Round($disk.Size / 1GB, 2)
    $freeSpace = [math]::Round($disk.FreeSpace / 1GB, 2)
    $usedSpace = $totalSize - $freeSpace
    $usagePercent = [math]::Round(($usedSpace / $totalSize) * 100, 2)
    
    Write-Host "Total Size: $totalSize GB"
    Write-Host "Used Space: $usedSpace GB ($usagePercent%)"
    Write-Host "Free Space: $freeSpace GB"
    Write-Host ""
} catch {
    Write-Host "Could not retrieve disk information for $TargetDrive"
}

# Clean Windows Temporary Files
if (Confirm-Action "🗂️  Clean Windows Temporary Files?") {
    Write-Host "🗂️  Cleaning Windows Temporary Files..." -ForegroundColor Green
    
    $tempFolders = @{
        "$env:TEMP" = "User Temp Files"
        "$env:WINDIR\Temp" = "Windows Temp Files"
        "$env:LOCALAPPDATA\Temp" = "Local Temp Files"
        "$env:WINDIR\Prefetch" = "Prefetch Files"
        "$env:LOCALAPPDATA\Microsoft\Windows\Temporary Internet Files" = "IE Temporary Files"
        "$env:LOCALAPPDATA\Microsoft\Windows\INetCache" = "IE Cache"
    }
    
    foreach ($folder in $tempFolders.Keys) {
        $totalCleaned += Remove-ItemsSafely $folder $tempFolders[$folder]
    }
}

# Clean System Cache and Logs
if ($CleanLogs -or $DeepClean) {
    if (Confirm-Action "📋 Clean System Logs and Cache?") {
        Write-Host "📋 Cleaning System Logs and Cache..." -ForegroundColor Green
        
        $logFolders = @{
            "$env:WINDIR\Logs" = "Windows Logs"
            "$env:WINDIR\System32\LogFiles" = "System Log Files"
            "$env:PROGRAMDATA\Microsoft\Windows\WER" = "Windows Error Reporting"
            "$env:LOCALAPPDATA\CrashDumps" = "Crash Dumps"
        }
        
        foreach ($folder in $logFolders.Keys) {
            $totalCleaned += Remove-ItemsSafely $folder $logFolders[$folder]
        }
        
        # Clean Windows Update Cache
        if (Confirm-Action "  Clean Windows Update Cache?") {
            try {
                Stop-Service -Name "wuauserv" -Force -ErrorAction SilentlyContinue
                $updateCache = "$env:WINDIR\SoftwareDistribution\Download"
                $totalCleaned += Remove-ItemsSafely $updateCache "Windows Update Cache"
                Start-Service -Name "wuauserv" -ErrorAction SilentlyContinue
            } catch {
                Write-Host "  ⚠️  Could not clean Windows Update cache" -ForegroundColor Yellow
            }
        }
    }
}

# Clean Browser Data
if ($IncludeBrowsers -or $DeepClean) {
    if (Confirm-Action "🌐 Clean Browser Cache and Data?") {
        Write-Host "🌐 Cleaning Browser Cache and Data..." -ForegroundColor Green
        
        # Chrome
        $chromePaths = @{
            "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Cache" = "Chrome Cache"
            "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\Code Cache" = "Chrome Code Cache"
            "$env:LOCALAPPDATA\Google\Chrome\User Data\Default\GPUCache" = "Chrome GPU Cache"
        }
        
        foreach ($path in $chromePaths.Keys) {
            if (Test-Path $path) {
                $totalCleaned += Remove-ItemsSafely $path $chromePaths[$path]
            }
        }
        
        # Firefox
        $firefoxProfile = Get-ChildItem -Path "$env:APPDATA\Mozilla\Firefox\Profiles" -Directory -ErrorAction SilentlyContinue | Select-Object -First 1
        if ($firefoxProfile) {
            $firefoxPaths = @{
                "$($firefoxProfile.FullName)\cache2" = "Firefox Cache"
                "$($firefoxProfile.FullName)\startupCache" = "Firefox Startup Cache"
                "$($firefoxProfile.FullName)\thumbnails" = "Firefox Thumbnails"
            }
            
            foreach ($path in $firefoxPaths.Keys) {
                $totalCleaned += Remove-ItemsSafely $path $firefoxPaths[$path]
            }
        }
        
        # Edge
        $edgePaths = @{
            "$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Cache" = "Edge Cache"
            "$env:LOCALAPPDATA\Microsoft\Edge\User Data\Default\Code Cache" = "Edge Code Cache"
        }
        
        foreach ($path in $edgePaths.Keys) {
            if (Test-Path $path) {
                $totalCleaned += Remove-ItemsSafely $path $edgePaths[$path]
            }
        }
    }
}

# Clean Thumbnail Cache
if (Confirm-Action "🖼️  Clean Thumbnail Cache?") {
    Write-Host "🖼️  Cleaning Thumbnail Cache..." -ForegroundColor Green
    
    $thumbnailPaths = @{
        "$env:LOCALAPPDATA\Microsoft\Windows\Explorer" = "Windows Thumbnail Cache"
        "$env:LOCALAPPDATA\IconCache.db" = "Icon Cache"
    }
    
    foreach ($path in $thumbnailPaths.Keys) {
        if (Test-Path $path) {
            if ($path.EndsWith(".db")) {
                try {
                    Remove-Item -Path $path -Force -ErrorAction SilentlyContinue
                    Write-Host "  ✓ $($thumbnailPaths[$path]): Cleaned" -ForegroundColor Green
                } catch {
                    Write-Host "  ⚠️  Could not clean $($thumbnailPaths[$path])" -ForegroundColor Yellow
                }
            } else {
                $totalCleaned += Remove-ItemsSafely $path $thumbnailPaths[$path]
            }
        }
    }
}

# Empty Recycle Bin
if ($EmptyRecycleBin -or $DeepClean) {
    if (Confirm-Action "🗑️  Empty Recycle Bin?") {
        Write-Host "🗑️  Emptying Recycle Bin..." -ForegroundColor Green
        
        try {
            # Get Recycle Bin size before emptying
            $recycleBin = Get-ChildItem -Path "C:\`$Recycle.Bin" -Recurse -Force -ErrorAction SilentlyContinue
            $recycleBinSize = ($recycleBin | Measure-Object -Property Length -Sum).Sum / 1MB
            
            # Empty Recycle Bin
            Clear-RecycleBin -Force -ErrorAction SilentlyContinue
            
            if ($recycleBinSize -gt 0) {
                Write-Host "  ✓ Recycle Bin: Cleaned $([math]::Round($recycleBinSize, 2)) MB" -ForegroundColor Green
                $totalCleaned += $recycleBinSize
            }
        } catch {
            Write-Host "  ⚠️  Could not empty Recycle Bin: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

# Clean System File Cache (Deep Clean)
if ($DeepClean) {
    if (Confirm-Action "🔧 Perform Deep System Clean (may take longer)?") {
        Write-Host "🔧 Performing Deep System Clean..." -ForegroundColor Green
        
        # Run Disk Cleanup utility
        try {
            Write-Host "  Running Windows Disk Cleanup..." -ForegroundColor Gray
            Start-Process -FilePath "cleanmgr.exe" -ArgumentList "/sagerun:1" -Wait -WindowStyle Hidden
            Write-Host "  ✓ Windows Disk Cleanup completed" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️  Could not run Windows Disk Cleanup" -ForegroundColor Yellow
        }
        
        # Clear DNS Cache
        try {
            ipconfig /flushdns | Out-Null
            Write-Host "  ✓ DNS Cache cleared" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️  Could not clear DNS cache" -ForegroundColor Yellow
        }
        
        # Clear Font Cache
        try {
            Stop-Service -Name "FontCache" -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$env:WINDIR\System32\FNTCACHE.DAT" -Force -ErrorAction SilentlyContinue
            Start-Service -Name "FontCache" -ErrorAction SilentlyContinue
            Write-Host "  ✓ Font Cache cleared" -ForegroundColor Green
        } catch {
            Write-Host "  ⚠️  Could not clear font cache" -ForegroundColor Yellow
        }
    }
}

# Clean Memory Dumps
if ($DeepClean) {
    if (Confirm-Action "💾 Clean Memory Dumps?") {
        Write-Host "💾 Cleaning Memory Dumps..." -ForegroundColor Green
        
        $dumpPaths = @{
            "$env:WINDIR\MEMORY.DMP" = "System Memory Dump"
            "$env:WINDIR\Minidump" = "Mini Dumps"
        }
        
        foreach ($path in $dumpPaths.Keys) {
            if (Test-Path $path) {
                if ($path.EndsWith(".DMP")) {
                    try {
                        $size = (Get-Item $path).Length / 1MB
                        Remove-Item -Path $path -Force -ErrorAction SilentlyContinue
                        Write-Host "  ✓ $($dumpPaths[$path]): Cleaned $([math]::Round($size, 2)) MB" -ForegroundColor Green
                        $totalCleaned += $size
                    } catch {
                        Write-Host "  ⚠️  Could not clean $($dumpPaths[$path])" -ForegroundColor Yellow
                    }
                } else {
                    $totalCleaned += Remove-ItemsSafely $path $dumpPaths[$path]
                }
            }
        }
    }
}

# Display final results
Write-Host "`n📊 Cleanup Results" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan

try {
    $diskAfter = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq $TargetDrive }
    $freeSpaceAfter = [math]::Round($diskAfter.FreeSpace / 1GB, 2)
    
    Write-Host "Total Space Cleaned: $([math]::Round($totalCleaned, 2)) MB" -ForegroundColor Green
    Write-Host "Free Space After Cleanup: $freeSpaceAfter GB" -ForegroundColor Green
} catch {
    Write-Host "Total Space Cleaned: $([math]::Round($totalCleaned, 2)) MB" -ForegroundColor Green
}

Write-Host "`n💡 Additional Recommendations:" -ForegroundColor Yellow
Write-Host "• Use Storage Sense to automatically clean files" -ForegroundColor Gray
Write-Host "• Uninstall unused programs from Control Panel" -ForegroundColor Gray
Write-Host "• Move large files to external storage" -ForegroundColor Gray
Write-Host "• Consider using cloud storage for documents" -ForegroundColor Gray
Write-Host "• Run this cleanup monthly for best results" -ForegroundColor Gray

Write-Host "`n🏁 Disk Cleanup Complete!" -ForegroundColor Cyan
