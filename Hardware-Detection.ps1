# Hardware Detection and Optimization Suggestions
# Detects your hardware and suggests specific optimizations

Write-Host "🔍 Detecting Your Hardware Configuration..." -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan

# Storage Detection
Write-Host "`n💾 Storage Analysis:" -ForegroundColor Green
$drives = Get-WmiObject -Class Win32_DiskDrive
foreach ($drive in $drives) {
    $isSSD = $false
    try {
        $physicalDisk = Get-PhysicalDisk | Where-Object { $_.DeviceID -eq $drive.Index } -ErrorAction SilentlyContinue
        if ($physicalDisk) {
            $isSSD = ($physicalDisk.MediaType -eq "SSD") -or ($physicalDisk.BusType -eq "NVMe")
        }
        if (-not $isSSD) {
            $isSSD = ($drive.Model -like "*SSD*") -or ($drive.Model -like "*NVMe*")
        }
    } catch {
        $isSSD = $drive.Model -like "*SSD*"
    }
    
    $type = if ($isSSD) { "SSD" } else { "HDD" }
    $size = [math]::Round($drive.Size / 1GB, 0)
    Write-Host "  📀 $($drive.Model) - $size GB ($type)"
    
    if ($isSSD) {
        Write-Host "    🔧 SSD Optimizations Available:" -ForegroundColor Yellow
        Write-Host "      • Disable defragmentation (reduces wear)" -ForegroundColor Gray
        Write-Host "      • Enable TRIM support (maintains performance)" -ForegroundColor Gray
        Write-Host "      • Disable Last Access Time updates (reduces writes)" -ForegroundColor Gray
        Write-Host "      • Optimize prefetch settings for SSD" -ForegroundColor Gray
        Write-Host "      • Disable hibernation (saves space and writes)" -ForegroundColor Gray
    } else {
        Write-Host "    🔧 HDD Optimizations Available:" -ForegroundColor Yellow
        Write-Host "      • Enable scheduled defragmentation" -ForegroundColor Gray
        Write-Host "      • Optimize prefetch for mechanical drives" -ForegroundColor Gray
        Write-Host "      • Enable SuperFetch for better caching" -ForegroundColor Gray
    }
}

# CPU Detection
Write-Host "`n🖥️  CPU Analysis:" -ForegroundColor Green
$cpu = Get-WmiObject -Class Win32_Processor | Select-Object -First 1
Write-Host "  🔥 $($cpu.Name)"
Write-Host "  📊 Cores: $($cpu.NumberOfCores) | Logical: $($cpu.NumberOfLogicalProcessors)"

$isIntel = $cpu.Manufacturer -like "*Intel*"
$isAMD = $cpu.Manufacturer -like "*AMD*"
$hasHT = $cpu.NumberOfLogicalProcessors -gt $cpu.NumberOfCores

Write-Host "`n    🔧 CPU-Specific Optimizations Available:" -ForegroundColor Yellow
if ($isIntel) {
    Write-Host "      • Enable Intel Turbo Boost optimization" -ForegroundColor Gray
    Write-Host "      • Optimize Intel SpeedStep settings" -ForegroundColor Gray
}
if ($isAMD) {
    Write-Host "      • Enable AMD Cool'n'Quiet optimization" -ForegroundColor Gray
    Write-Host "      • Optimize AMD performance scaling" -ForegroundColor Gray
}
if ($hasHT) {
    Write-Host "      • Optimize Hyper-Threading thread scheduling" -ForegroundColor Gray
}
if ($cpu.NumberOfCores -gt 4) {
    Write-Host "      • Multi-core processor optimizations" -ForegroundColor Gray
    Write-Host "      • Enable all cores for boot process" -ForegroundColor Gray
}

# RAM Detection
Write-Host "`n💿 RAM Analysis:" -ForegroundColor Green
$ram = Get-WmiObject -Class Win32_PhysicalMemory
$totalRAM = [math]::Round(($ram | Measure-Object -Property Capacity -Sum).Sum / 1GB, 2)
$ramSpeed = ($ram | Select-Object -First 1).Speed
$ramModules = $ram.Count

Write-Host "  🚀 Total: $totalRAM GB ($ramModules modules) - $ramSpeed MHz"

Write-Host "`n    🔧 RAM-Specific Optimizations Available:" -ForegroundColor Yellow
if ($totalRAM -ge 16) {
    Write-Host "      • High RAM system: Disable paging executive" -ForegroundColor Gray
    Write-Host "      • Increase system cache size" -ForegroundColor Gray
    Write-Host "      • Reduce page file size (plenty of RAM available)" -ForegroundColor Gray
} elseif ($totalRAM -lt 8) {
    Write-Host "      • Low RAM system: Optimize memory management" -ForegroundColor Gray
    Write-Host "      • Increase page file size for stability" -ForegroundColor Gray
    Write-Host "      • Disable memory-intensive visual effects" -ForegroundColor Gray
} else {
    Write-Host "      • Balanced RAM system: Standard optimizations" -ForegroundColor Gray
}

if ($ramSpeed -ge 3000) {
    Write-Host "      • High-speed RAM: Optimize memory timings" -ForegroundColor Gray
}

# GPU Detection
Write-Host "`n🎮 GPU Analysis:" -ForegroundColor Green
$gpus = Get-WmiObject -Class Win32_VideoController | Where-Object { $_.Name -notlike "*Basic*" -and $_.Name -notlike "*VGA*" }
foreach ($gpu in $gpus) {
    $vram = if ($gpu.AdapterRAM) { [math]::Round($gpu.AdapterRAM / 1MB, 0) } else { "Unknown" }
    Write-Host "  🎯 $($gpu.Name) - $vram MB VRAM"
    
    Write-Host "`n    🔧 GPU-Specific Optimizations Available:" -ForegroundColor Yellow
    if ($gpu.Name -like "*NVIDIA*" -or $gpu.Name -like "*GeForce*") {
        Write-Host "      • NVIDIA GPU: Optimize driver timeout settings" -ForegroundColor Gray
        Write-Host "      • Enable NVIDIA hardware acceleration" -ForegroundColor Gray
    }
    if ($gpu.Name -like "*AMD*" -or $gpu.Name -like "*Radeon*") {
        Write-Host "      • AMD GPU: Optimize driver settings" -ForegroundColor Gray
        Write-Host "      • Enable AMD hardware acceleration" -ForegroundColor Gray
    }
    if ($gpu.Name -like "*Intel*") {
        Write-Host "      • Intel GPU: Optimize integrated graphics settings" -ForegroundColor Gray
        Write-Host "      • Balance performance vs. power consumption" -ForegroundColor Gray
    }
}

# Network Optimization Suggestions
Write-Host "`n🌐 Network Optimizations Available:" -ForegroundColor Green
Write-Host "    🔧 Modern Hardware Network Optimizations:" -ForegroundColor Yellow
Write-Host "      • Enable TCP window auto-tuning" -ForegroundColor Gray
Write-Host "      • Optimize receive side scaling (RSS)" -ForegroundColor Gray
Write-Host "      • Enable compound TCP for high-speed connections" -ForegroundColor Gray
Write-Host "      • Optimize network adapter settings" -ForegroundColor Gray

# Advanced Suggestions
Write-Host "`n⚡ Advanced Configuration-Based Optimizations:" -ForegroundColor Green
Write-Host "    🔧 File System Optimizations:" -ForegroundColor Yellow
Write-Host "      • Optimize NTFS for your storage type" -ForegroundColor Gray
Write-Host "      • Configure optimal allocation unit size" -ForegroundColor Gray
Write-Host "      • Disable unnecessary file system features" -ForegroundColor Gray

Write-Host "`n    🔧 Windows Feature Optimizations:" -ForegroundColor Yellow
Write-Host "      • Disable features incompatible with your hardware" -ForegroundColor Gray
Write-Host "      • Remove unused Windows components" -ForegroundColor Gray
Write-Host "      • Optimize Windows services for your configuration" -ForegroundColor Gray

Write-Host "`n🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "==============" -ForegroundColor Cyan
Write-Host "To apply these optimizations, run:" -ForegroundColor White
Write-Host "  .\Advanced-Hardware-Optimizer.ps1 -ApplyAll" -ForegroundColor Yellow
Write-Host ""
Write-Host "Or apply specific optimizations:" -ForegroundColor White
Write-Host "  .\Advanced-Hardware-Optimizer.ps1 -SSDOptimizations" -ForegroundColor Yellow
Write-Host "  .\Advanced-Hardware-Optimizer.ps1 -CPUOptimizations" -ForegroundColor Yellow
Write-Host "  .\Advanced-Hardware-Optimizer.ps1 -RAMOptimizations" -ForegroundColor Yellow
Write-Host "  .\Advanced-Hardware-Optimizer.ps1 -GPUOptimizations" -ForegroundColor Yellow
