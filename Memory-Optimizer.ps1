# Memory Optimizer Script
# Optimize RAM usage and clear memory caches

param(
    [switch]$Interactive,
    [switch]$ClearCaches,
    [switch]$OptimizeServices,
    [switch]$ConfigurePageFile,
    [switch]$ShowMemoryInfo,
    [int]$MemoryThreshold = 80
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Administrator privileges required for full memory optimization functionality."
    exit 1
}

Write-Host "💾 Memory Optimizer" -ForegroundColor Cyan
Write-Host "==================" -ForegroundColor Cyan
Write-Host ""

function Get-MemoryInfo {
    $os = Get-WmiObject -Class Win32_OperatingSystem
    $computer = Get-WmiObject -Class Win32_ComputerSystem
    
    # Fix: Use consistent units - both in KB, convert to GB
    $totalRAM = [math]::Round($os.TotalVisibleMemorySize / 1024 / 1024, 2)  # KB to GB
    $availableRAM = [math]::Round($os.FreePhysicalMemory / 1024 / 1024, 2)  # KB to GB
    $usedRAM = $totalRAM - $availableRAM
    $usagePercent = [math]::Round(($usedRAM / $totalRAM) * 100, 2)
    
    return @{
        TotalRAM = $totalRAM
        UsedRAM = $usedRAM
        AvailableRAM = $availableRAM
        UsagePercent = $usagePercent
    }
}

function Show-MemoryInfo {
    param([hashtable]$MemInfo)
    
    Write-Host "📊 Current Memory Status:" -ForegroundColor Green
    Write-Host "=========================" -ForegroundColor Green
    Write-Host "Total RAM: $($MemInfo.TotalRAM) GB"
    Write-Host "Used RAM: $($MemInfo.UsedRAM) GB ($($MemInfo.UsagePercent)%)"
    Write-Host "Available RAM: $($MemInfo.AvailableRAM) GB"
    
    if ($MemInfo.UsagePercent -gt 90) {
        Write-Host "🔴 CRITICAL: Very high memory usage!" -ForegroundColor Red
    } elseif ($MemInfo.UsagePercent -gt 80) {
        Write-Host "🟡 WARNING: High memory usage" -ForegroundColor Yellow
    } elseif ($MemInfo.UsagePercent -gt 70) {
        Write-Host "🟠 CAUTION: Moderate memory usage" -ForegroundColor DarkYellow
    } else {
        Write-Host "🟢 GOOD: Normal memory usage" -ForegroundColor Green
    }
    Write-Host ""
}

function Get-TopMemoryProcesses {
    param([int]$Count = 10)
    
    Write-Host "🔥 Top Memory Consuming Processes:" -ForegroundColor Green
    $processes = Get-Process | Sort-Object WorkingSet -Descending | Select-Object -First $Count
    
    $processes | Select-Object Name, 
        @{Name="Memory(MB)"; Expression={[math]::Round($_.WorkingSet / 1MB, 2)}},
        @{Name="CPU(s)"; Expression={[math]::Round($_.CPU, 2)}},
        Id | Format-Table -AutoSize
    
    return $processes
}

function Clear-MemoryCaches {
    Write-Host "🧹 Clearing Memory Caches..." -ForegroundColor Green
    
    try {
        # Clear DNS cache
        ipconfig /flushdns | Out-Null
        Write-Host "  ✓ DNS cache cleared" -ForegroundColor Gray
        
        # Clear ARP cache
        arp -d * 2>$null
        Write-Host "  ✓ ARP cache cleared" -ForegroundColor Gray
        
        # Clear NetBios cache
        nbtstat -R 2>$null
        nbtstat -RR 2>$null
        Write-Host "  ✓ NetBios cache cleared" -ForegroundColor Gray
        
        # Trim working sets (force garbage collection)
        [GC]::Collect()
        [GC]::WaitForPendingFinalizers()
        [GC]::Collect()
        Write-Host "  ✓ .NET garbage collection completed" -ForegroundColor Gray
        
        # Clear font cache
        Stop-Service -Name "FontCache" -Force -ErrorAction SilentlyContinue
        Remove-Item -Path "$env:WINDIR\System32\FNTCACHE.DAT" -Force -ErrorAction SilentlyContinue
        Start-Service -Name "FontCache" -ErrorAction SilentlyContinue
        Write-Host "  ✓ Font cache cleared" -ForegroundColor Gray
        
        Write-Host "✅ Memory caches cleared successfully" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Error clearing caches: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Optimize-MemoryServices {
    Write-Host "⚙️  Optimizing Memory-Related Services..." -ForegroundColor Green
    
    # Services that can be optimized for memory
    $memoryServices = @{
        "SysMain" = @{
            Name = "Superfetch/SysMain"
            Action = "Disable"
            Reason = "Can consume significant RAM on systems with limited memory"
        }
        "Themes" = @{
            Name = "Themes Service"
            Action = "Manual"
            Reason = "Only needed when changing themes"
        }
        "TabletInputService" = @{
            Name = "Tablet PC Input Service"
            Action = "Disable"
            Reason = "Not needed on non-tablet devices"
        }
        "WSearch" = @{
            Name = "Windows Search"
            Action = "Manual"
            Reason = "Can use significant memory for indexing"
        }
    }
    
    foreach ($serviceName in $memoryServices.Keys) {
        try {
            $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
            if ($service) {
                $serviceInfo = $memoryServices[$serviceName]
                
                switch ($serviceInfo.Action) {
                    "Disable" {
                        if ($service.Status -eq "Running") {
                            Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
                        }
                        Set-Service -Name $serviceName -StartupType Disabled -ErrorAction SilentlyContinue
                        Write-Host "  ✓ Disabled: $($serviceInfo.Name)" -ForegroundColor Gray
                    }
                    "Manual" {
                        Set-Service -Name $serviceName -StartupType Manual -ErrorAction SilentlyContinue
                        Write-Host "  ✓ Set to Manual: $($serviceInfo.Name)" -ForegroundColor Gray
                    }
                }
            }
        } catch {
            Write-Host "  ⚠️  Could not optimize service: $serviceName" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ Memory services optimized" -ForegroundColor Green
}

function Configure-PageFile {
    Write-Host "💿 Configuring Virtual Memory (Page File)..." -ForegroundColor Green
    
    try {
        # Get total physical memory
        $totalRAM = (Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB
        
        # Calculate optimal page file size
        # Initial size: 1.5x RAM, Maximum size: 3x RAM (capped at reasonable limits)
        $initialSizeMB = [math]::Min([math]::Round($totalRAM * 1.5 * 1024), 16384)  # Max 16GB initial
        $maximumSizeMB = [math]::Min([math]::Round($totalRAM * 3 * 1024), 32768)    # Max 32GB maximum
        
        Write-Host "  Current RAM: $([math]::Round($totalRAM, 2)) GB" -ForegroundColor Gray
        Write-Host "  Recommended Initial Page File: $([math]::Round($initialSizeMB / 1024, 2)) GB" -ForegroundColor Gray
        Write-Host "  Recommended Maximum Page File: $([math]::Round($maximumSizeMB / 1024, 2)) GB" -ForegroundColor Gray
        
        # Disable automatic page file management
        $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges
        $cs.AutomaticManagedPagefile = $false
        $cs.Put() | Out-Null
        
        # Remove existing page files
        $existingPageFiles = Get-WmiObject -Class Win32_PageFileSetting
        if ($existingPageFiles) {
            $existingPageFiles | ForEach-Object { $_.Delete() }
        }
        
        # Create new page file on C: drive
        $pageFile = Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{
            name = "C:\pagefile.sys"
            InitialSize = $initialSizeMB
            MaximumSize = $maximumSizeMB
        }
        
        Write-Host "✅ Page file configured successfully" -ForegroundColor Green
        Write-Host "  Location: C:\pagefile.sys" -ForegroundColor Gray
        Write-Host "  Initial Size: $initialSizeMB MB" -ForegroundColor Gray
        Write-Host "  Maximum Size: $maximumSizeMB MB" -ForegroundColor Gray
        
    } catch {
        Write-Host "❌ Error configuring page file: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Optimize-MemorySettings {
    Write-Host "🔧 Optimizing Memory Settings..." -ForegroundColor Green
    
    try {
        # Optimize memory management
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "ClearPageFileAtShutdown" -Value 0 -Force
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "DisablePagingExecutive" -Value 1 -Force
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management" -Name "LargeSystemCache" -Value 0 -Force
        
        # Optimize file system cache
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "NtfsDisableLastAccessUpdate" -Value 1 -Force
        
        # Optimize prefetcher
        Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" -Name "EnablePrefetcher" -Value 3 -Force
        
        Write-Host "✅ Memory settings optimized" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Error optimizing memory settings: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Close-MemoryHungryProcesses {
    param([int]$Threshold = 500) # MB threshold
    
    Write-Host "🔄 Identifying Memory-Hungry Processes..." -ForegroundColor Green
    
    $hungryProcesses = Get-Process | Where-Object { 
        $_.WorkingSet -gt ($Threshold * 1MB) -and 
        $_.ProcessName -notin @("System", "dwm", "winlogon", "csrss", "smss", "explorer") 
    } | Sort-Object WorkingSet -Descending
    
    if ($hungryProcesses.Count -gt 0) {
        Write-Host "Found $($hungryProcesses.Count) processes using more than $Threshold MB:" -ForegroundColor Yellow
        
        $hungryProcesses | Select-Object -First 10 | ForEach-Object {
            $memoryMB = [math]::Round($_.WorkingSet / 1MB, 2)
            Write-Host "  • $($_.Name): $memoryMB MB (PID: $($_.Id))" -ForegroundColor Gray
        }
        
        if ($Interactive) {
            $response = Read-Host "`nWould you like to see options to close some processes? (y/n)"
            if ($response -eq 'y' -or $response -eq 'Y') {
                foreach ($process in $hungryProcesses | Select-Object -First 5) {
                    $memoryMB = [math]::Round($process.WorkingSet / 1MB, 2)
                    $closeProcess = Read-Host "Close $($process.Name) ($memoryMB MB)? (y/n)"
                    if ($closeProcess -eq 'y' -or $closeProcess -eq 'Y') {
                        try {
                            Stop-Process -Id $process.Id -Force
                            Write-Host "  ✓ Closed: $($process.Name)" -ForegroundColor Green
                        } catch {
                            Write-Host "  ❌ Could not close: $($process.Name)" -ForegroundColor Red
                        }
                    }
                }
            }
        }
    } else {
        Write-Host "✅ No excessively memory-hungry processes found" -ForegroundColor Green
    }
}

# Main execution
$memoryBefore = Get-MemoryInfo

if ($ShowMemoryInfo -or !($ClearCaches -or $OptimizeServices -or $ConfigurePageFile)) {
    Show-MemoryInfo $memoryBefore
    Get-TopMemoryProcesses
}

if ($Interactive) {
    while ($true) {
        Write-Host "`n🔧 Memory Optimization Options:" -ForegroundColor Cyan
        Write-Host "1. Clear memory caches"
        Write-Host "2. Optimize memory-related services"
        Write-Host "3. Configure page file"
        Write-Host "4. Close memory-hungry processes"
        Write-Host "5. Apply all optimizations"
        Write-Host "6. Show current memory info"
        Write-Host "7. Exit"
        
        $choice = Read-Host "Select option (1-7)"
        
        switch ($choice) {
            "1" { Clear-MemoryCaches }
            "2" { Optimize-MemoryServices }
            "3" { Configure-PageFile }
            "4" { Close-MemoryHungryProcesses }
            "5" { 
                Clear-MemoryCaches
                Optimize-MemoryServices
                Configure-PageFile
                Optimize-MemorySettings
            }
            "6" { 
                $currentMem = Get-MemoryInfo
                Show-MemoryInfo $currentMem
                Get-TopMemoryProcesses
            }
            "7" { break }
            default { Write-Host "Invalid option. Please select 1-7." -ForegroundColor Red }
        }
    }
} else {
    # Run specified optimizations
    if ($ClearCaches) {
        Clear-MemoryCaches
    }
    
    if ($OptimizeServices) {
        Optimize-MemoryServices
    }
    
    if ($ConfigurePageFile) {
        Configure-PageFile
    }
    
    # If no specific options, run all optimizations
    if (!($ClearCaches -or $OptimizeServices -or $ConfigurePageFile)) {
        Clear-MemoryCaches
        Optimize-MemoryServices
        Configure-PageFile
        Optimize-MemorySettings
    }
}

# Show results
Write-Host "`n📊 Memory Optimization Results:" -ForegroundColor Cyan
Write-Host "===============================" -ForegroundColor Cyan

$memoryAfter = Get-MemoryInfo
Show-MemoryInfo $memoryAfter

$memoryFreed = $memoryAfter.AvailableRAM - $memoryBefore.AvailableRAM
if ($memoryFreed -gt 0) {
    Write-Host "💾 Memory Freed: $([math]::Round($memoryFreed, 2)) GB" -ForegroundColor Green
} else {
    Write-Host "📊 Memory usage may improve after restart" -ForegroundColor Yellow
}

Write-Host "`n💡 Additional Memory Tips:" -ForegroundColor Yellow
Write-Host "• Close unused browser tabs and applications"
Write-Host "• Disable visual effects for better performance"
Write-Host "• Consider adding more RAM if usage consistently exceeds 80%"
Write-Host "• Run this optimizer regularly for best results"
Write-Host "• Restart your computer to fully apply page file changes"

Write-Host "`n🏁 Memory Optimization Complete!" -ForegroundColor Cyan
