# PC Performance Optimization Master Script
# Comprehensive Windows performance optimization suite

param(
    [switch]$RunAll,
    [switch]$Interactive,
    [switch]$DiagnosticsOnly,
    [switch]$SafeMode,
    [switch]$CreateBackups,
    [string]$LogPath = "$env:USERPROFILE\Desktop\PC-Optimization-Log.txt"
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "🔒 Administrator Privileges Required" -ForegroundColor Red
    Write-Host "This script needs to run as Administrator for full functionality." -ForegroundColor Yellow
    Write-Host "Please restart PowerShell as Administrator and run the script again." -ForegroundColor Yellow
    
    $response = Read-Host "`nAttempt to restart as Administrator? (y/n)"
    if ($response -eq 'y' -or $response -eq 'Y') {
        Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    }
    exit 1
}

# Set execution policy for current session
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

Clear-Host

Write-Host "🚀 PC Performance Optimization Suite" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Cyan
Write-Host "A comprehensive toolkit to optimize your Windows PC" -ForegroundColor Gray
Write-Host ""

# Initialize log
$logContent = @()
$logContent += "PC Optimization Log - $(Get-Date)"
$logContent += "=" * 50

function Write-Log {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
    $logContent += "$(Get-Date -Format 'HH:mm:ss') - $Message"
}

function Show-Menu {
    Write-Host "📋 Optimization Options:" -ForegroundColor Green
    Write-Host "========================" -ForegroundColor Green
    Write-Host "1. 🔍 System Diagnostics (Analyze current performance)"
    Write-Host "2. ⚡ Quick Performance Boost (Safe optimizations)"
    Write-Host "3. 🧹 Disk Cleanup (Free up storage space)"
    Write-Host "4. 🚀 Startup Optimization (Improve boot time)"
    Write-Host "5. 💾 Memory Optimization (Optimize RAM usage)"
    Write-Host "6. 🔧 Advanced Optimizations (Registry & system tweaks)"
    Write-Host "7. 🏆 Complete Optimization (Run all optimizations)"
    Write-Host "8. 📊 View System Information"
    Write-Host "9. 🔄 Restore System (Create restore point)"
    Write-Host "0. ❌ Exit"
    Write-Host ""
}

function Show-SystemInfo {
    Write-Log "📊 System Information" "Green"
    Write-Log "=====================" "Green"
    
    $os = Get-WmiObject -Class Win32_OperatingSystem
    $computer = Get-WmiObject -Class Win32_ComputerSystem
    $processor = Get-WmiObject -Class Win32_Processor
    
    Write-Log "Computer: $($computer.Name)"
    Write-Log "OS: $($os.Caption) $($os.Version)"
    Write-Log "Processor: $($processor.Name)"
    Write-Log "Total RAM: $([math]::Round($computer.TotalPhysicalMemory / 1GB, 2)) GB"
    Write-Log "Architecture: $($os.OSArchitecture)"
    Write-Log ""
}

function Run-Diagnostics {
    Write-Log "🔍 Running System Diagnostics..." "Yellow"
    
    if (Test-Path ".\System-Diagnostics.ps1") {
        & ".\System-Diagnostics.ps1" -ExportReport -OutputPath "$env:USERPROFILE\Desktop\SystemDiagnostics-$(Get-Date -Format 'yyyy-MM-dd-HHmm').txt"
    } else {
        Write-Log "❌ System-Diagnostics.ps1 not found" "Red"
    }
}

function Run-QuickOptimization {
    Write-Log "⚡ Running Quick Performance Optimizations..." "Yellow"
    
    # Safe optimizations that won't break anything
    if (Test-Path ".\Performance-Optimizer.ps1") {
        & ".\Performance-Optimizer.ps1" -VisualEffects -PowerSettings -Telemetry -CreateRestorePoint
    }
    
    if (Test-Path ".\Memory-Optimizer.ps1") {
        & ".\Memory-Optimizer.ps1" -ClearCaches
    }
    
    Write-Log "✅ Quick optimization completed" "Green"
}

function Run-DiskCleanup {
    Write-Log "🧹 Running Disk Cleanup..." "Yellow"
    
    if (Test-Path ".\Disk-Cleanup.ps1") {
        & ".\Disk-Cleanup.ps1" -IncludeBrowsers -EmptyRecycleBin -CleanLogs
    } else {
        Write-Log "❌ Disk-Cleanup.ps1 not found" "Red"
    }
}

function Run-StartupOptimization {
    Write-Log "🚀 Running Startup Optimization..." "Yellow"
    
    if (Test-Path ".\Startup-Manager.ps1") {
        & ".\Startup-Manager.ps1" -CreateBackup -ShowAll
    } else {
        Write-Log "❌ Startup-Manager.ps1 not found" "Red"
    }
}

function Run-MemoryOptimization {
    Write-Log "💾 Running Memory Optimization..." "Yellow"
    
    if (Test-Path ".\Memory-Optimizer.ps1") {
        & ".\Memory-Optimizer.ps1" -ClearCaches -OptimizeServices -ConfigurePageFile
    } else {
        Write-Log "❌ Memory-Optimizer.ps1 not found" "Red"
    }
}

function Run-AdvancedOptimizations {
    Write-Log "🔧 Running Advanced Optimizations..." "Yellow"
    
    if (Test-Path ".\Performance-Optimizer.ps1") {
        & ".\Performance-Optimizer.ps1" -ApplyAll -CreateRestorePoint
    } else {
        Write-Log "❌ Performance-Optimizer.ps1 not found" "Red"
    }
}

function Run-CompleteOptimization {
    Write-Log "🏆 Running Complete System Optimization..." "Cyan"
    Write-Log "This will take several minutes..." "Yellow"
    
    # Create system restore point first
    Write-Log "📌 Creating System Restore Point..." "Yellow"
    try {
        Checkpoint-Computer -Description "Complete PC Optimization - $(Get-Date)" -RestorePointType "MODIFY_SETTINGS"
        Write-Log "✅ System Restore Point created" "Green"
    } catch {
        Write-Log "⚠️ Could not create restore point: $($_.Exception.Message)" "Yellow"
    }
    
    # Run all optimizations in order
    Run-Diagnostics
    Run-DiskCleanup
    Run-MemoryOptimization
    Run-StartupOptimization
    Run-AdvancedOptimizations
    
    Write-Log "🏆 Complete optimization finished!" "Green"
}

function Create-RestorePoint {
    Write-Log "🔄 Creating System Restore Point..." "Yellow"
    
    try {
        Checkpoint-Computer -Description "Manual Restore Point - $(Get-Date)" -RestorePointType "MODIFY_SETTINGS"
        Write-Log "✅ System Restore Point created successfully" "Green"
    } catch {
        Write-Log "❌ Failed to create restore point: $($_.Exception.Message)" "Red"
    }
}

function Save-Log {
    try {
        $logContent | Out-File -FilePath $LogPath -Encoding UTF8
        Write-Log "📄 Log saved to: $LogPath" "Green"
    } catch {
        Write-Log "❌ Failed to save log: $($_.Exception.Message)" "Red"
    }
}

# Check if running in Safe Mode
if (bcdedit /enum | Select-String "safeboot") {
    Write-Log "⚠️ Running in Safe Mode - Limited functionality available" "Yellow"
    $SafeMode = $true
}

# Main execution logic
if ($DiagnosticsOnly) {
    Run-Diagnostics
    Save-Log
    exit
}

if ($RunAll) {
    Run-CompleteOptimization
    Save-Log
    
    Write-Log "`n🎉 Optimization Complete!" "Cyan"
    Write-Log "Your PC should now run faster and more efficiently." "Green"
    Write-Log "It's recommended to restart your computer to apply all changes." "Yellow"
    
    $restart = Read-Host "`nWould you like to restart now? (y/n)"
    if ($restart -eq 'y' -or $restart -eq 'Y') {
        Write-Log "Restarting in 10 seconds..." "Yellow"
        Start-Sleep -Seconds 10
        Restart-Computer -Force
    }
    exit
}

# Interactive mode
if ($Interactive -or (!$RunAll -and !$DiagnosticsOnly)) {
    Show-SystemInfo
    
    while ($true) {
        Show-Menu
        $choice = Read-Host "Select an option (0-9)"
        
        switch ($choice) {
            "1" { 
                Run-Diagnostics 
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "2" { 
                Run-QuickOptimization
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "3" { 
                Run-DiskCleanup
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "4" { 
                Run-StartupOptimization
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "5" { 
                Run-MemoryOptimization
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "6" { 
                Run-AdvancedOptimizations
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "7" { 
                $confirm = Read-Host "This will run all optimizations. Continue? (y/n)"
                if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                    Run-CompleteOptimization
                    Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
                }
            }
            "8" { 
                Show-SystemInfo
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "9" { 
                Create-RestorePoint
                Write-Host "`nPress any key to continue..." -ForegroundColor Gray
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            "0" { 
                Write-Log "Exiting PC Optimization Suite..." "Cyan"
                break 
            }
            default { 
                Write-Log "Invalid option. Please select 0-9." "Red"
                Start-Sleep -Seconds 1
            }
        }
        
        Clear-Host
        Write-Host "🚀 PC Performance Optimization Suite" -ForegroundColor Cyan
        Write-Host "====================================" -ForegroundColor Cyan
        Write-Host ""
    }
}

# Save log before exit
Save-Log

Write-Host "`n💡 Final Recommendations:" -ForegroundColor Yellow
Write-Host "• Keep Windows and drivers updated"
Write-Host "• Run these optimizations monthly"
Write-Host "• Monitor system performance regularly"
Write-Host "• Consider hardware upgrades if issues persist"
Write-Host "• Always maintain system backups"

Write-Host "`nThank you for using PC Performance Optimization Suite! 🚀" -ForegroundColor Cyan
