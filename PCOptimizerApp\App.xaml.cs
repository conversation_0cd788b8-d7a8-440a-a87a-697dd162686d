using System.Windows;
using ModernWpf;
using PCOptimizerApp.Views;
using PCOptimizerApp.ViewModels;
using PCOptimizerApp.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Serilog;
using System.Security.Principal;

namespace PCOptimizerApp
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override void OnStartup(StartupEventArgs e)
        {
            // Check if running with administrator privileges
            CheckAdministratorPrivileges();

            // Configure Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.File("logs/pcoptimizer-.log", rollingInterval: RollingInterval.Day)
                .WriteTo.Console()
                .CreateLogger();

            // Configure Modern WPF theme
            ThemeManager.Current.ApplicationTheme = ApplicationTheme.Dark;
            ThemeManager.Current.AccentColor = System.Windows.Media.Color.FromRgb(0, 212, 255);

            // Configure dependency injection
            _host = Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Services
                    services.AddSingleton<IAdminPrivilegeService, AdminPrivilegeService>();
                    services.AddSingleton<ISystemInfoService, SystemInfoService>();
                    services.AddSingleton<IHardwareDetectionService, HardwareDetectionService>();
                    services.AddSingleton<IRegistryService, RegistryService>();
                    services.AddSingleton<IBackupService, BackupService>();
                    services.AddSingleton<IPerformanceMonitoringService, PerformanceMonitoringService>();
                    services.AddSingleton<IOptimizationService, OptimizationService>();
                    services.AddSingleton<IProgressTrackingService, ProgressTrackingService>();
                    services.AddSingleton<ISmartAnalysisService, SmartAnalysisService>();

                    // ViewModels
                    services.AddTransient<MainWindowViewModel>();
                    services.AddTransient<DashboardViewModel>();
                    services.AddTransient<SystemAnalysisViewModel>();
                    services.AddTransient<OptimizationViewModel>();
                    services.AddTransient<HardwareViewModel>();
                    services.AddTransient<SafetyViewModel>();

                    // Views
                    services.AddTransient<MainWindow>();
                })
                .Build();

            // Initialize service locator
            ServiceLocator.Initialize(_host.Services);

            // Start the main window
            var mainWindow = _host.Services.GetRequiredService<MainWindow>();
            mainWindow.Show();

            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            _host?.Dispose();
            Log.CloseAndFlush();
            base.OnExit(e);
        }

        private void CheckAdministratorPrivileges()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                bool isElevated = principal.IsInRole(WindowsBuiltInRole.Administrator);

                if (!isElevated)
                {
                    var result = MessageBox.Show(
                        "PC Optimizer Pro requires administrator privileges to function properly.\n\n" +
                        "Many optimization features will not work without elevated permissions:\n" +
                        "• Registry modifications\n" +
                        "• System service management\n" +
                        "• System restore point creation\n" +
                        "• Power plan modifications\n" +
                        "• System file cleanup\n\n" +
                        "Please restart the application as an administrator.\n\n" +
                        "Do you want to continue anyway? (Limited functionality)",
                        "Administrator Privileges Required",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.No)
                    {
                        this.Shutdown();
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "Failed to check administrator privileges");
            }
        }

        public static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
