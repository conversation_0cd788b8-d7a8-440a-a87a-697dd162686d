using System.ComponentModel;

namespace PCOptimizerApp.Models
{
    public class SystemInfo
    {
        public string? OperatingSystem { get; set; }
        public string? ProcessorName { get; set; }
        public int ProcessorCores { get; set; }
        public double ProcessorSpeedGHz { get; set; }
        public long TotalMemoryGB { get; set; }
        public long AvailableMemoryGB { get; set; }
        public double MemoryUsagePercentage { get; set; }
        public List<StorageDevice> StorageDevices { get; set; } = new();
        public GraphicsCard? GraphicsCard { get; set; }
        public double CpuTemperature { get; set; }
        public double GpuTemperature { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class StorageDevice
    {
        public string? Name { get; set; }
        public string? Model { get; set; }
        public long TotalSizeGB { get; set; }
        public long FreeSpaceGB { get; set; }
        public double UsagePercentage => TotalSizeGB > 0 ? (double)(TotalSizeGB - FreeSpaceGB) / TotalSizeGB * 100 : 0;
        public StorageType Type { get; set; }
        public string? Health { get; set; }
        public bool TrimEnabled { get; set; }
    }

    public class GraphicsCard
    {
        public string? Name { get; set; }
        public string? DriverVersion { get; set; }
        public long VRamMB { get; set; }
        public bool DriverUpToDate { get; set; }
    }

    public enum StorageType
    {
        HDD,
        SSD,
        NVMe,
        Unknown
    }

    public class PerformanceMetrics
    {
        public double CpuUsagePercentage { get; set; }
        public double MemoryUsagePercentage { get; set; }
        public double DiskUsagePercentage { get; set; }
        public double DiskSpeedMBps { get; set; } // Replaced NetworkUsageKbps with disk speed
        public double CpuTemperature { get; set; }
        public double GpuTemperature { get; set; }
        public DateTime Timestamp { get; set; }
    }

    public class SystemHealthScore
    {
        public int OverallScore { get; set; }
        public SystemHealthStatus Status { get; set; }
        public List<HealthFactor> Factors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public class HealthFactor
    {
        public string? Name { get; set; }
        public int Score { get; set; }
        public string? Description { get; set; }
        public HealthImpact Impact { get; set; }
    }

    public enum SystemHealthStatus
    {
        Excellent,
        Good,
        Fair,
        Poor,
        Critical
    }

    public enum HealthImpact
    {
        Low,
        Medium,
        High,
        Critical
    }

    public class OptimizationItem
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? Category { get; set; }
        public OptimizationSafety Safety { get; set; }
        public OptimizationImpact Impact { get; set; }
        public bool IsApplicable { get; set; }
        public bool IsApplied { get; set; }
        public bool IsReversible { get; set; }
        public string? ExpectedImprovement { get; set; }
        public List<string> Requirements { get; set; } = new();
        public DateTime? AppliedDate { get; set; }
    }

    public enum OptimizationSafety
    {
        Safe = 5,
        MostlySafe = 4,
        Moderate = 3,
        Risky = 2,
        Dangerous = 1
    }

    public enum OptimizationImpact
    {
        Low,
        Medium,
        High
    }

    public class ProcessInfo
    {
        public string? Name { get; set; }
        public int ProcessId { get; set; }
        public double CpuUsagePercentage { get; set; }
        public long MemoryUsageMB { get; set; }
        public string? Description { get; set; }
    }

    public class StartupProgram
    {
        public string? Name { get; set; }
        public string? Publisher { get; set; }
        public string? Command { get; set; }
        public StartupLocation Location { get; set; }
        public StartupImpact Impact { get; set; }
        public bool IsEnabled { get; set; }
        public bool CanDisable { get; set; }
        public string? Description { get; set; }
    }

    public enum StartupLocation
    {
        Registry,
        StartupFolder,
        Services,
        TaskScheduler
    }

    public enum StartupImpact
    {
        Low,
        Medium,
        High
    }

    public class BackupInfo
    {
        public string? Id { get; set; }
        public string? Name { get; set; }
        public DateTime CreatedDate { get; set; }
        public BackupType Type { get; set; }
        public long SizeMB { get; set; }
        public string? Description { get; set; }
        public List<string> BackedUpItems { get; set; } = new();
    }

    public enum BackupType
    {
        SystemRestorePoint,
        RegistryBackup,
        SettingsBackup,
        FullSystemBackup
    }

    public class ProgressUpdateEventArgs : EventArgs
    {
        public string OperationId { get; set; } = string.Empty;
        public string OperationName { get; set; } = string.Empty;
        public int CurrentStep { get; set; }
        public int TotalSteps { get; set; }
        public string CurrentStepDescription { get; set; } = string.Empty;
        public string? Details { get; set; }
        public double ProgressPercentage => TotalSteps > 0 ? (double)CurrentStep / TotalSteps * 100 : 0;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public class OperationHistory
    {
        public string OperationId { get; set; } = string.Empty;
        public string OperationName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool Success { get; set; }
        public string? Result { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
        public List<OperationLogEntry> LogEntries { get; set; } = new();
    }

    public class OperationLogEntry
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string Level { get; set; } = string.Empty; // Info, Warning, Error, Debug
        public string Message { get; set; } = string.Empty;
        public string? ExceptionDetails { get; set; }
    }
}
