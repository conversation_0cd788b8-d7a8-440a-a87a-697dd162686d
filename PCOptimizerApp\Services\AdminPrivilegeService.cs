using System.Security.Principal;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service for checking and managing administrator privileges
    /// </summary>
    public interface IAdminPrivilegeService
    {
        bool IsRunningAsAdministrator { get; }
        bool CanPerformAdminOperations { get; }
        string GetPrivilegeStatusMessage();
        bool CheckPrivilegeForOperation(string operationName);
    }

    public class AdminPrivilegeService : IAdminPrivilegeService
    {
        public bool IsRunningAsAdministrator { get; private set; }
        public bool CanPerformAdminOperations => IsRunningAsAdministrator;

        public AdminPrivilegeService()
        {
            CheckAdminPrivileges();
        }

        private void CheckAdminPrivileges()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                IsRunningAsAdministrator = principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                IsRunningAsAdministrator = false;
            }
        }

        public string GetPrivilegeStatusMessage()
        {
            return IsRunningAsAdministrator 
                ? "Running with Administrator privileges - All features available"
                : "Running without Administrator privileges - Limited functionality";
        }

        public bool CheckPrivilegeForOperation(string operationName)
        {
            if (IsRunningAsAdministrator)
                return true;

            // Log warning about privilege requirement
            Serilog.Log.Warning("Operation '{OperationName}' requires administrator privileges but application is not elevated", operationName);
            return false;
        }
    }
}
