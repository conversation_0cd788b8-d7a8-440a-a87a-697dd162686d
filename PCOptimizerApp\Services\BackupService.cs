using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;

namespace PCOptimizerApp.Services
{
    public class BackupService : IBackupService
    {
        private readonly ILogger _logger = Log.ForContext<BackupService>();
        private readonly string _backupDirectory;
        private readonly IProgressTrackingService? _progressTrackingService;

        public BackupService(IProgressTrackingService? progressTrackingService = null)
        {
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PCOptimizer", "Backups");
            Directory.CreateDirectory(_backupDirectory);
            _progressTrackingService = progressTrackingService;
        }

        public async Task<bool> CreateSystemRestorePointAsync(string description)
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                _logger.Information("Creating system restore point: {Description}", description);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.StartOperationAsync(operationId, "Create System Restore Point", 3);
                    await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Initializing restore point creation...",
                        $"Creating restore point: {description}");
                }

                return await Task.Run(async () =>
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "powershell",
                            Arguments = $"-Command \"Checkpoint-Computer -Description '{description}' -RestorePointType 'MODIFY_SETTINGS'\"",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            Verb = "runas" // Requires admin privileges
                        }
                    };

                    if (_progressTrackingService != null)
                    {
                        await _progressTrackingService.UpdateProgressAsync(operationId, 2, "Creating restore point...",
                            "This may take several minutes depending on system size");
                    }

                    process.Start();
                    var output = process.StandardOutput.ReadToEnd();
                    var error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        _logger.Information("Successfully created system restore point: {Description}", description);

                        if (_progressTrackingService != null)
                        {
                            await _progressTrackingService.UpdateProgressAsync(operationId, 3, "Restore point created successfully",
                                $"System restore point '{description}' is ready");
                            await _progressTrackingService.CompleteOperationAsync(operationId, true,
                                $"System restore point '{description}' created successfully");
                        }

                        return true;
                    }
                    else
                    {
                        _logger.Warning("Failed to create system restore point. Error: {Error}", error);

                        if (_progressTrackingService != null)
                        {
                            await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                                $"Failed to create restore point: {error}");
                            await _progressTrackingService.CompleteOperationAsync(operationId, false,
                                $"Failed to create restore point: {error}");
                        }

                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating system restore point: {Description}", description);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                        "Exception during restore point creation", ex);
                    await _progressTrackingService.CompleteOperationAsync(operationId, false,
                        $"Exception: {ex.Message}");
                }

                return false;
            }
        }

        public async Task<bool> CreateRegistryBackupAsync(string registryPath)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"registry_backup_{timestamp}.reg";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                _logger.Information("Creating registry backup for: {RegistryPath}", registryPath);

                return await Task.Run(() =>
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "reg",
                            Arguments = $"export \"{registryPath}\" \"{backupFilePath}\" /y",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        }
                    };

                    process.Start();
                    process.WaitForExit();

                    if (process.ExitCode == 0 && File.Exists(backupFilePath))
                    {
                        _logger.Information("Successfully created registry backup: {BackupPath}", backupFilePath);
                        return true;
                    }
                    else
                    {
                        _logger.Warning("Failed to create registry backup for: {RegistryPath}", registryPath);
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating registry backup for: {RegistryPath}", registryPath);
                return false;
            }
        }

        public async Task<bool> CreateSettingsBackupAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"settings_backup_{timestamp}.json";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                _logger.Information("Creating settings backup");

                var settingsBackup = new
                {
                    Timestamp = DateTime.Now,
                    PowerPlan = await GetCurrentPowerPlanAsync(),
                    VisualEffects = await GetVisualEffectsSettingsAsync(),
                    StartupPrograms = await GetStartupProgramsStateAsync(),
                    SystemSettings = await GetSystemSettingsAsync()
                };

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(settingsBackup, Newtonsoft.Json.Formatting.Indented);
                await File.WriteAllTextAsync(backupFilePath, json);

                _logger.Information("Successfully created settings backup: {BackupPath}", backupFilePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating settings backup");
                return false;
            }
        }

        public async Task<List<BackupInfo>> GetAvailableBackupsAsync()
        {
            try
            {
                var backups = new List<BackupInfo>();

                // Get file-based backups
                var backupFiles = Directory.GetFiles(_backupDirectory, "*.*", SearchOption.TopDirectoryOnly);
                foreach (var file in backupFiles)
                {
                    var fileInfo = new FileInfo(file);
                    var backup = new BackupInfo
                    {
                        Id = Path.GetFileNameWithoutExtension(file),
                        Name = Path.GetFileName(file),
                        CreatedDate = fileInfo.CreationTime,
                        SizeMB = fileInfo.Length / (1024 * 1024),
                        Type = file.EndsWith(".reg") ? BackupType.RegistryBackup : BackupType.SettingsBackup,
                        Description = GetBackupDescription(file)
                    };
                    backups.Add(backup);
                }

                // Get system restore points
                var restorePoints = await GetSystemRestorePointsAsync();
                backups.AddRange(restorePoints);

                return backups.OrderByDescending(b => b.CreatedDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available backups");
                return new List<BackupInfo>();
            }
        }

        public async Task<bool> RestoreFromBackupAsync(string backupId)
        {
            try
            {
                _logger.Information("Restoring from backup: {BackupId}", backupId);

                var backups = await GetAvailableBackupsAsync();
                var backup = backups.FirstOrDefault(b => b.Id == backupId);

                if (backup == null)
                {
                    _logger.Warning("Backup not found: {BackupId}", backupId);
                    return false;
                }

                return backup.Type switch
                {
                    BackupType.SystemRestorePoint => await RestoreSystemRestorePointAsync(backupId),
                    BackupType.RegistryBackup => await RestoreRegistryBackupAsync(backupId),
                    BackupType.SettingsBackup => await RestoreSettingsBackupAsync(backupId),
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring from backup: {BackupId}", backupId);
                return false;
            }
        }

        public async Task<bool> DeleteBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, backupId + ".*");
                var matchingFiles = Directory.GetFiles(_backupDirectory, $"{backupId}.*");

                foreach (var file in matchingFiles)
                {
                    File.Delete(file);
                    _logger.Information("Deleted backup file: {File}", file);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error deleting backup: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<string?> GetCurrentPowerPlanAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powercfg",
                        Arguments = "/getactivescheme",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return output.Trim();
            }
            catch
            {
                return null;
            }
        }

        private async Task<object?> GetVisualEffectsSettingsAsync()
        {
            try
            {
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects");
                return key?.GetValue("VisualFXSetting");
            }
            catch
            {
                return null;
            }
        }

        private async Task<List<string>> GetStartupProgramsStateAsync()
        {
            try
            {
                var startupPrograms = new List<string>();

                using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run");
                if (key != null)
                {
                    foreach (var valueName in key.GetValueNames())
                    {
                        startupPrograms.Add($"{valueName}={key.GetValue(valueName)}");
                    }
                }

                return startupPrograms;
            }
            catch
            {
                return new List<string>();
            }
        }

        private async Task<Dictionary<string, object?>> GetSystemSettingsAsync()
        {
            try
            {
                var settings = new Dictionary<string, object?>();

                // Add various system settings that we track
                settings["WindowsVersion"] = Environment.OSVersion.ToString();
                settings["MachineName"] = Environment.MachineName;
                settings["UserName"] = Environment.UserName;

                return settings;
            }
            catch
            {
                return new Dictionary<string, object?>();
            }
        }

        public async Task<List<BackupInfo>> GetSystemRestorePointsAsync()
        {
            try
            {
                var restorePoints = new List<BackupInfo>();

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powershell",
                        Arguments = "-Command \"Get-ComputerRestorePoint | ConvertTo-Json\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
                {
                    try
                    {
                        dynamic? restorePointData = Newtonsoft.Json.JsonConvert.DeserializeObject(output);
                        if (restorePointData != null)
                        {
                            // Parse restore point data and create BackupInfo objects
                            // This would require more detailed implementation based on the actual JSON structure
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error parsing restore point data");
                    }
                }

                return restorePoints;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting system restore points");
                return new List<BackupInfo>();
            }
        }

        private async Task<bool> RestoreSystemRestorePointAsync(string backupId)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "rstrui.exe",
                        Arguments = $"/offline:C:\\Windows /RestorePoint:{backupId}",
                        UseShellExecute = true,
                        Verb = "runas"
                    }
                };

                process.Start();
                return true; // System restore is interactive, so we can't wait for completion
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring system restore point: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<bool> RestoreRegistryBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.reg");
                if (!File.Exists(backupFile))
                {
                    _logger.Warning("Registry backup file not found: {BackupFile}", backupFile);
                    return false;
                }

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "reg",
                        Arguments = $"import \"{backupFile}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        Verb = "runas"
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                return process.ExitCode == 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring registry backup: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<bool> RestoreSettingsBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.json");
                if (!File.Exists(backupFile))
                {
                    _logger.Warning("Settings backup file not found: {BackupFile}", backupFile);
                    return false;
                }

                var json = await File.ReadAllTextAsync(backupFile);
                dynamic? settingsBackup = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                if (settingsBackup != null)
                {
                    // Restore individual settings
                    // This would require implementing restoration for each setting type
                    // For now, we'll just log that we would restore them
                    _logger.Information("Would restore settings from backup: {BackupId}", backupId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring settings backup: {BackupId}", backupId);
                return false;
            }
        }

        private string GetBackupDescription(string filePath)
        {
            var fileName = Path.GetFileName(filePath);
            var extension = Path.GetExtension(filePath).ToLower();

            return extension switch
            {
                ".reg" => "Registry backup",
                ".json" => "Settings backup",
                _ => "System backup"
            };
        }

        #region Enhanced Backup and Rollback Methods

        public async Task<string> CreateOptimizationBackupAsync(string optimizationName, List<string> affectedRegistryKeys)
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupId = $"optimization_{optimizationName}_{timestamp}";
                var backupFileName = $"{backupId}.json";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                _logger.Information("Creating optimization backup for: {OptimizationName}", optimizationName);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.StartOperationAsync(operationId, "Create Optimization Backup", affectedRegistryKeys.Count + 2);
                    await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Initializing backup...",
                        $"Creating backup for optimization: {optimizationName}");
                }

                var backupData = new
                {
                    OptimizationName = optimizationName,
                    Timestamp = DateTime.Now,
                    BackupId = backupId,
                    RegistryBackups = new List<object>(),
                    SystemSettings = new Dictionary<string, object>()
                };

                var registryBackups = new List<object>();
                var currentStep = 2;

                // Backup each affected registry key
                foreach (var registryKey in affectedRegistryKeys)
                {
                    try
                    {
                        if (_progressTrackingService != null)
                        {
                            await _progressTrackingService.UpdateProgressAsync(operationId, currentStep,
                                $"Backing up registry key: {registryKey}", $"Saving current state of {registryKey}");
                        }

                        var keyBackupFile = Path.Combine(_backupDirectory, $"{backupId}_{registryKey.Replace("\\", "_").Replace(":", "")}.reg");

                        if (await CreateRegistryBackupAsync(registryKey))
                        {
                            registryBackups.Add(new
                            {
                                RegistryKey = registryKey,
                                BackupFile = keyBackupFile,
                                Success = true
                            });
                        }
                        else
                        {
                            registryBackups.Add(new
                            {
                                RegistryKey = registryKey,
                                BackupFile = keyBackupFile,
                                Success = false
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to backup registry key: {RegistryKey}", registryKey);
                        registryBackups.Add(new
                        {
                            RegistryKey = registryKey,
                            BackupFile = "",
                            Success = false,
                            Error = ex.Message
                        });
                    }
                    currentStep++;
                }

                // Save backup metadata
                var backupMetadata = new
                {
                    OptimizationName = optimizationName,
                    Timestamp = DateTime.Now,
                    BackupId = backupId,
                    RegistryBackups = registryBackups,
                    SystemSettings = await GetCurrentSystemSettingsAsync()
                };

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(backupMetadata, Newtonsoft.Json.Formatting.Indented);
                await File.WriteAllTextAsync(backupFilePath, json);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.UpdateProgressAsync(operationId, currentStep, "Backup completed",
                        $"Optimization backup created: {backupId}");
                    await _progressTrackingService.CompleteOperationAsync(operationId, true,
                        $"Optimization backup '{backupId}' created successfully");
                }

                _logger.Information("Successfully created optimization backup: {BackupId}", backupId);
                return backupId;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating optimization backup for: {OptimizationName}", optimizationName);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                        "Exception during optimization backup creation", ex);
                    await _progressTrackingService.CompleteOperationAsync(operationId, false,
                        $"Exception: {ex.Message}");
                }

                return string.Empty;
            }
        }

        public async Task<bool> RollbackOptimizationAsync(string backupId)
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                _logger.Information("Rolling back optimization: {BackupId}", backupId);

                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.json");
                if (!File.Exists(backupFile))
                {
                    _logger.Warning("Optimization backup file not found: {BackupFile}", backupFile);
                    return false;
                }

                var json = await File.ReadAllTextAsync(backupFile);
                dynamic? backupData = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                if (backupData?.RegistryBackups == null)
                {
                    _logger.Warning("Invalid backup data in file: {BackupFile}", backupFile);
                    return false;
                }

                var registryBackups = backupData.RegistryBackups;
                var totalSteps = registryBackups.Count + 2;

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.StartOperationAsync(operationId, "Rollback Optimization", totalSteps);
                    await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Initializing rollback...",
                        $"Rolling back optimization: {backupData.OptimizationName}");
                }

                var currentStep = 2;
                var success = true;

                // Restore each registry key
                foreach (var registryBackup in registryBackups)
                {
                    try
                    {
                        if (_progressTrackingService != null)
                        {
                            await _progressTrackingService.UpdateProgressAsync(operationId, currentStep,
                                $"Restoring registry key: {registryBackup.RegistryKey}",
                                $"Reverting changes to {registryBackup.RegistryKey}");
                        }

                        if (registryBackup.Success == true && !string.IsNullOrEmpty(registryBackup.BackupFile?.ToString()))
                        {
                            var restoreSuccess = await RestoreRegistryBackupAsync(Path.GetFileNameWithoutExtension(registryBackup.BackupFile.ToString()));
                            if (!restoreSuccess)
                            {
                                success = false;
                                _logger.Warning("Failed to restore registry key: {RegistryKey}", registryBackup.RegistryKey);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        success = false;
                        _logger.Error(ex, "Error restoring registry key during rollback: {RegistryKey}", registryBackup.RegistryKey);
                    }
                    currentStep++;
                }

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.UpdateProgressAsync(operationId, totalSteps,
                        success ? "Rollback completed successfully" : "Rollback completed with errors",
                        success ? "All changes have been reverted" : "Some changes could not be reverted");
                    await _progressTrackingService.CompleteOperationAsync(operationId, success,
                        success ? "Optimization rollback completed successfully" : "Rollback completed with errors");
                }

                _logger.Information("Optimization rollback completed. Success: {Success}", success);
                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during optimization rollback: {BackupId}", backupId);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                        "Exception during optimization rollback", ex);
                    await _progressTrackingService.CompleteOperationAsync(operationId, false,
                        $"Exception: {ex.Message}");
                }

                return false;
            }
        }

        public async Task<bool> CreateFullSystemBackupAsync(string description)
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                _logger.Information("Creating full system backup: {Description}", description);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.StartOperationAsync(operationId, "Create Full System Backup", 4);
                    await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Creating system restore point...",
                        "This is the foundation of the full backup");
                }

                // Create system restore point as the base
                var restorePointSuccess = await CreateSystemRestorePointAsync($"Full Backup - {description}");

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.UpdateProgressAsync(operationId, 2, "Backing up registry...",
                        "Creating comprehensive registry backup");
                }

                // Create comprehensive registry backup
                var registrySuccess = await CreateRegistryBackupAsync("HKEY_LOCAL_MACHINE");

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.UpdateProgressAsync(operationId, 3, "Backing up settings...",
                        "Saving current system settings");
                }

                // Create settings backup
                var settingsSuccess = await CreateSettingsBackupAsync();

                var success = restorePointSuccess && registrySuccess && settingsSuccess;

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.UpdateProgressAsync(operationId, 4,
                        success ? "Full backup completed" : "Backup completed with errors",
                        success ? "All system components backed up successfully" : "Some components failed to backup");
                    await _progressTrackingService.CompleteOperationAsync(operationId, success,
                        $"Full system backup: {(success ? "Success" : "Partial failure")}");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating full system backup: {Description}", description);

                if (_progressTrackingService != null)
                {
                    await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                        "Exception during full system backup", ex);
                    await _progressTrackingService.CompleteOperationAsync(operationId, false,
                        $"Exception: {ex.Message}");
                }

                return false;
            }
        }



        public async Task<bool> ValidateBackupIntegrityAsync(string backupId)
        {
            try
            {
                _logger.Information("Validating backup integrity: {BackupId}", backupId);

                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.json");
                if (!File.Exists(backupFile))
                {
                    return false;
                }

                var json = await File.ReadAllTextAsync(backupFile);
                dynamic? backupData = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                if (backupData == null)
                {
                    return false;
                }

                // Validate that referenced backup files exist
                if (backupData.RegistryBackups != null)
                {
                    foreach (var registryBackup in backupData.RegistryBackups)
                    {
                        if (registryBackup.BackupFile != null && !string.IsNullOrEmpty(registryBackup.BackupFile.ToString()))
                        {
                            if (!File.Exists(registryBackup.BackupFile.ToString()))
                            {
                                _logger.Warning("Missing backup file: {BackupFile}", registryBackup.BackupFile);
                                return false;
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error validating backup integrity: {BackupId}", backupId);
                return false;
            }
        }

        public async Task<BackupInfo?> GetBackupInfoAsync(string backupId)
        {
            try
            {
                var backups = await GetAvailableBackupsAsync();
                return backups.FirstOrDefault(b => b.Id == backupId);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting backup info: {BackupId}", backupId);
                return null;
            }
        }

        private async Task<Dictionary<string, object>> GetCurrentSystemSettingsAsync()
        {
            try
            {
                var settings = new Dictionary<string, object>
                {
                    ["PowerPlan"] = await GetCurrentPowerPlanAsync() ?? "Unknown",
                    ["VisualEffects"] = await GetVisualEffectsSettingsAsync() ?? "Unknown",
                    ["StartupPrograms"] = await GetStartupProgramsStateAsync(),
                    ["SystemInfo"] = new
                    {
                        OSVersion = Environment.OSVersion.ToString(),
                        MachineName = Environment.MachineName,
                        UserName = Environment.UserName,
                        ProcessorCount = Environment.ProcessorCount
                    }
                };

                return settings;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting current system settings");
                return new Dictionary<string, object>();
            }
        }

        #endregion
    }
}
