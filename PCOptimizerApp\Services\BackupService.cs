using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;

namespace PCOptimizerApp.Services
{
    public class BackupService : IBackupService
    {
        private readonly ILogger _logger = Log.ForContext<BackupService>();
        private readonly string _backupDirectory;

        public BackupService()
        {
            _backupDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PCOptimizer", "Backups");
            Directory.CreateDirectory(_backupDirectory);
        }

        public async Task<bool> CreateSystemRestorePointAsync(string description)
        {
            try
            {
                _logger.Information("Creating system restore point: {Description}", description);

                return await Task.Run(() =>
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "powershell",
                            Arguments = $"-Command \"Checkpoint-Computer -Description '{description}' -RestorePointType 'MODIFY_SETTINGS'\"",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true,
                            Verb = "runas" // Requires admin privileges
                        }
                    };

                    process.Start();
                    var output = process.StandardOutput.ReadToEnd();
                    var error = process.StandardError.ReadToEnd();
                    process.WaitForExit();

                    if (process.ExitCode == 0)
                    {
                        _logger.Information("Successfully created system restore point: {Description}", description);
                        return true;
                    }
                    else
                    {
                        _logger.Warning("Failed to create system restore point. Error: {Error}", error);
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating system restore point: {Description}", description);
                return false;
            }
        }

        public async Task<bool> CreateRegistryBackupAsync(string registryPath)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"registry_backup_{timestamp}.reg";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                _logger.Information("Creating registry backup for: {RegistryPath}", registryPath);

                return await Task.Run(() =>
                {
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = "reg",
                            Arguments = $"export \"{registryPath}\" \"{backupFilePath}\" /y",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        }
                    };

                    process.Start();
                    process.WaitForExit();

                    if (process.ExitCode == 0 && File.Exists(backupFilePath))
                    {
                        _logger.Information("Successfully created registry backup: {BackupPath}", backupFilePath);
                        return true;
                    }
                    else
                    {
                        _logger.Warning("Failed to create registry backup for: {RegistryPath}", registryPath);
                        return false;
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating registry backup for: {RegistryPath}", registryPath);
                return false;
            }
        }

        public async Task<bool> CreateSettingsBackupAsync()
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"settings_backup_{timestamp}.json";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                _logger.Information("Creating settings backup");

                var settingsBackup = new
                {
                    Timestamp = DateTime.Now,
                    PowerPlan = await GetCurrentPowerPlanAsync(),
                    VisualEffects = await GetVisualEffectsSettingsAsync(),
                    StartupPrograms = await GetStartupProgramsStateAsync(),
                    SystemSettings = await GetSystemSettingsAsync()
                };

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(settingsBackup, Newtonsoft.Json.Formatting.Indented);
                await File.WriteAllTextAsync(backupFilePath, json);

                _logger.Information("Successfully created settings backup: {BackupPath}", backupFilePath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating settings backup");
                return false;
            }
        }

        public async Task<List<BackupInfo>> GetAvailableBackupsAsync()
        {
            try
            {
                var backups = new List<BackupInfo>();

                // Get file-based backups
                var backupFiles = Directory.GetFiles(_backupDirectory, "*.*", SearchOption.TopDirectoryOnly);
                foreach (var file in backupFiles)
                {
                    var fileInfo = new FileInfo(file);
                    var backup = new BackupInfo
                    {
                        Id = Path.GetFileNameWithoutExtension(file),
                        Name = Path.GetFileName(file),
                        CreatedDate = fileInfo.CreationTime,
                        SizeMB = fileInfo.Length / (1024 * 1024),
                        Type = file.EndsWith(".reg") ? BackupType.RegistryBackup : BackupType.SettingsBackup,
                        Description = GetBackupDescription(file)
                    };
                    backups.Add(backup);
                }

                // Get system restore points
                var restorePoints = await GetSystemRestorePointsAsync();
                backups.AddRange(restorePoints);

                return backups.OrderByDescending(b => b.CreatedDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available backups");
                return new List<BackupInfo>();
            }
        }

        public async Task<bool> RestoreFromBackupAsync(string backupId)
        {
            try
            {
                _logger.Information("Restoring from backup: {BackupId}", backupId);

                var backups = await GetAvailableBackupsAsync();
                var backup = backups.FirstOrDefault(b => b.Id == backupId);

                if (backup == null)
                {
                    _logger.Warning("Backup not found: {BackupId}", backupId);
                    return false;
                }

                return backup.Type switch
                {
                    BackupType.SystemRestorePoint => await RestoreSystemRestorePointAsync(backupId),
                    BackupType.RegistryBackup => await RestoreRegistryBackupAsync(backupId),
                    BackupType.SettingsBackup => await RestoreSettingsBackupAsync(backupId),
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring from backup: {BackupId}", backupId);
                return false;
            }
        }

        public async Task<bool> DeleteBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, backupId + ".*");
                var matchingFiles = Directory.GetFiles(_backupDirectory, $"{backupId}.*");

                foreach (var file in matchingFiles)
                {
                    File.Delete(file);
                    _logger.Information("Deleted backup file: {File}", file);
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error deleting backup: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<string?> GetCurrentPowerPlanAsync()
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powercfg",
                        Arguments = "/getactivescheme",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                return output.Trim();
            }
            catch
            {
                return null;
            }
        }

        private async Task<object?> GetVisualEffectsSettingsAsync()
        {
            try
            {
                using var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects");
                return key?.GetValue("VisualFXSetting");
            }
            catch
            {
                return null;
            }
        }

        private async Task<List<string>> GetStartupProgramsStateAsync()
        {
            try
            {
                var startupPrograms = new List<string>();

                using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Run");
                if (key != null)
                {
                    foreach (var valueName in key.GetValueNames())
                    {
                        startupPrograms.Add($"{valueName}={key.GetValue(valueName)}");
                    }
                }

                return startupPrograms;
            }
            catch
            {
                return new List<string>();
            }
        }

        private async Task<Dictionary<string, object?>> GetSystemSettingsAsync()
        {
            try
            {
                var settings = new Dictionary<string, object?>();

                // Add various system settings that we track
                settings["WindowsVersion"] = Environment.OSVersion.ToString();
                settings["MachineName"] = Environment.MachineName;
                settings["UserName"] = Environment.UserName;

                return settings;
            }
            catch
            {
                return new Dictionary<string, object?>();
            }
        }

        private async Task<List<BackupInfo>> GetSystemRestorePointsAsync()
        {
            try
            {
                var restorePoints = new List<BackupInfo>();

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "powershell",
                        Arguments = "-Command \"Get-ComputerRestorePoint | ConvertTo-Json\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true
                    }
                };

                process.Start();
                var output = await process.StandardOutput.ReadToEndAsync();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0 && !string.IsNullOrEmpty(output))
                {
                    try
                    {
                        dynamic? restorePointData = Newtonsoft.Json.JsonConvert.DeserializeObject(output);
                        if (restorePointData != null)
                        {
                            // Parse restore point data and create BackupInfo objects
                            // This would require more detailed implementation based on the actual JSON structure
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error parsing restore point data");
                    }
                }

                return restorePoints;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting system restore points");
                return new List<BackupInfo>();
            }
        }

        private async Task<bool> RestoreSystemRestorePointAsync(string backupId)
        {
            try
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "rstrui.exe",
                        Arguments = $"/offline:C:\\Windows /RestorePoint:{backupId}",
                        UseShellExecute = true,
                        Verb = "runas"
                    }
                };

                process.Start();
                return true; // System restore is interactive, so we can't wait for completion
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring system restore point: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<bool> RestoreRegistryBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.reg");
                if (!File.Exists(backupFile))
                {
                    _logger.Warning("Registry backup file not found: {BackupFile}", backupFile);
                    return false;
                }

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = "reg",
                        Arguments = $"import \"{backupFile}\"",
                        UseShellExecute = false,
                        CreateNoWindow = true,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        Verb = "runas"
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                return process.ExitCode == 0;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring registry backup: {BackupId}", backupId);
                return false;
            }
        }

        private async Task<bool> RestoreSettingsBackupAsync(string backupId)
        {
            try
            {
                var backupFile = Path.Combine(_backupDirectory, $"{backupId}.json");
                if (!File.Exists(backupFile))
                {
                    _logger.Warning("Settings backup file not found: {BackupFile}", backupFile);
                    return false;
                }

                var json = await File.ReadAllTextAsync(backupFile);
                dynamic? settingsBackup = Newtonsoft.Json.JsonConvert.DeserializeObject(json);

                if (settingsBackup != null)
                {
                    // Restore individual settings
                    // This would require implementing restoration for each setting type
                    // For now, we'll just log that we would restore them
                    _logger.Information("Would restore settings from backup: {BackupId}", backupId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring settings backup: {BackupId}", backupId);
                return false;
            }
        }

        private string GetBackupDescription(string filePath)
        {
            var fileName = Path.GetFileName(filePath);
            var extension = Path.GetExtension(filePath).ToLower();

            return extension switch
            {
                ".reg" => "Registry backup",
                ".json" => "Settings backup",
                _ => "System backup"
            };
        }
    }
}
