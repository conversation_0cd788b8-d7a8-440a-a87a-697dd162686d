using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;
using System.Management;

namespace PCOptimizerApp.Services
{
    public class HardwareDetectionService : IHardwareDetectionService
    {
        private readonly ILogger _logger = Log.ForContext<HardwareDetectionService>();

        public async Task<SystemInfo> DetectHardwareAsync()
        {
            try
            {
                var systemInfo = new SystemInfo();

                await Task.Run(() =>
                {
                    DetectStorageDevices(systemInfo);
                    DetectProcessorInfo(systemInfo);
                    DetectMemoryInfo(systemInfo);
                    DetectGraphicsCard(systemInfo);
                });

                systemInfo.LastUpdated = DateTime.Now;
                return systemInfo;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting hardware");
                throw;
            }
        }

        public async Task<List<OptimizationItem>> GetHardwareSpecificOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();
                var systemInfo = await DetectHardwareAsync();

                // SSD-specific optimizations
                if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.SSD || d.Type == StorageType.NVMe))
                {
                    optimizations.AddRange(GetSsdOptimizations());
                }

                // High-RAM optimizations
                if (systemInfo.TotalMemoryGB >= 16)
                {
                    optimizations.AddRange(GetHighRamOptimizations());
                }

                // Multi-core CPU optimizations
                if (systemInfo.ProcessorCores >= 4)
                {
                    optimizations.AddRange(GetMultiCoreOptimizations());
                }

                // Gaming-specific optimizations (if dedicated GPU detected)
                if (systemInfo.GraphicsCard != null && !systemInfo.GraphicsCard.Name?.Contains("Intel") == true)
                {
                    optimizations.AddRange(GetGamingOptimizations());
                }

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting hardware-specific optimizations");
                throw;
            }
        }

        public async Task<bool> IsSsdOptimizationApplicableAsync()
        {
            try
            {
                var systemInfo = await DetectHardwareAsync();
                return systemInfo.StorageDevices.Any(d => d.Type == StorageType.SSD || d.Type == StorageType.NVMe);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error checking SSD optimization applicability");
                return false;
            }
        }

        public async Task<bool> IsGameModeOptimizationApplicableAsync()
        {
            try
            {
                var systemInfo = await DetectHardwareAsync();
                return systemInfo.GraphicsCard != null && 
                       !systemInfo.GraphicsCard.Name?.Contains("Intel") == true &&
                       systemInfo.TotalMemoryGB >= 8;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error checking game mode optimization applicability");
                return false;
            }
        }

        private void DetectStorageDevices(SystemInfo systemInfo)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject drive in collection)
                    {
                        var device = new StorageDevice
                        {
                            Name = drive["Caption"]?.ToString(),
                            Model = drive["Model"]?.ToString(),
                            TotalSizeGB = Convert.ToInt64(drive["Size"] ?? 0) / (1024 * 1024 * 1024)
                        };

                        // Detect storage type
                        var model = device.Model?.ToLower() ?? "";
                        var mediaType = drive["MediaType"]?.ToString()?.ToLower() ?? "";

                        if (model.Contains("ssd") || model.Contains("nvme") || mediaType.Contains("ssd"))
                        {
                            device.Type = model.Contains("nvme") ? StorageType.NVMe : StorageType.SSD;
                        }
                        else
                        {
                            device.Type = StorageType.HDD;
                        }

                        device.Health = "Good"; // Would need SMART data for accurate health
                        device.TrimEnabled = device.Type != StorageType.HDD; // Assume TRIM is enabled for SSDs

                        systemInfo.StorageDevices.Add(device);
                    }
                }

                // Get free space information
                var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
                foreach (var drive in drives)
                {
                    var matchingDevice = systemInfo.StorageDevices.FirstOrDefault();
                    if (matchingDevice != null)
                    {
                        matchingDevice.FreeSpaceGB = drive.AvailableFreeSpace / (1024 * 1024 * 1024);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting storage devices");
            }
        }

        private void DetectProcessorInfo(SystemInfo systemInfo)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject processor in collection)
                    {
                        systemInfo.ProcessorName = processor["Name"]?.ToString()?.Trim();
                        systemInfo.ProcessorCores = Convert.ToInt32(processor["NumberOfCores"] ?? 0);
                        systemInfo.ProcessorSpeedGHz = Convert.ToDouble(processor["MaxClockSpeed"] ?? 0) / 1000.0;
                        break; // Take first processor
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting processor information");
            }
        }

        private void DetectMemoryInfo(SystemInfo systemInfo)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject system in collection)
                    {
                        var totalMemory = Convert.ToInt64(system["TotalPhysicalMemory"] ?? 0);
                        systemInfo.TotalMemoryGB = totalMemory / (1024 * 1024 * 1024);
                        break;
                    }
                }

                // Get available memory
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject os in collection)
                    {
                        var freeMemoryKB = Convert.ToInt64(os["FreePhysicalMemory"] ?? 0);
                        // Convert KB to GB: KB / (1024 * 1024 * 1024) = GB
                        systemInfo.AvailableMemoryGB = freeMemoryKB / (1024L * 1024L * 1024L);

                        // Calculate memory usage percentage using double precision
                        if (systemInfo.TotalMemoryGB > 0)
                        {
                            var totalMemoryPrecise = (double)systemInfo.TotalMemoryGB;
                            var availableMemoryPrecise = (double)systemInfo.AvailableMemoryGB;
                            var usedMemoryPrecise = totalMemoryPrecise - availableMemoryPrecise;
                            systemInfo.MemoryUsagePercentage = (usedMemoryPrecise / totalMemoryPrecise) * 100.0;
                            // Ensure percentage is between 0 and 100
                            systemInfo.MemoryUsagePercentage = Math.Max(0, Math.Min(100, systemInfo.MemoryUsagePercentage));
                        }

                        // Debug logging
                        _logger.Information("Hardware Memory Debug - Total: {TotalGB} GB, Available: {AvailableGB} GB, Usage: {UsagePercent:F1}%",
                            systemInfo.TotalMemoryGB, systemInfo.AvailableMemoryGB, systemInfo.MemoryUsagePercentage);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting memory information");
            }
        }

        private void DetectGraphicsCard(SystemInfo systemInfo)
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject gpu in collection)
                    {
                        var name = gpu["Name"]?.ToString();
                        if (!string.IsNullOrEmpty(name) && !name.Contains("Basic Display") && !name.Contains("Microsoft"))
                        {
                            systemInfo.GraphicsCard = new GraphicsCard
                            {
                                Name = name,
                                DriverVersion = gpu["DriverVersion"]?.ToString(),
                                VRamMB = Convert.ToInt64(gpu["AdapterRAM"] ?? 0) / (1024 * 1024),
                                DriverUpToDate = true // Would need version checking for accuracy
                            };
                            break; // Take first dedicated GPU
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting graphics card information");
            }
        }

        private List<OptimizationItem> GetSsdOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "ssd_trim_enable",
                    Name = "Enable TRIM for SSD",
                    Description = "Enables TRIM command for better SSD performance and longevity",
                    Category = "Storage",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Improved SSD performance and lifespan"
                },
                new OptimizationItem
                {
                    Id = "ssd_superfetch_disable",
                    Name = "Disable Superfetch for SSD",
                    Description = "Disables Superfetch service which is not needed for SSDs",
                    Category = "Storage",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Reduced unnecessary disk activity"
                },
                new OptimizationItem
                {
                    Id = "ssd_indexing_optimize",
                    Name = "Optimize Search Indexing",
                    Description = "Configures Windows Search indexing for optimal SSD performance",
                    Category = "Storage",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster search with less SSD wear"
                }
            };
        }

        private List<OptimizationItem> GetHighRamOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "high_ram_virtual_memory",
                    Name = "Optimize Virtual Memory for High RAM",
                    Description = "Adjusts virtual memory settings for systems with 16GB+ RAM",
                    Category = "Memory",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better memory management and performance"
                },
                new OptimizationItem
                {
                    Id = "high_ram_memory_compression",
                    Name = "Configure Memory Compression",
                    Description = "Optimizes memory compression for high-RAM systems",
                    Category = "Memory",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "More efficient memory usage"
                }
            };
        }

        private List<OptimizationItem> GetMultiCoreOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "multicore_cpu_scheduling",
                    Name = "Optimize CPU Scheduling",
                    Description = "Configures CPU scheduling for multi-core processors",
                    Category = "CPU",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better CPU utilization across cores"
                },
                new OptimizationItem
                {
                    Id = "multicore_power_settings",
                    Name = "Multi-Core Power Settings",
                    Description = "Optimizes power settings for multi-core performance",
                    Category = "Power",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Maximum CPU performance when needed"
                }
            };
        }

        private List<OptimizationItem> GetGamingOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "gaming_mode_enable",
                    Name = "Enable Game Mode",
                    Description = "Enables Windows Game Mode for better gaming performance",
                    Category = "Gaming",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Better gaming performance and reduced input lag"
                },
                new OptimizationItem
                {
                    Id = "gaming_gpu_scheduling",
                    Name = "Hardware-Accelerated GPU Scheduling",
                    Description = "Enables hardware-accelerated GPU scheduling for better graphics performance",
                    Category = "Gaming",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Improved graphics performance and reduced latency"
                }
            };
        }
    }
}
