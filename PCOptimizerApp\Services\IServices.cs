using PCOptimizerApp.Models;

namespace PCOptimizerApp.Services
{
    public interface ISystemInfoService
    {
        Task<SystemInfo> GetSystemInfoAsync();
        Task<PerformanceMetrics> GetCurrentPerformanceMetricsAsync();
        Task<SystemHealthScore> CalculateSystemHealthScoreAsync();
        Task<List<ProcessInfo>> GetTopProcessesAsync(int count = 10);
        Task<List<StartupProgram>> GetStartupProgramsAsync();
    }

    public interface IOptimizationService
    {
        Task<List<OptimizationItem>> GetAvailableOptimizationsAsync();
        Task<List<OptimizationItem>> GetRecommendedOptimizationsAsync();
        Task<bool> ApplyOptimizationAsync(string optimizationId);
        Task<bool> RevertOptimizationAsync(string optimizationId);
        Task<bool> ApplyMultipleOptimizationsAsync(List<string> optimizationIds);
        Task<OptimizationResult> RunQuickOptimizeAsync();
    }

    public interface IHardwareDetectionService
    {
        Task<SystemInfo> DetectHardwareAsync();
        Task<List<OptimizationItem>> GetHardwareSpecificOptimizationsAsync();
        Task<bool> IsSsdOptimizationApplicableAsync();
        Task<bool> IsGameModeOptimizationApplicableAsync();
    }

    public interface IBackupService
    {
        Task<bool> CreateSystemRestorePointAsync(string description);
        Task<bool> CreateRegistryBackupAsync(string registryPath);
        Task<bool> CreateSettingsBackupAsync();
        Task<List<BackupInfo>> GetAvailableBackupsAsync();
        Task<bool> RestoreFromBackupAsync(string backupId);
        Task<bool> DeleteBackupAsync(string backupId);

        // Enhanced backup and rollback capabilities
        Task<string> CreateOptimizationBackupAsync(string optimizationName, List<string> affectedRegistryKeys);
        Task<bool> RollbackOptimizationAsync(string backupId);
        Task<bool> CreateFullSystemBackupAsync(string description);
        Task<List<BackupInfo>> GetSystemRestorePointsAsync();
        Task<bool> ValidateBackupIntegrityAsync(string backupId);
        Task<BackupInfo?> GetBackupInfoAsync(string backupId);
    }

    public interface IRegistryService
    {
        Task<bool> SetRegistryValueAsync(string keyPath, string valueName, object value);
        Task<object?> GetRegistryValueAsync(string keyPath, string valueName);
        Task<bool> DeleteRegistryValueAsync(string keyPath, string valueName);
        Task<bool> CreateRegistryKeyAsync(string keyPath);
        Task<bool> DeleteRegistryKeyAsync(string keyPath);
        Task<bool> BackupRegistryKeyAsync(string keyPath, string backupPath);
        Task<bool> RestoreRegistryKeyAsync(string backupPath, string keyPath);
    }

    public interface IPerformanceMonitoringService
    {
        event EventHandler<PerformanceMetrics>? PerformanceUpdated;
        void StartMonitoring();
        void StopMonitoring();
        Task<PerformanceMetrics> GetCurrentMetricsAsync();
        Task<List<PerformanceMetrics>> GetHistoricalMetricsAsync(DateTime from, DateTime to);
    }

    public interface IProgressTrackingService
    {
        event EventHandler<ProgressUpdateEventArgs>? ProgressUpdated;
        Task StartOperationAsync(string operationId, string operationName, int totalSteps);
        Task UpdateProgressAsync(string operationId, int currentStep, string currentStepDescription, string? details = null);
        Task CompleteOperationAsync(string operationId, bool success, string? result = null);
        Task<List<OperationHistory>> GetOperationHistoryAsync();
        Task LogOperationDetailAsync(string operationId, string level, string message, Exception? exception = null);
    }

    public interface ISmartAnalysisService
    {
        event EventHandler<AnalysisProgressEventArgs>? AnalysisProgressUpdated;
        Task<SmartAnalysisResult> PerformSmartAnalysisAsync();
        Task<List<HardwareDetectionResult>> DetectHardwareCapabilitiesAsync();
        Task<List<UsagePatternResult>> AnalyzeUsagePatternsAsync();
        Task<List<OptimizationRecommendation>> GenerateSmartRecommendationsAsync(SystemInfo systemInfo);
    }

    public class OptimizationResult
    {
        public bool Success { get; set; }
        public List<string> AppliedOptimizations { get; set; } = new();
        public List<string> FailedOptimizations { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
        public int ImprovementPercentage { get; set; }
    }
}
