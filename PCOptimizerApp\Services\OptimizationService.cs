using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;

namespace PCOptimizerApp.Services
{
    public class OptimizationService : IOptimizationService
    {
        private readonly ILogger _logger = Log.ForContext<OptimizationService>();
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IRegistryService _registryService;
        private readonly IBackupService _backupService;

        public OptimizationService(
            IHardwareDetectionService hardwareDetectionService,
            IRegistryService registryService,
            IBackupService backupService)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _registryService = registryService;
            _backupService = backupService;
        }

        public async Task<List<OptimizationItem>> GetAvailableOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();

                // Get basic optimizations
                optimizations.AddRange(GetBasicOptimizations());

                // Get hardware-specific optimizations
                var hardwareOptimizations = await _hardwareDetectionService.GetHardwareSpecificOptimizationsAsync();
                optimizations.AddRange(hardwareOptimizations);

                // Check applicability for each optimization
                foreach (var optimization in optimizations)
                {
                    optimization.IsApplicable = await CheckOptimizationApplicabilityAsync(optimization);
                }

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available optimizations");
                throw;
            }
        }

        public async Task<List<OptimizationItem>> GetRecommendedOptimizationsAsync()
        {
            try
            {
                var allOptimizations = await GetAvailableOptimizationsAsync();
                
                // Filter for safe, high-impact optimizations that are applicable
                var recommended = allOptimizations
                    .Where(o => o.IsApplicable && 
                               !o.IsApplied && 
                               o.Safety >= OptimizationSafety.MostlySafe &&
                               o.Impact >= OptimizationImpact.Medium)
                    .OrderByDescending(o => (int)o.Impact)
                    .ThenByDescending(o => (int)o.Safety)
                    .Take(5)
                    .ToList();

                return recommended;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting recommended optimizations");
                throw;
            }
        }

        public async Task<bool> ApplyOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Applying optimization: {OptimizationId}", optimizationId);

                // Create backup before applying optimization
                await _backupService.CreateSystemRestorePointAsync($"Before applying {optimizationId}");

                bool success = optimizationId switch
                {
                    "visual_effects_performance" => await ApplyVisualEffectsOptimizationAsync(),
                    "startup_programs_cleanup" => await ApplyStartupCleanupAsync(),
                    "temporary_files_cleanup" => await ApplyTemporaryFilesCleanupAsync(),
                    "power_high_performance" => await ApplyHighPerformancePowerPlanAsync(),
                    "ssd_trim_enable" => await ApplySsdTrimOptimizationAsync(),
                    "ssd_superfetch_disable" => await ApplySuperfetchDisableAsync(),
                    "gaming_mode_enable" => await ApplyGamingModeAsync(),
                    "multicore_cpu_scheduling" => await ApplyMultiCoreCpuSchedulingAsync(),
                    "high_ram_virtual_memory" => await ApplyHighRamVirtualMemoryAsync(),
                    _ => false
                };

                if (success)
                {
                    _logger.Information("Successfully applied optimization: {OptimizationId}", optimizationId);
                }
                else
                {
                    _logger.Warning("Failed to apply optimization: {OptimizationId}", optimizationId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> RevertOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Reverting optimization: {OptimizationId}", optimizationId);

                bool success = optimizationId switch
                {
                    "visual_effects_performance" => await RevertVisualEffectsOptimizationAsync(),
                    "power_high_performance" => await RevertPowerPlanAsync(),
                    "ssd_superfetch_disable" => await RevertSuperfetchDisableAsync(),
                    "gaming_mode_enable" => await RevertGamingModeAsync(),
                    _ => false
                };

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> ApplyMultipleOptimizationsAsync(List<string> optimizationIds)
        {
            try
            {
                _logger.Information("Applying multiple optimizations: {Count}", optimizationIds.Count);

                // Create backup before applying optimizations
                await _backupService.CreateSystemRestorePointAsync("Before multiple optimizations");

                int successCount = 0;
                foreach (var optimizationId in optimizationIds)
                {
                    if (await ApplyOptimizationAsync(optimizationId))
                    {
                        successCount++;
                    }
                    
                    // Small delay between optimizations
                    await Task.Delay(100);
                }

                bool allSuccessful = successCount == optimizationIds.Count;
                _logger.Information("Applied {SuccessCount}/{TotalCount} optimizations", successCount, optimizationIds.Count);

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multiple optimizations");
                return false;
            }
        }

        public async Task<OptimizationResult> RunQuickOptimizeAsync()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                _logger.Information("Starting quick optimization");

                var result = new OptimizationResult();

                // Get recommended optimizations
                var recommended = await GetRecommendedOptimizationsAsync();
                var safeOptimizations = recommended
                    .Where(o => o.Safety >= OptimizationSafety.Safe)
                    .Select(o => o.Id!)
                    .ToList();

                // Create backup
                await _backupService.CreateSystemRestorePointAsync("Quick Optimize");

                // Apply optimizations
                foreach (var optimizationId in safeOptimizations)
                {
                    try
                    {
                        if (await ApplyOptimizationAsync(optimizationId))
                        {
                            result.AppliedOptimizations.Add(optimizationId);
                        }
                        else
                        {
                            result.FailedOptimizations.Add(optimizationId);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply optimization during quick optimize: {OptimizationId}", optimizationId);
                        result.FailedOptimizations.Add(optimizationId);
                    }
                }

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.AppliedOptimizations.Count > 0;
                result.ImprovementPercentage = CalculateImprovementPercentage(result.AppliedOptimizations.Count);

                _logger.Information("Quick optimization completed. Applied: {Applied}, Failed: {Failed}, Duration: {Duration}", 
                    result.AppliedOptimizations.Count, result.FailedOptimizations.Count, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during quick optimization");
                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                };
            }
        }

        private List<OptimizationItem> GetBasicOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "visual_effects_performance",
                    Name = "Optimize Visual Effects for Performance",
                    Description = "Adjusts Windows visual effects for better performance",
                    Category = "Performance",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% better system responsiveness"
                },
                new OptimizationItem
                {
                    Id = "startup_programs_cleanup",
                    Name = "Startup Programs Cleanup",
                    Description = "Disables unnecessary startup programs",
                    Category = "Startup",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster boot times and reduced resource usage"
                },
                new OptimizationItem
                {
                    Id = "temporary_files_cleanup",
                    Name = "Temporary Files Cleanup",
                    Description = "Removes temporary files and cache to free up space",
                    Category = "Cleanup",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = false,
                    ExpectedImprovement = "Free up disk space and improve performance"
                },
                new OptimizationItem
                {
                    Id = "power_high_performance",
                    Name = "High Performance Power Plan",
                    Description = "Sets Windows power plan to High Performance",
                    Category = "Power",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Maximum CPU and system performance"
                },
                new OptimizationItem
                {
                    Id = "windows_updates_optimize",
                    Name = "Optimize Windows Updates",
                    Description = "Configures Windows Update settings for better performance",
                    Category = "System",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Reduced background update activity"
                }
            };
        }

        private async Task<bool> CheckOptimizationApplicabilityAsync(OptimizationItem optimization)
        {
            try
            {
                return optimization.Id switch
                {
                    "ssd_trim_enable" => await _hardwareDetectionService.IsSsdOptimizationApplicableAsync(),
                    "gaming_mode_enable" => await _hardwareDetectionService.IsGameModeOptimizationApplicableAsync(),
                    _ => true // Most optimizations are generally applicable
                };
            }
            catch
            {
                return false;
            }
        }

        #region Optimization Implementations

        private async Task<bool> ApplyVisualEffectsOptimizationAsync()
        {
            try
            {
                // Set visual effects for performance
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 2); // Adjust for performance

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying visual effects optimization");
                return false;
            }
        }

        private async Task<bool> ApplyStartupCleanupAsync()
        {
            try
            {
                // This would involve disabling unnecessary startup programs
                // Implementation would check common unnecessary startup items
                // and disable them safely
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying startup cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyTemporaryFilesCleanupAsync()
        {
            try
            {
                // Clean temporary files
                var tempFolders = new[]
                {
                    Path.GetTempPath(),
                    @"C:\Windows\Temp",
                    @"C:\Windows\Prefetch"
                };

                foreach (var folder in tempFolders)
                {
                    if (Directory.Exists(folder))
                    {
                        try
                        {
                            var files = Directory.GetFiles(folder, "*", SearchOption.TopDirectoryOnly);
                            foreach (var file in files)
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch
                                {
                                    // Ignore files that can't be deleted (in use)
                                }
                            }
                        }
                        catch
                        {
                            // Continue with other folders if one fails
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying temporary files cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyHighPerformancePowerPlanAsync()
        {
            try
            {
                // Set high performance power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", // High Performance GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high performance power plan");
                return false;
            }
        }

        private async Task<bool> ApplySsdTrimOptimizationAsync()
        {
            try
            {
                // Enable TRIM
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "fsutil",
                    Arguments = "behavior set DisableDeleteNotify 0",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas" // Requires admin
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying SSD TRIM optimization");
                return false;
            }
        }

        private async Task<bool> ApplySuperfetchDisableAsync()
        {
            try
            {
                // Disable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= disabled",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling Superfetch");
                return false;
            }
        }

        private async Task<bool> ApplyGamingModeAsync()
        {
            try
            {
                // Enable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 1);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 1);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error enabling gaming mode");
                return false;
            }
        }

        private async Task<bool> ApplyMultiCoreCpuSchedulingAsync()
        {
            try
            {
                // Optimize CPU scheduling for multi-core
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl",
                    "Win32PrioritySeparation", 0x26);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multi-core CPU scheduling");
                return false;
            }
        }

        private async Task<bool> ApplyHighRamVirtualMemoryAsync()
        {
            try
            {
                // This would involve setting appropriate virtual memory settings
                // based on the amount of RAM available
                // Implementation would be more complex and require system analysis
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high RAM virtual memory optimization");
                return false;
            }
        }

        #endregion

        #region Revert Implementations

        private async Task<bool> RevertVisualEffectsOptimizationAsync()
        {
            try
            {
                // Restore default visual effects
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 0); // Let Windows choose

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting visual effects optimization");
                return false;
            }
        }

        private async Task<bool> RevertPowerPlanAsync()
        {
            try
            {
                // Set balanced power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 381b4222-f694-41f0-9685-ff5bb260df2e", // Balanced GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting power plan");
                return false;
            }
        }

        private async Task<bool> RevertSuperfetchDisableAsync()
        {
            try
            {
                // Re-enable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= auto",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error re-enabling Superfetch");
                return false;
            }
        }

        private async Task<bool> RevertGamingModeAsync()
        {
            try
            {
                // Disable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 0);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 0);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling gaming mode");
                return false;
            }
        }

        #endregion

        private int CalculateImprovementPercentage(int appliedOptimizations)
        {
            // Simple calculation - each optimization contributes to improvement
            return Math.Min(appliedOptimizations * 5, 25); // Max 25% improvement
        }
    }
}
