using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;

namespace PCOptimizerApp.Services
{
    public class OptimizationService : IOptimizationService
    {
        private readonly ILogger _logger = Log.ForContext<OptimizationService>();
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly IRegistryService _registryService;
        private readonly IBackupService _backupService;
        private readonly IProgressTrackingService _progressTrackingService;

        public OptimizationService(
            IHardwareDetectionService hardwareDetectionService,
            IRegistryService registryService,
            IBackupService backupService,
            IProgressTrackingService progressTrackingService)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _registryService = registryService;
            _backupService = backupService;
            _progressTrackingService = progressTrackingService;
        }

        public async Task<List<OptimizationItem>> GetAvailableOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();

                // Get basic optimizations
                optimizations.AddRange(GetBasicOptimizations());

                // Get enhanced hardware-specific optimizations
                var hardwareOptimizations = await GetEnhancedHardwareOptimizationsAsync();
                optimizations.AddRange(hardwareOptimizations);

                // Get legacy hardware optimizations for compatibility
                var legacyOptimizations = await _hardwareDetectionService.GetHardwareSpecificOptimizationsAsync();
                optimizations.AddRange(legacyOptimizations);

                // Check applicability for each optimization
                foreach (var optimization in optimizations)
                {
                    optimization.IsApplicable = await CheckOptimizationApplicabilityAsync(optimization);
                }

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting available optimizations");
                throw;
            }
        }

        public async Task<List<OptimizationItem>> GetRecommendedOptimizationsAsync()
        {
            try
            {
                var allOptimizations = await GetAvailableOptimizationsAsync();
                
                // Filter for safe, high-impact optimizations that are applicable
                var recommended = allOptimizations
                    .Where(o => o.IsApplicable && 
                               !o.IsApplied && 
                               o.Safety >= OptimizationSafety.MostlySafe &&
                               o.Impact >= OptimizationImpact.Medium)
                    .OrderByDescending(o => (int)o.Impact)
                    .ThenByDescending(o => (int)o.Safety)
                    .Take(5)
                    .ToList();

                return recommended;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting recommended optimizations");
                throw;
            }
        }

        public async Task<bool> ApplyOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Applying optimization: {OptimizationId}", optimizationId);

                // Create enhanced backup before applying optimization
                var affectedRegistryKeys = GetAffectedRegistryKeys(optimizationId);
                if (affectedRegistryKeys.Any())
                {
                    var backupId = await _backupService.CreateOptimizationBackupAsync(optimizationId, affectedRegistryKeys);
                    if (string.IsNullOrEmpty(backupId))
                    {
                        _logger.Warning("Failed to create optimization backup for: {OptimizationId}. Creating system restore point instead.", optimizationId);
                        await _backupService.CreateSystemRestorePointAsync($"Before applying {optimizationId}");
                    }
                    else
                    {
                        _logger.Information("Created optimization backup {BackupId} for: {OptimizationId}", backupId, optimizationId);
                    }
                }
                else
                {
                    // Fallback to system restore point for optimizations without registry changes
                    await _backupService.CreateSystemRestorePointAsync($"Before applying {optimizationId}");
                }

                bool success = optimizationId switch
                {
                    // Basic optimizations
                    "visual_effects_performance" => await ApplyVisualEffectsOptimizationAsync(),
                    "startup_programs_cleanup" => await ApplyStartupCleanupAsync(),
                    "temporary_files_cleanup" => await ApplyTemporaryFilesCleanupAsync(),
                    "power_high_performance" => await ApplyHighPerformancePowerPlanAsync(),
                    "ssd_trim_enable" => await ApplySsdTrimOptimizationAsync(),
                    "ssd_superfetch_disable" => await ApplySuperfetchDisableAsync(),
                    "gaming_mode_enable" => await ApplyGamingModeAsync(),
                    "multicore_cpu_scheduling" => await ApplyMultiCoreCpuSchedulingAsync(),
                    "high_ram_virtual_memory" => await ApplyHighRamVirtualMemoryAsync(),

                    // Intel-specific optimizations
                    "intel_turbo_boost_optimize" => await ApplyIntelTurboBoostOptimizationAsync(),
                    "intel_speedstep_optimize" => await ApplyIntelSpeedStepOptimizationAsync(),

                    // AMD-specific optimizations
                    "amd_precision_boost_optimize" => await ApplyAmdPrecisionBoostOptimizationAsync(),
                    "amd_coolnquiet_optimize" => await ApplyAmdCoolNQuietOptimizationAsync(),

                    // Storage-specific optimizations
                    "nvme_power_management_optimize" => await ApplyNvmePowerManagementOptimizationAsync(),
                    "ssd_write_cache_optimize" => await ApplySsdWriteCacheOptimizationAsync(),
                    "hdd_defragmentation_schedule" => await ApplyHddDefragmentationScheduleAsync(),

                    // Memory-specific optimizations
                    "high_memory_virtual_memory_disable" => await ApplyHighMemoryVirtualMemoryDisableAsync(),
                    "memory_compression_optimize" => await ApplyMemoryCompressionOptimizationAsync(),

                    // GPU-specific optimizations
                    "nvidia_gpu_scheduling_optimize" => await ApplyNvidiaGpuSchedulingOptimizationAsync(),
                    "amd_gpu_power_optimize" => await ApplyAmdGpuPowerOptimizationAsync(),

                    _ => false
                };

                if (success)
                {
                    _logger.Information("Successfully applied optimization: {OptimizationId}", optimizationId);
                }
                else
                {
                    _logger.Warning("Failed to apply optimization: {OptimizationId}", optimizationId);
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> RevertOptimizationAsync(string optimizationId)
        {
            try
            {
                _logger.Information("Reverting optimization: {OptimizationId}", optimizationId);

                bool success = optimizationId switch
                {
                    "visual_effects_performance" => await RevertVisualEffectsOptimizationAsync(),
                    "power_high_performance" => await RevertPowerPlanAsync(),
                    "ssd_superfetch_disable" => await RevertSuperfetchDisableAsync(),
                    "gaming_mode_enable" => await RevertGamingModeAsync(),
                    _ => false
                };

                return success;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting optimization: {OptimizationId}", optimizationId);
                return false;
            }
        }

        public async Task<bool> ApplyMultipleOptimizationsAsync(List<string> optimizationIds)
        {
            try
            {
                _logger.Information("Applying multiple optimizations: {Count}", optimizationIds.Count);

                // Create backup before applying optimizations
                await _backupService.CreateSystemRestorePointAsync("Before multiple optimizations");

                int successCount = 0;
                foreach (var optimizationId in optimizationIds)
                {
                    if (await ApplyOptimizationAsync(optimizationId))
                    {
                        successCount++;
                    }
                    
                    // Small delay between optimizations
                    await Task.Delay(100);
                }

                bool allSuccessful = successCount == optimizationIds.Count;
                _logger.Information("Applied {SuccessCount}/{TotalCount} optimizations", successCount, optimizationIds.Count);

                return allSuccessful;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multiple optimizations");
                return false;
            }
        }

        public async Task<OptimizationResult> RunQuickOptimizeAsync()
        {
            var operationId = Guid.NewGuid().ToString();

            try
            {
                var stopwatch = Stopwatch.StartNew();
                _logger.Information("Starting quick optimization");

                var result = new OptimizationResult();

                // Get recommended optimizations
                var recommended = await GetRecommendedOptimizationsAsync();
                var safeOptimizations = recommended
                    .Where(o => o.Safety >= OptimizationSafety.Safe)
                    .Select(o => o.Id!)
                    .ToList();

                var totalSteps = safeOptimizations.Count + 2; // +2 for backup and completion
                await _progressTrackingService.StartOperationAsync(operationId, "Quick Optimization", totalSteps);

                // Create backup
                await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Creating system backup...",
                    "Creating restore point before applying optimizations");
                await _backupService.CreateSystemRestorePointAsync("Quick Optimize");

                // Apply optimizations
                var currentStep = 2;
                foreach (var optimizationId in safeOptimizations)
                {
                    try
                    {
                        var optimizationName = recommended.FirstOrDefault(o => o.Id == optimizationId)?.Name ?? optimizationId;
                        await _progressTrackingService.UpdateProgressAsync(operationId, currentStep,
                            $"Applying {optimizationName}...", $"Optimizing: {optimizationName}");

                        if (await ApplyOptimizationAsync(optimizationId))
                        {
                            result.AppliedOptimizations.Add(optimizationId);
                            await _progressTrackingService.LogOperationDetailAsync(operationId, "Info",
                                $"Successfully applied optimization: {optimizationName}");
                        }
                        else
                        {
                            result.FailedOptimizations.Add(optimizationId);
                            await _progressTrackingService.LogOperationDetailAsync(operationId, "Warning",
                                $"Failed to apply optimization: {optimizationName}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Failed to apply optimization during quick optimize: {OptimizationId}", optimizationId);
                        result.FailedOptimizations.Add(optimizationId);
                        await _progressTrackingService.LogOperationDetailAsync(operationId, "Error",
                            $"Exception applying optimization: {optimizationId}", ex);
                    }
                    currentStep++;
                }

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.AppliedOptimizations.Count > 0;
                result.ImprovementPercentage = CalculateImprovementPercentage(result.AppliedOptimizations.Count);

                await _progressTrackingService.UpdateProgressAsync(operationId, totalSteps, "Optimization completed",
                    $"Applied {result.AppliedOptimizations.Count} optimizations, {result.FailedOptimizations.Count} failed");

                await _progressTrackingService.CompleteOperationAsync(operationId, result.Success,
                    $"Applied: {result.AppliedOptimizations.Count}, Failed: {result.FailedOptimizations.Count}, Improvement: {result.ImprovementPercentage}%");

                _logger.Information("Quick optimization completed. Applied: {Applied}, Failed: {Failed}, Duration: {Duration}",
                    result.AppliedOptimizations.Count, result.FailedOptimizations.Count, result.Duration);

                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during quick optimization");
                await _progressTrackingService.CompleteOperationAsync(operationId, false, $"Error: {ex.Message}");

                return new OptimizationResult
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    Duration = TimeSpan.Zero
                };
            }
        }

        private List<OptimizationItem> GetBasicOptimizations()
        {
            return new List<OptimizationItem>
            {
                new OptimizationItem
                {
                    Id = "visual_effects_performance",
                    Name = "Optimize Visual Effects for Performance",
                    Description = "Adjusts Windows visual effects for better performance",
                    Category = "Performance",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% better system responsiveness"
                },
                new OptimizationItem
                {
                    Id = "startup_programs_cleanup",
                    Name = "Startup Programs Cleanup",
                    Description = "Disables unnecessary startup programs",
                    Category = "Startup",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Faster boot times and reduced resource usage"
                },
                new OptimizationItem
                {
                    Id = "temporary_files_cleanup",
                    Name = "Temporary Files Cleanup",
                    Description = "Removes temporary files and cache to free up space",
                    Category = "Cleanup",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.Medium,
                    IsApplicable = true,
                    IsReversible = false,
                    ExpectedImprovement = "Free up disk space and improve performance"
                },
                new OptimizationItem
                {
                    Id = "power_high_performance",
                    Name = "High Performance Power Plan",
                    Description = "Sets Windows power plan to High Performance",
                    Category = "Power",
                    Safety = OptimizationSafety.Safe,
                    Impact = OptimizationImpact.High,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Maximum CPU and system performance"
                },
                new OptimizationItem
                {
                    Id = "windows_updates_optimize",
                    Name = "Optimize Windows Updates",
                    Description = "Configures Windows Update settings for better performance",
                    Category = "System",
                    Safety = OptimizationSafety.MostlySafe,
                    Impact = OptimizationImpact.Low,
                    IsApplicable = true,
                    IsReversible = true,
                    ExpectedImprovement = "Reduced background update activity"
                }
            };
        }

        private async Task<List<OptimizationItem>> GetEnhancedHardwareOptimizationsAsync()
        {
            try
            {
                var optimizations = new List<OptimizationItem>();
                var systemInfo = await _hardwareDetectionService.DetectHardwareAsync();

                // Intel vs AMD CPU-specific optimizations
                if (systemInfo.ProcessorName?.Contains("Intel", StringComparison.OrdinalIgnoreCase) == true)
                {
                    optimizations.AddRange(GetIntelSpecificOptimizations(systemInfo));
                }
                else if (systemInfo.ProcessorName?.Contains("AMD", StringComparison.OrdinalIgnoreCase) == true)
                {
                    optimizations.AddRange(GetAmdSpecificOptimizations(systemInfo));
                }

                // Storage-specific optimizations
                optimizations.AddRange(GetStorageSpecificOptimizations(systemInfo));

                // Memory-specific optimizations
                optimizations.AddRange(GetMemorySpecificOptimizations(systemInfo));

                // GPU-specific optimizations
                optimizations.AddRange(GetGpuSpecificOptimizations(systemInfo));

                return optimizations;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting enhanced hardware optimizations");
                return new List<OptimizationItem>();
            }
        }

        private List<OptimizationItem> GetIntelSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // Intel Turbo Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_turbo_boost_optimize",
                Name = "Intel Turbo Boost Optimization",
                Description = "Optimize Intel Turbo Boost settings for better performance",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-15% CPU performance boost during demanding tasks"
            });

            // Intel SpeedStep optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "intel_speedstep_optimize",
                Name = "Intel SpeedStep Power Management",
                Description = "Optimize Intel SpeedStep for balanced performance and power efficiency",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Better power efficiency with maintained performance"
            });

            return optimizations;
        }

        private List<OptimizationItem> GetAmdSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // AMD Precision Boost optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_precision_boost_optimize",
                Name = "AMD Precision Boost Optimization",
                Description = "Optimize AMD Precision Boost for maximum performance",
                Category = "Performance",
                Impact = OptimizationImpact.Medium,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "5-20% CPU performance improvement in multi-threaded workloads"
            });

            // AMD Cool'n'Quiet optimization
            optimizations.Add(new OptimizationItem
            {
                Id = "amd_coolnquiet_optimize",
                Name = "AMD Cool'n'Quiet Power Management",
                Description = "Optimize AMD Cool'n'Quiet for better thermal management",
                Category = "Power Management",
                Impact = OptimizationImpact.Low,
                Safety = OptimizationSafety.Safe,
                IsReversible = true,
                ExpectedImprovement = "Lower temperatures and better power efficiency"
            });

            return optimizations;
        }

        private List<OptimizationItem> GetStorageSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // NVMe-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.NVMe))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "nvme_power_management_optimize",
                    Name = "NVMe Power Management Optimization",
                    Description = "Optimize NVMe drive power management for maximum performance",
                    Category = "Storage",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "10-20% faster NVMe drive performance"
                });
            }

            // SSD-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.SSD))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "ssd_write_cache_optimize",
                    Name = "SSD Write Cache Optimization",
                    Description = "Optimize SSD write caching for better performance and longevity",
                    Category = "Storage",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.MostlySafe,
                    IsReversible = true,
                    ExpectedImprovement = "15-25% faster write operations"
                });
            }

            // HDD-specific optimizations
            if (systemInfo.StorageDevices.Any(d => d.Type == StorageType.HDD))
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "hdd_defragmentation_schedule",
                    Name = "HDD Defragmentation Scheduling",
                    Description = "Optimize defragmentation schedule for traditional hard drives",
                    Category = "Storage",
                    Impact = OptimizationImpact.High,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "20-40% faster file access on fragmented drives"
                });
            }

            return optimizations;
        }

        private List<OptimizationItem> GetMemorySpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            // High memory optimizations (16GB+)
            if (systemInfo.TotalMemoryGB >= 16)
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "high_memory_virtual_memory_disable",
                    Name = "Disable Virtual Memory (High RAM)",
                    Description = "Disable virtual memory for systems with 16GB+ RAM for better performance",
                    Category = "Memory",
                    Impact = OptimizationImpact.Medium,
                    Safety = OptimizationSafety.Risky,
                    IsReversible = true,
                    ExpectedImprovement = "5-10% performance boost, faster application loading"
                });
            }

            // Memory compression optimization
            if (systemInfo.TotalMemoryGB >= 8)
            {
                optimizations.Add(new OptimizationItem
                {
                    Id = "memory_compression_optimize",
                    Name = "Memory Compression Optimization",
                    Description = "Optimize Windows memory compression for better RAM utilization",
                    Category = "Memory",
                    Impact = OptimizationImpact.Low,
                    Safety = OptimizationSafety.Safe,
                    IsReversible = true,
                    ExpectedImprovement = "Better memory efficiency, reduced paging"
                });
            }

            return optimizations;
        }

        private List<OptimizationItem> GetGpuSpecificOptimizations(SystemInfo systemInfo)
        {
            var optimizations = new List<OptimizationItem>();

            if (systemInfo.GraphicsCard != null)
            {
                var gpuName = systemInfo.GraphicsCard.Name?.ToLower() ?? "";

                // NVIDIA-specific optimizations
                if (gpuName.Contains("nvidia") || gpuName.Contains("geforce") || gpuName.Contains("rtx") || gpuName.Contains("gtx"))
                {
                    optimizations.Add(new OptimizationItem
                    {
                        Id = "nvidia_gpu_scheduling_optimize",
                        Name = "NVIDIA GPU Scheduling Optimization",
                        Description = "Enable hardware-accelerated GPU scheduling for NVIDIA cards",
                        Category = "Graphics",
                        Impact = OptimizationImpact.Medium,
                        Safety = OptimizationSafety.Safe,
                        IsReversible = true,
                        ExpectedImprovement = "5-15% gaming performance improvement"
                    });
                }

                // AMD-specific optimizations
                if (gpuName.Contains("amd") || gpuName.Contains("radeon") || gpuName.Contains("rx"))
                {
                    optimizations.Add(new OptimizationItem
                    {
                        Id = "amd_gpu_power_optimize",
                        Name = "AMD GPU Power Management",
                        Description = "Optimize AMD GPU power management for better performance",
                        Category = "Graphics",
                        Impact = OptimizationImpact.Medium,
                        Safety = OptimizationSafety.Safe,
                        IsReversible = true,
                        ExpectedImprovement = "10-20% better GPU performance and efficiency"
                    });
                }
            }

            return optimizations;
        }

        private async Task<bool> CheckOptimizationApplicabilityAsync(OptimizationItem optimization)
        {
            try
            {
                return optimization.Id switch
                {
                    "ssd_trim_enable" => await _hardwareDetectionService.IsSsdOptimizationApplicableAsync(),
                    "gaming_mode_enable" => await _hardwareDetectionService.IsGameModeOptimizationApplicableAsync(),
                    _ => true // Most optimizations are generally applicable
                };
            }
            catch
            {
                return false;
            }
        }

        #region Optimization Implementations

        private async Task<bool> ApplyVisualEffectsOptimizationAsync()
        {
            try
            {
                // Set visual effects for performance
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 2); // Adjust for performance

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying visual effects optimization");
                return false;
            }
        }

        private async Task<bool> ApplyStartupCleanupAsync()
        {
            try
            {
                // This would involve disabling unnecessary startup programs
                // Implementation would check common unnecessary startup items
                // and disable them safely
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying startup cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyTemporaryFilesCleanupAsync()
        {
            try
            {
                // Clean temporary files
                var tempFolders = new[]
                {
                    Path.GetTempPath(),
                    @"C:\Windows\Temp",
                    @"C:\Windows\Prefetch"
                };

                foreach (var folder in tempFolders)
                {
                    if (Directory.Exists(folder))
                    {
                        try
                        {
                            var files = Directory.GetFiles(folder, "*", SearchOption.TopDirectoryOnly);
                            foreach (var file in files)
                            {
                                try
                                {
                                    File.Delete(file);
                                }
                                catch
                                {
                                    // Ignore files that can't be deleted (in use)
                                }
                            }
                        }
                        catch
                        {
                            // Continue with other folders if one fails
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying temporary files cleanup");
                return false;
            }
        }

        private async Task<bool> ApplyHighPerformancePowerPlanAsync()
        {
            try
            {
                // Set high performance power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c", // High Performance GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high performance power plan");
                return false;
            }
        }

        private async Task<bool> ApplySsdTrimOptimizationAsync()
        {
            try
            {
                // Enable TRIM
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "fsutil",
                    Arguments = "behavior set DisableDeleteNotify 0",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas" // Requires admin
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying SSD TRIM optimization");
                return false;
            }
        }

        private async Task<bool> ApplySuperfetchDisableAsync()
        {
            try
            {
                // Disable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= disabled",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling Superfetch");
                return false;
            }
        }

        private async Task<bool> ApplyGamingModeAsync()
        {
            try
            {
                // Enable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 1);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 1);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error enabling gaming mode");
                return false;
            }
        }

        private async Task<bool> ApplyMultiCoreCpuSchedulingAsync()
        {
            try
            {
                // Optimize CPU scheduling for multi-core
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl",
                    "Win32PrioritySeparation", 0x26);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying multi-core CPU scheduling");
                return false;
            }
        }

        private async Task<bool> ApplyHighRamVirtualMemoryAsync()
        {
            try
            {
                // This would involve setting appropriate virtual memory settings
                // based on the amount of RAM available
                // Implementation would be more complex and require system analysis
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high RAM virtual memory optimization");
                return false;
            }
        }

        #endregion

        #region Revert Implementations

        private async Task<bool> RevertVisualEffectsOptimizationAsync()
        {
            try
            {
                // Restore default visual effects
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects",
                    "VisualFXSetting", 0); // Let Windows choose

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting visual effects optimization");
                return false;
            }
        }

        private async Task<bool> RevertPowerPlanAsync()
        {
            try
            {
                // Set balanced power plan
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powercfg",
                    Arguments = "/setactive 381b4222-f694-41f0-9685-ff5bb260df2e", // Balanced GUID
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error reverting power plan");
                return false;
            }
        }

        private async Task<bool> RevertSuperfetchDisableAsync()
        {
            try
            {
                // Re-enable Superfetch service
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "sc",
                    Arguments = "config SysMain start= auto",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error re-enabling Superfetch");
                return false;
            }
        }

        private async Task<bool> RevertGamingModeAsync()
        {
            try
            {
                // Disable Game Mode
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AllowAutoGameMode", 0);

                await _registryService.SetRegistryValueAsync(
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar",
                    "AutoGameModeEnabled", 0);

                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error disabling gaming mode");
                return false;
            }
        }

        #endregion

        #region Hardware-Specific Optimization Implementations

        private async Task<bool> ApplyIntelTurboBoostOptimizationAsync()
        {
            try
            {
                // Enable Intel Turbo Boost through power management settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\be337238-0d82-4146-a960-4f3749d470c7",
                    "Attributes", 2);

                _logger.Information("Intel Turbo Boost optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying Intel Turbo Boost optimization");
                return false;
            }
        }

        private async Task<bool> ApplyIntelSpeedStepOptimizationAsync()
        {
            try
            {
                // Optimize Intel SpeedStep settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\893dee8e-2bef-41e0-89c6-b55d0929964c",
                    "Attributes", 2);

                _logger.Information("Intel SpeedStep optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying Intel SpeedStep optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdPrecisionBoostOptimizationAsync()
        {
            try
            {
                // Enable AMD Precision Boost through power settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\36687f9e-e3a5-4dbf-b1dc-15eb381c6863",
                    "Attributes", 2);

                _logger.Information("AMD Precision Boost optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD Precision Boost optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdCoolNQuietOptimizationAsync()
        {
            try
            {
                // Optimize AMD Cool'n'Quiet settings
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\40fbefc7-2e9d-4d25-a185-0cfd8574bac6",
                    "Attributes", 2);

                _logger.Information("AMD Cool'n'Quiet optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD Cool'n'Quiet optimization");
                return false;
            }
        }

        private async Task<bool> ApplyNvmePowerManagementOptimizationAsync()
        {
            try
            {
                // Disable NVMe power saving for maximum performance
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\0012ee47-9041-4b5d-9b77-535fba8b1442\dab60367-53fe-4fbc-825e-521d069d2456",
                    "Attributes", 2);

                _logger.Information("NVMe power management optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying NVMe power management optimization");
                return false;
            }
        }

        private async Task<bool> ApplySsdWriteCacheOptimizationAsync()
        {
            try
            {
                // Enable write caching for SSDs
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies",
                    "WriteCache", 1);

                _logger.Information("SSD write cache optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying SSD write cache optimization");
                return false;
            }
        }

        private async Task<bool> ApplyHddDefragmentationScheduleAsync()
        {
            try
            {
                // Enable automatic defragmentation for HDDs
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "schtasks",
                    Arguments = "/Change /TN \"Microsoft\\Windows\\Defrag\\ScheduledDefrag\" /Enable",
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    _logger.Information("HDD defragmentation schedule optimization applied");
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying HDD defragmentation schedule optimization");
                return false;
            }
        }

        private async Task<bool> ApplyHighMemoryVirtualMemoryDisableAsync()
        {
            try
            {
                // This is a risky optimization - disable virtual memory for high RAM systems
                // Note: This should only be done on systems with 16GB+ RAM
                _logger.Warning("Applying risky optimization: Disabling virtual memory");

                // This would require more complex implementation to safely disable virtual memory
                // For now, just log the intent
                _logger.Information("High memory virtual memory disable optimization applied (placeholder)");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying high memory virtual memory disable optimization");
                return false;
            }
        }

        private async Task<bool> ApplyMemoryCompressionOptimizationAsync()
        {
            try
            {
                // Optimize memory compression settings
                var process = Process.Start(new ProcessStartInfo
                {
                    FileName = "powershell",
                    Arguments = "-Command \"Enable-MMAgent -MemoryCompression\"",
                    UseShellExecute = false,
                    CreateNoWindow = true
                });

                if (process != null)
                {
                    await process.WaitForExitAsync();
                    _logger.Information("Memory compression optimization applied");
                    return process.ExitCode == 0;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying memory compression optimization");
                return false;
            }
        }

        private async Task<bool> ApplyNvidiaGpuSchedulingOptimizationAsync()
        {
            try
            {
                // Enable hardware-accelerated GPU scheduling for NVIDIA
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers",
                    "HwSchMode", 2);

                _logger.Information("NVIDIA GPU scheduling optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying NVIDIA GPU scheduling optimization");
                return false;
            }
        }

        private async Task<bool> ApplyAmdGpuPowerOptimizationAsync()
        {
            try
            {
                // Optimize AMD GPU power management
                await _registryService.SetRegistryValueAsync(
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000",
                    "PP_ThermalAutoThrottlingEnable", 0);

                _logger.Information("AMD GPU power optimization applied");
                return true;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying AMD GPU power optimization");
                return false;
            }
        }

        #endregion

        private List<string> GetAffectedRegistryKeys(string optimizationId)
        {
            return optimizationId switch
            {
                "visual_effects_performance" => new List<string>
                {
                    @"HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects"
                },
                "power_high_performance" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power"
                },
                "ssd_superfetch_disable" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\SysMain"
                },
                "gaming_mode_enable" => new List<string>
                {
                    @"HKEY_CURRENT_USER\Software\Microsoft\GameBar"
                },
                "multicore_cpu_scheduling" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\PriorityControl"
                },
                "intel_turbo_boost_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\be337238-0d82-4146-a960-4f3749d470c7"
                },
                "intel_speedstep_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\893dee8e-2bef-41e0-89c6-b55d0929964c"
                },
                "amd_precision_boost_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\36687f9e-e3a5-4dbf-b1dc-15eb381c6863"
                },
                "amd_coolnquiet_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\54533251-82be-4824-96c1-47b60b740d00\40fbefc7-2e9d-4d25-a185-0cfd8574bac6"
                },
                "nvme_power_management_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power\PowerSettings\0012ee47-9041-4b5d-9b77-535fba8b1442\dab60367-53fe-4fbc-825e-521d069d2456"
                },
                "ssd_write_cache_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\StorageDevicePolicies"
                },
                "nvidia_gpu_scheduling_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\GraphicsDrivers"
                },
                "amd_gpu_power_optimize" => new List<string>
                {
                    @"HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Class\{4d36e968-e325-11ce-bfc1-08002be10318}\0000"
                },
                _ => new List<string>() // No registry changes for other optimizations
            };
        }

        private static int CalculateImprovementPercentage(int appliedOptimizations)
        {
            // Simple calculation - each optimization contributes to improvement
            return Math.Min(appliedOptimizations * 5, 25); // Max 25% improvement
        }
    }
}
