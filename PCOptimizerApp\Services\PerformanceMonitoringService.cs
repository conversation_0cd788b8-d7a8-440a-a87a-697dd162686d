using PCOptimizerApp.Models;
using Serilog;
using System.Diagnostics;
using System.IO;
using System.Windows.Threading;

namespace PCOptimizerApp.Services
{
    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ILogger _logger = Log.ForContext<PerformanceMonitoringService>();
        private readonly DispatcherTimer _monitoringTimer;
        private readonly List<PerformanceMetrics> _historicalMetrics = new();
        private bool _isMonitoring = false;

        // Enhanced performance counters
        private PerformanceCounter? _cpuCounter;
        private PerformanceCounter? _memoryCounter;
        private PerformanceCounter? _diskCounter;
        private PerformanceCounter? _diskQueueCounter;
        private PerformanceCounter? _networkBytesCounter;

        // Performance tracking
        private readonly Queue<double> _cpuHistory = new();
        private readonly Queue<double> _memoryHistory = new();
        private readonly Queue<double> _diskHistory = new();
        private const int MaxHistorySize = 30; // Keep 1 minute of history at 2-second intervals

        public event EventHandler<PerformanceMetrics>? PerformanceUpdated;

        public PerformanceMonitoringService()
        {
            _monitoringTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2) // Update every 2 seconds
            };
            _monitoringTimer.Tick += MonitoringTimer_Tick;

            InitializePerformanceCounters();
        }

        public void StartMonitoring()
        {
            if (_isMonitoring) return;

            try
            {
                _logger.Information("Starting performance monitoring");
                _isMonitoring = true;
                _monitoringTimer.Start();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error starting performance monitoring");
            }
        }

        public void StopMonitoring()
        {
            if (!_isMonitoring) return;

            try
            {
                _logger.Information("Stopping performance monitoring");
                _isMonitoring = false;
                _monitoringTimer.Stop();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error stopping performance monitoring");
            }
        }

        public async Task<PerformanceMetrics> GetCurrentMetricsAsync()
        {
            try
            {
                return await Task.Run(() =>
                {
                    var metrics = new PerformanceMetrics
                    {
                        Timestamp = DateTime.Now
                    };

                    // Get enhanced CPU usage with smoothing
                    try
                    {
                        double cpuUsage;
                        if (_cpuCounter != null)
                        {
                            cpuUsage = _cpuCounter.NextValue();
                        }
                        else
                        {
                            // Fallback method using WMI
                            cpuUsage = GetCpuUsageWmi();
                        }

                        // Add to history and smooth the value
                        _cpuHistory.Enqueue(cpuUsage);
                        if (_cpuHistory.Count > MaxHistorySize)
                            _cpuHistory.Dequeue();

                        // Use smoothed average for more stable readings
                        metrics.CpuUsagePercentage = _cpuHistory.Count > 3 ?
                            _cpuHistory.TakeLast(3).Average() : cpuUsage;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error getting CPU usage");
                        metrics.CpuUsagePercentage = 0;
                    }

                    // Get enhanced memory usage with trend analysis
                    try
                    {
                        var totalPhysicalMemory = GetTotalPhysicalMemory();
                        var availableMemory = GetAvailableMemory();

                        if (totalPhysicalMemory > 0)
                        {
                            // Both values are now in bytes, so calculation is correct
                            var usedMemory = totalPhysicalMemory - availableMemory;
                            var memoryUsage = (double)usedMemory / totalPhysicalMemory * 100;

                            // Ensure percentage is between 0 and 100
                            memoryUsage = Math.Max(0, Math.Min(100, memoryUsage));

                            // Add to history for trend analysis
                            _memoryHistory.Enqueue(memoryUsage);
                            if (_memoryHistory.Count > MaxHistorySize)
                                _memoryHistory.Dequeue();

                            metrics.MemoryUsagePercentage = memoryUsage;

                            // Log memory pressure analysis
                            if (_memoryHistory.Count >= 5)
                            {
                                var trend = AnalyzeMemoryTrend();
                                if (trend == "Increasing" && memoryUsage > 80)
                                {
                                    _logger.Warning("High memory usage detected: {Usage:F1}% with increasing trend", memoryUsage);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error getting memory usage");
                        metrics.MemoryUsagePercentage = 0;
                    }

                    // Get disk usage
                    try
                    {
                        if (_diskCounter != null)
                        {
                            metrics.DiskUsagePercentage = _diskCounter.NextValue();
                        }
                        else
                        {
                            metrics.DiskUsagePercentage = GetDiskUsage();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error getting disk usage");
                        metrics.DiskUsagePercentage = 0;
                    }

                    // Get disk speed (more relevant for optimization)
                    try
                    {
                        metrics.DiskSpeedMBps = GetDiskSpeed();
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error getting disk speed");
                        metrics.DiskSpeedMBps = 0;
                    }

                    // Get temperatures (if available)
                    try
                    {
                        var temperatures = GetTemperatures();
                        metrics.CpuTemperature = temperatures.cpuTemp;
                        metrics.GpuTemperature = temperatures.gpuTemp;
                    }
                    catch (Exception ex)
                    {
                        _logger.Warning(ex, "Error getting temperatures");
                        metrics.CpuTemperature = 0;
                        metrics.GpuTemperature = 0;
                    }

                    return metrics;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting current performance metrics");
                return new PerformanceMetrics { Timestamp = DateTime.Now };
            }
        }

        public async Task<List<PerformanceMetrics>> GetHistoricalMetricsAsync(DateTime from, DateTime to)
        {
            try
            {
                return await Task.Run(() =>
                {
                    lock (_historicalMetrics)
                    {
                        return _historicalMetrics
                            .Where(m => m.Timestamp >= from && m.Timestamp <= to)
                            .OrderBy(m => m.Timestamp)
                            .ToList();
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting historical metrics");
                return new List<PerformanceMetrics>();
            }
        }

        private async void MonitoringTimer_Tick(object? sender, EventArgs e)
        {
            if (!_isMonitoring) return;

            try
            {
                var metrics = await GetCurrentMetricsAsync();
                
                // Store in historical data
                lock (_historicalMetrics)
                {
                    _historicalMetrics.Add(metrics);
                    
                    // Keep only last 1000 entries (about 33 minutes at 2-second intervals)
                    if (_historicalMetrics.Count > 1000)
                    {
                        _historicalMetrics.RemoveAt(0);
                    }
                }

                // Notify subscribers
                PerformanceUpdated?.Invoke(this, metrics);
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error during performance monitoring tick");
            }
        }

        private void InitializePerformanceCounters()
        {
            try
            {
                // Initialize core CPU counter
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _cpuCounter.NextValue(); // First call returns 0, subsequent calls return actual values

                // Initialize memory counter
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");

                // Initialize disk counter
                _diskCounter = new PerformanceCounter("PhysicalDisk", "% Disk Time", "_Total");

                // Initialize additional performance counters for enhanced monitoring
                try
                {
                    _diskQueueCounter = new PerformanceCounter("PhysicalDisk", "Current Disk Queue Length", "_Total");
                    _diskQueueCounter.NextValue();
                }
                catch (Exception ex)
                {
                    _logger.Debug(ex, "Disk queue counter not available");
                    _diskQueueCounter = null;
                }

                try
                {
                    // Try to get network interface counter (may fail if no network interfaces)
                    var networkInterfaces = new PerformanceCounterCategory("Network Interface").GetInstanceNames()
                        .Where(name => !name.Contains("Loopback") && !name.Contains("isatap"))
                        .FirstOrDefault();

                    if (!string.IsNullOrEmpty(networkInterfaces))
                    {
                        _networkBytesCounter = new PerformanceCounter("Network Interface", "Bytes Total/sec", networkInterfaces);
                        _networkBytesCounter.NextValue();
                    }
                }
                catch (Exception ex)
                {
                    _logger.Debug(ex, "Network bytes counter not available");
                    _networkBytesCounter = null;
                }

                _logger.Information("Performance counters initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error initializing performance counters, will use fallback methods");
                // Dispose any partially created counters
                DisposeCounters();
            }
        }

        private void DisposeCounters()
        {
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _diskCounter?.Dispose();
            _diskQueueCounter?.Dispose();
            _networkBytesCounter?.Dispose();

            _cpuCounter = null;
            _memoryCounter = null;
            _diskCounter = null;
            _diskQueueCounter = null;
            _networkBytesCounter = null;
        }

        private string AnalyzeMemoryTrend()
        {
            if (_memoryHistory.Count < 5) return "Unknown";

            var recent = _memoryHistory.TakeLast(5).ToArray();
            var slope = CalculateSlope(recent);

            return slope switch
            {
                > 2.0 => "Rapidly Increasing",
                > 0.5 => "Increasing",
                < -2.0 => "Rapidly Decreasing",
                < -0.5 => "Decreasing",
                _ => "Stable"
            };
        }

        private static double CalculateSlope(double[] values)
        {
            if (values.Length < 2) return 0;

            var n = values.Length;
            var sumX = n * (n - 1) / 2.0; // Sum of indices 0,1,2,3,4
            var sumY = values.Sum();
            var sumXY = values.Select((y, x) => x * y).Sum();
            var sumX2 = Enumerable.Range(0, n).Sum(x => x * x);

            return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
        }

        private double GetCpuUsageWmi()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_Processor");
                using var collection = searcher.Get();
                
                foreach (System.Management.ManagementObject obj in collection)
                {
                    var loadPercentage = obj["LoadPercentage"];
                    if (loadPercentage != null)
                    {
                        return Convert.ToDouble(loadPercentage);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting CPU usage via WMI");
            }

            return 0;
        }

        private long GetTotalPhysicalMemory()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem");
                using var collection = searcher.Get();
                
                foreach (System.Management.ManagementObject obj in collection)
                {
                    var totalMemory = obj["TotalPhysicalMemory"];
                    if (totalMemory != null)
                    {
                        return Convert.ToInt64(totalMemory);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting total physical memory");
            }

            return 0;
        }

        private long GetAvailableMemory()
        {
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem");
                using var collection = searcher.Get();
                
                foreach (System.Management.ManagementObject obj in collection)
                {
                    var freeMemory = obj["FreePhysicalMemory"];
                    if (freeMemory != null)
                    {
                        return Convert.ToInt64(freeMemory) * 1024; // Convert from KB to bytes
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting available memory");
            }

            return 0;
        }

        private double GetDiskUsage()
        {
            try
            {
                var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
                if (drives.Any())
                {
                    var primaryDrive = drives.First();
                    return ((double)(primaryDrive.TotalSize - primaryDrive.AvailableFreeSpace) / primaryDrive.TotalSize) * 100;
                }
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting disk usage");
            }

            return 0;
        }

        private double GetDiskSpeed()
        {
            try
            {
                // Get disk performance information
                // This is a simplified implementation that estimates disk speed based on disk activity
                using var searcher = new System.Management.ManagementObjectSearcher("SELECT * FROM Win32_LogicalDisk WHERE DriveType=3");
                using var collection = searcher.Get();

                foreach (System.Management.ManagementObject disk in collection)
                {
                    var size = Convert.ToInt64(disk["Size"] ?? 0);
                    var freeSpace = Convert.ToInt64(disk["FreeSpace"] ?? 0);

                    if (size > 0)
                    {
                        // Estimate disk speed based on disk type and usage
                        // This is a placeholder - real disk speed would require performance counters
                        // or actual read/write tests

                        // Assume SSD if it's the system drive (C:) and has good performance characteristics
                        var deviceId = disk["DeviceID"]?.ToString();
                        if (deviceId == "C:")
                        {
                            // Estimate based on typical SSD/HDD speeds
                            // Real implementation would use performance counters or benchmarking
                            return EstimateDiskSpeed(size, freeSpace);
                        }
                    }
                }

                return 0;
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting disk speed");
                return 0;
            }
        }

        private static double EstimateDiskSpeed(long totalSize, long freeSpace)
        {
            // Simple heuristic to estimate disk speed
            // Real implementation would benchmark or use performance counters

            var totalSizeGB = totalSize / (1024.0 * 1024.0 * 1024.0);
            var usagePercent = ((double)(totalSize - freeSpace) / totalSize) * 100;

            // Assume modern systems likely have SSDs, especially for smaller drives
            if (totalSizeGB < 1000) // Likely SSD
            {
                // SSD speeds typically 200-500 MB/s, reduced by usage
                var baseSsdSpeed = 350.0;
                var speedReduction = usagePercent > 80 ? 0.8 : usagePercent > 60 ? 0.9 : 1.0;
                return baseSsdSpeed * speedReduction;
            }
            else // Likely HDD
            {
                // HDD speeds typically 80-150 MB/s, reduced by usage and fragmentation
                var baseHddSpeed = 120.0;
                var speedReduction = usagePercent > 80 ? 0.6 : usagePercent > 60 ? 0.8 : 0.9;
                return baseHddSpeed * speedReduction;
            }
        }

        private (double cpuTemp, double gpuTemp) GetTemperatures()
        {
            try
            {
                // Try to get real temperature data from WMI thermal zone
                double cpuTemp = GetCpuTemperatureFromWMI();
                if (cpuTemp == 0)
                {
                    // Fallback to estimated temperature based on CPU usage
                    var cpuUsage = GetCpuUsageWmi();
                    cpuTemp = EstimateTemperatureFromUsage(cpuUsage, baseTemp: 35.0, maxTemp: 75.0);
                }

                double gpuTemp = GetGpuTemperatureFromWMI();
                if (gpuTemp == 0)
                {
                    // Fallback to estimated GPU temperature
                    gpuTemp = EstimateTemperatureFromUsage(0, baseTemp: 30.0, maxTemp: 65.0);
                }

                return (cpuTemp, gpuTemp);
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error getting temperatures");
                return (0, 0);
            }
        }

        private double GetCpuTemperatureFromWMI()
        {
            // Try multiple WMI methods for temperature reading

            // Method 1: MSAcpi_ThermalZoneTemperature (most common)
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    @"root\WMI", "SELECT * FROM MSAcpi_ThermalZoneTemperature");
                using var collection = searcher.Get();

                foreach (System.Management.ManagementObject obj in collection)
                {
                    var temp = Convert.ToDouble(obj["CurrentTemperature"]);
                    // Convert from tenths of Kelvin to Celsius
                    var celsius = (temp / 10.0) - 273.15;
                    if (celsius > 0 && celsius < 150) // Sanity check
                    {
                        _logger.Information("CPU Temperature from MSAcpi_ThermalZoneTemperature: {Temperature:F1}°C", celsius);
                        return celsius;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "MSAcpi_ThermalZoneTemperature not available");
            }

            // Method 2: Win32_TemperatureProbe
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_TemperatureProbe");
                using var collection = searcher.Get();

                foreach (System.Management.ManagementObject obj in collection)
                {
                    var temp = obj["CurrentReading"];
                    if (temp != null)
                    {
                        var celsius = (Convert.ToDouble(temp) / 10.0) - 273.15;
                        if (celsius > 0 && celsius < 150) // Sanity check
                        {
                            _logger.Information("CPU Temperature from Win32_TemperatureProbe: {Temperature:F1}°C", celsius);
                            return celsius;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Win32_TemperatureProbe not available");
            }

            // Method 3: OpenHardwareMonitor WMI (if installed)
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    @"root\OpenHardwareMonitor", "SELECT * FROM Sensor WHERE SensorType='Temperature' AND Name LIKE '%CPU%'");
                using var collection = searcher.Get();

                foreach (System.Management.ManagementObject obj in collection)
                {
                    var temp = obj["Value"];
                    if (temp != null)
                    {
                        var celsius = Convert.ToDouble(temp);
                        if (celsius > 0 && celsius < 150) // Sanity check
                        {
                            _logger.Information("CPU Temperature from OpenHardwareMonitor: {Temperature:F1}°C", celsius);
                            return celsius;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "OpenHardwareMonitor WMI not available");
            }

            _logger.Information("No hardware temperature sensors available, using estimation");
            return 0;
        }

        private double GetGpuTemperatureFromWMI()
        {
            // Try multiple methods for GPU temperature reading

            // Method 1: OpenHardwareMonitor WMI (if installed)
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    @"root\OpenHardwareMonitor", "SELECT * FROM Sensor WHERE SensorType='Temperature' AND Name LIKE '%GPU%'");
                using var collection = searcher.Get();

                foreach (System.Management.ManagementObject obj in collection)
                {
                    var temp = obj["Value"];
                    if (temp != null)
                    {
                        var celsius = Convert.ToDouble(temp);
                        if (celsius > 0 && celsius < 150) // Sanity check
                        {
                            _logger.Information("GPU Temperature from OpenHardwareMonitor: {Temperature:F1}°C", celsius);
                            return celsius;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "OpenHardwareMonitor GPU temperature not available");
            }

            // Method 2: NVIDIA GPU temperature (if NVIDIA GPU present)
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_VideoController WHERE Name LIKE '%NVIDIA%'");
                using var collection = searcher.Get();

                if (collection.Count > 0)
                {
                    // NVIDIA GPU detected, but WMI doesn't provide temperature directly
                    // Would need NVIDIA Management Library (NVML) for actual temperature
                    _logger.Information("NVIDIA GPU detected, but temperature requires NVML");
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Error checking for NVIDIA GPU");
            }

            // Method 3: AMD GPU temperature (if AMD GPU present)
            try
            {
                using var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_VideoController WHERE Name LIKE '%AMD%' OR Name LIKE '%Radeon%'");
                using var collection = searcher.Get();

                if (collection.Count > 0)
                {
                    // AMD GPU detected, but WMI doesn't provide temperature directly
                    // Would need AMD Display Library (ADL) for actual temperature
                    _logger.Information("AMD GPU detected, but temperature requires ADL");
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Error checking for AMD GPU");
            }

            _logger.Information("No GPU temperature sensors available, using estimation");
            return 0;
        }

        private static double EstimateTemperatureFromUsage(double usage, double baseTemp, double maxTemp)
        {
            // Estimate temperature based on usage percentage
            // This provides realistic-looking values when actual sensors aren't available
            var tempRange = maxTemp - baseTemp;

            // Use a non-linear curve for more realistic temperature scaling
            // Temperature increases more rapidly at higher usage levels
            var normalizedUsage = usage / 100.0;
            var temperatureMultiplier = Math.Pow(normalizedUsage, 1.3); // Slightly exponential curve

            var estimatedTemp = baseTemp + (tempRange * temperatureMultiplier);

            // Add some realistic variation based on time
            var timeBasedVariation = Math.Sin(DateTime.Now.Millisecond / 1000.0 * Math.PI) * 2.0; // ±2°C variation

            var finalTemp = estimatedTemp + timeBasedVariation;

            // Ensure temperature stays within reasonable bounds
            finalTemp = Math.Max(baseTemp - 5, Math.Min(maxTemp + 10, finalTemp));

            return Math.Round(finalTemp, 1);
        }

        public void Dispose()
        {
            StopMonitoring();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _diskCounter?.Dispose();
        }
    }
}
