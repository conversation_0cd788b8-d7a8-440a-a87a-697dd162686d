using PCOptimizerApp.Models;
using Serilog;
using System.Collections.Concurrent;

namespace PCOptimizerApp.Services
{
    public class ProgressTrackingService : IProgressTrackingService
    {
        private readonly ILogger _logger = Log.ForContext<ProgressTrackingService>();
        private readonly ConcurrentDictionary<string, OperationHistory> _activeOperations = new();
        private readonly List<OperationHistory> _operationHistory = new();
        private readonly object _historyLock = new();

        public event EventHandler<ProgressUpdateEventArgs>? ProgressUpdated;

        public async Task StartOperationAsync(string operationId, string operationName, int totalSteps)
        {
            try
            {
                var operation = new OperationHistory
                {
                    OperationId = operationId,
                    OperationName = operationName,
                    StartTime = DateTime.Now,
                    Success = false
                };

                _activeOperations[operationId] = operation;

                var progressArgs = new ProgressUpdateEventArgs
                {
                    OperationId = operationId,
                    OperationName = operationName,
                    CurrentStep = 0,
                    TotalSteps = totalSteps,
                    CurrentStepDescription = "Starting operation...",
                    Details = $"Initializing {operationName}"
                };

                ProgressUpdated?.Invoke(this, progressArgs);

                await LogOperationDetailAsync(operationId, "Info", $"Started operation: {operationName} with {totalSteps} steps");
                _logger.Information("Started operation {OperationId}: {OperationName} with {TotalSteps} steps", 
                    operationId, operationName, totalSteps);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error starting operation {OperationId}: {OperationName}", operationId, operationName);
                throw;
            }
        }

        public async Task UpdateProgressAsync(string operationId, int currentStep, string currentStepDescription, string? details = null)
        {
            try
            {
                if (!_activeOperations.TryGetValue(operationId, out var operation))
                {
                    _logger.Warning("Attempted to update progress for unknown operation: {OperationId}", operationId);
                    return;
                }

                var progressArgs = new ProgressUpdateEventArgs
                {
                    OperationId = operationId,
                    OperationName = operation.OperationName,
                    CurrentStep = currentStep,
                    TotalSteps = operation.LogEntries.Count > 0 ? 
                        operation.LogEntries.Count + 10 : 10, // Estimate total steps if not known
                    CurrentStepDescription = currentStepDescription,
                    Details = details
                };

                ProgressUpdated?.Invoke(this, progressArgs);

                await LogOperationDetailAsync(operationId, "Info", 
                    $"Step {currentStep}: {currentStepDescription}", null);

                _logger.Information("Operation {OperationId} progress: Step {CurrentStep} - {Description}", 
                    operationId, currentStep, currentStepDescription);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating progress for operation {OperationId}", operationId);
            }
        }

        public async Task CompleteOperationAsync(string operationId, bool success, string? result = null)
        {
            try
            {
                if (!_activeOperations.TryRemove(operationId, out var operation))
                {
                    _logger.Warning("Attempted to complete unknown operation: {OperationId}", operationId);
                    return;
                }

                operation.EndTime = DateTime.Now;
                operation.Success = success;
                operation.Result = result;

                if (!success && string.IsNullOrEmpty(operation.ErrorMessage))
                {
                    operation.ErrorMessage = "Operation completed with errors";
                }

                // Add to history
                lock (_historyLock)
                {
                    _operationHistory.Add(operation);
                    
                    // Keep only last 100 operations
                    if (_operationHistory.Count > 100)
                    {
                        _operationHistory.RemoveAt(0);
                    }
                }

                var progressArgs = new ProgressUpdateEventArgs
                {
                    OperationId = operationId,
                    OperationName = operation.OperationName,
                    CurrentStep = operation.LogEntries.Count,
                    TotalSteps = operation.LogEntries.Count,
                    CurrentStepDescription = success ? "Operation completed successfully" : "Operation completed with errors",
                    Details = result
                };

                ProgressUpdated?.Invoke(this, progressArgs);

                await LogOperationDetailAsync(operationId, success ? "Info" : "Error", 
                    $"Operation completed. Success: {success}. Duration: {operation.Duration}");

                _logger.Information("Completed operation {OperationId}: {OperationName}. Success: {Success}, Duration: {Duration}", 
                    operationId, operation.OperationName, success, operation.Duration);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error completing operation {OperationId}", operationId);
            }
        }

        public async Task<List<OperationHistory>> GetOperationHistoryAsync()
        {
            try
            {
                await Task.CompletedTask; // Make it async for consistency

                lock (_historyLock)
                {
                    return _operationHistory.OrderByDescending(h => h.StartTime).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting operation history");
                return new List<OperationHistory>();
            }
        }

        public async Task LogOperationDetailAsync(string operationId, string level, string message, Exception? exception = null)
        {
            try
            {
                await Task.CompletedTask; // Make it async for consistency

                if (_activeOperations.TryGetValue(operationId, out var operation))
                {
                    var logEntry = new OperationLogEntry
                    {
                        Timestamp = DateTime.Now,
                        Level = level,
                        Message = message,
                        ExceptionDetails = exception?.ToString()
                    };

                    operation.LogEntries.Add(logEntry);

                    // Also log to main logger
                    switch (level.ToLower())
                    {
                        case "error":
                            _logger.Error(exception, "[{OperationId}] {Message}", operationId, message);
                            break;
                        case "warning":
                            _logger.Warning("[{OperationId}] {Message}", operationId, message);
                            break;
                        case "debug":
                            _logger.Debug("[{OperationId}] {Message}", operationId, message);
                            break;
                        default:
                            _logger.Information("[{OperationId}] {Message}", operationId, message);
                            break;
                    }

                    // Update error message if this is an error
                    if (level.ToLower() == "error")
                    {
                        operation.ErrorMessage = message;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error logging operation detail for {OperationId}", operationId);
            }
        }

        public void ClearHistory()
        {
            lock (_historyLock)
            {
                _operationHistory.Clear();
            }
            _logger.Information("Operation history cleared");
        }

        public async Task<OperationHistory?> GetOperationAsync(string operationId)
        {
            try
            {
                await Task.CompletedTask; // Make it async for consistency

                // Check active operations first
                if (_activeOperations.TryGetValue(operationId, out var activeOperation))
                {
                    return activeOperation;
                }

                // Check history
                lock (_historyLock)
                {
                    return _operationHistory.FirstOrDefault(h => h.OperationId == operationId);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting operation {OperationId}", operationId);
                return null;
            }
        }
    }
}
