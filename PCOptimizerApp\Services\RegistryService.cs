using Microsoft.Win32;
using Serilog;
using System.IO;

namespace PCOptimizerApp.Services
{
    public class RegistryService : IRegistryService
    {
        private readonly ILogger _logger = Log.ForContext<RegistryService>();

        public async Task<bool> SetRegistryValueAsync(string keyPath, string valueName, object value)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var (hive, subKey) = ParseKeyPath(keyPath);
                    using var key = hive.OpenSubKey(subKey, writable: true);
                    
                    if (key == null)
                    {
                        _logger.Warning("Registry key not found: {KeyPath}", keyPath);
                        return false;
                    }

                    key.SetValue(valueName, value);
                    _logger.Information("Set registry value: {KeyPath}\\{ValueName} = {Value}", keyPath, valueName, value);
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error setting registry value: {KeyPath}\\{ValueName}", keyPath, valueName);
                return false;
            }
        }

        public async Task<object?> GetRegistryValueAsync(string keyPath, string valueName)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var (hive, subKey) = ParseKeyPath(keyPath);
                    using var key = hive.OpenSubKey(subKey, writable: false);
                    
                    if (key == null)
                    {
                        _logger.Warning("Registry key not found: {KeyPath}", keyPath);
                        return null;
                    }

                    var value = key.GetValue(valueName);
                    _logger.Debug("Got registry value: {KeyPath}\\{ValueName} = {Value}", keyPath, valueName, value);
                    return value;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting registry value: {KeyPath}\\{ValueName}", keyPath, valueName);
                return null;
            }
        }

        public async Task<bool> DeleteRegistryValueAsync(string keyPath, string valueName)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var (hive, subKey) = ParseKeyPath(keyPath);
                    using var key = hive.OpenSubKey(subKey, writable: true);
                    
                    if (key == null)
                    {
                        _logger.Warning("Registry key not found: {KeyPath}", keyPath);
                        return false;
                    }

                    key.DeleteValue(valueName, throwOnMissingValue: false);
                    _logger.Information("Deleted registry value: {KeyPath}\\{ValueName}", keyPath, valueName);
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error deleting registry value: {KeyPath}\\{ValueName}", keyPath, valueName);
                return false;
            }
        }

        public async Task<bool> CreateRegistryKeyAsync(string keyPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var (hive, subKey) = ParseKeyPath(keyPath);
                    using var key = hive.CreateSubKey(subKey);
                    
                    _logger.Information("Created registry key: {KeyPath}", keyPath);
                    return key != null;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error creating registry key: {KeyPath}", keyPath);
                return false;
            }
        }

        public async Task<bool> DeleteRegistryKeyAsync(string keyPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var (hive, subKey) = ParseKeyPath(keyPath);
                    hive.DeleteSubKeyTree(subKey, throwOnMissingSubKey: false);
                    
                    _logger.Information("Deleted registry key: {KeyPath}", keyPath);
                    return true;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error deleting registry key: {KeyPath}", keyPath);
                return false;
            }
        }

        public async Task<bool> BackupRegistryKeyAsync(string keyPath, string backupPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    var process = new System.Diagnostics.Process
                    {
                        StartInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "reg",
                            Arguments = $"export \"{keyPath}\" \"{backupPath}\" /y",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        }
                    };

                    process.Start();
                    process.WaitForExit();

                    bool success = process.ExitCode == 0;
                    if (success)
                    {
                        _logger.Information("Backed up registry key: {KeyPath} to {BackupPath}", keyPath, backupPath);
                    }
                    else
                    {
                        _logger.Warning("Failed to backup registry key: {KeyPath}", keyPath);
                    }

                    return success;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error backing up registry key: {KeyPath}", keyPath);
                return false;
            }
        }

        public async Task<bool> RestoreRegistryKeyAsync(string backupPath, string keyPath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    if (!File.Exists(backupPath))
                    {
                        _logger.Warning("Registry backup file not found: {BackupPath}", backupPath);
                        return false;
                    }

                    var process = new System.Diagnostics.Process
                    {
                        StartInfo = new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = "reg",
                            Arguments = $"import \"{backupPath}\"",
                            UseShellExecute = false,
                            CreateNoWindow = true,
                            RedirectStandardOutput = true,
                            RedirectStandardError = true
                        }
                    };

                    process.Start();
                    process.WaitForExit();

                    bool success = process.ExitCode == 0;
                    if (success)
                    {
                        _logger.Information("Restored registry from backup: {BackupPath} to {KeyPath}", backupPath, keyPath);
                    }
                    else
                    {
                        _logger.Warning("Failed to restore registry from backup: {BackupPath}", backupPath);
                    }

                    return success;
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error restoring registry from backup: {BackupPath}", backupPath);
                return false;
            }
        }

        private (RegistryKey hive, string subKey) ParseKeyPath(string keyPath)
        {
            var parts = keyPath.Split('\\', 2);
            if (parts.Length < 2)
            {
                throw new ArgumentException($"Invalid registry key path: {keyPath}");
            }

            var hiveName = parts[0].ToUpperInvariant();
            var subKey = parts[1];

            var hive = hiveName switch
            {
                "HKEY_LOCAL_MACHINE" or "HKLM" => Registry.LocalMachine,
                "HKEY_CURRENT_USER" or "HKCU" => Registry.CurrentUser,
                "HKEY_CLASSES_ROOT" or "HKCR" => Registry.ClassesRoot,
                "HKEY_USERS" or "HKU" => Registry.Users,
                "HKEY_CURRENT_CONFIG" or "HKCC" => Registry.CurrentConfig,
                _ => throw new ArgumentException($"Unknown registry hive: {hiveName}")
            };

            return (hive, subKey);
        }
    }
}
