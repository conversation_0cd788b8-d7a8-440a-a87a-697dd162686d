using Microsoft.Extensions.DependencyInjection;

namespace PCOptimizerApp.Services
{
    /// <summary>
    /// Service locator pattern for accessing DI services from non-DI contexts like Pages
    /// </summary>
    public static class ServiceLocator
    {
        private static IServiceProvider? _serviceProvider;

        /// <summary>
        /// Initialize the service locator with the service provider
        /// </summary>
        /// <param name="serviceProvider">The service provider from the DI container</param>
        public static void Initialize(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Get a service of the specified type
        /// </summary>
        /// <typeparam name="T">The type of service to retrieve</typeparam>
        /// <returns>The service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown when ServiceLocator is not initialized</exception>
        public static T GetService<T>() where T : notnull
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("ServiceLocator is not initialized. Call Initialize() first.");
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Get a service of the specified type, or null if not found
        /// </summary>
        /// <typeparam name="T">The type of service to retrieve</typeparam>
        /// <returns>The service instance or null if not found</returns>
        public static T? GetServiceOrNull<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                return null;
            }

            return _serviceProvider.GetService<T>();
        }

        /// <summary>
        /// Check if the service locator is initialized
        /// </summary>
        public static bool IsInitialized => _serviceProvider != null;
    }
}
