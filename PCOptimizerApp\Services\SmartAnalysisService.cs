using PCOptimizerApp.Models;
using Serilog;
using System.Collections.ObjectModel;

namespace PCOptimizerApp.Services
{
    public class SmartAnalysisService : ISmartAnalysisService
    {
        private readonly ILogger _logger = Log.ForContext<SmartAnalysisService>();
        private readonly IHardwareDetectionService _hardwareDetectionService;
        private readonly ISystemInfoService _systemInfoService;
        private readonly IOptimizationService _optimizationService;
        private readonly IProgressTrackingService _progressTrackingService;

        public event EventHandler<AnalysisProgressEventArgs>? AnalysisProgressUpdated;

        public SmartAnalysisService(
            IHardwareDetectionService hardwareDetectionService,
            ISystemInfoService systemInfoService,
            IOptimizationService optimizationService,
            IProgressTrackingService progressTrackingService)
        {
            _hardwareDetectionService = hardwareDetectionService;
            _systemInfoService = systemInfoService;
            _optimizationService = optimizationService;
            _progressTrackingService = progressTrackingService;
        }

        public async Task<SmartAnalysisResult> PerformSmartAnalysisAsync()
        {
            var operationId = Guid.NewGuid().ToString();
            
            try
            {
                _logger.Information("Starting smart system analysis");
                
                await _progressTrackingService.StartOperationAsync(operationId, "Smart System Analysis", 5);
                
                var result = new SmartAnalysisResult
                {
                    AnalysisId = operationId,
                    StartTime = DateTime.Now
                };

                // Step 1: Hardware Detection
                OnAnalysisProgress(new AnalysisProgressEventArgs
                {
                    CurrentStep = 1,
                    TotalSteps = 5,
                    StepName = "Hardware Detection",
                    Description = "🔍 Analyzing your system hardware...",
                    Icon = "🔍"
                });

                await _progressTrackingService.UpdateProgressAsync(operationId, 1, "Detecting hardware capabilities...", 
                    "Scanning CPU, memory, storage, and graphics components");

                result.HardwareDetections = await DetectHardwareCapabilitiesAsync();

                // Step 2: System Information Gathering
                OnAnalysisProgress(new AnalysisProgressEventArgs
                {
                    CurrentStep = 2,
                    TotalSteps = 5,
                    StepName = "System Analysis",
                    Description = "📊 Gathering system information...",
                    Icon = "📊"
                });

                await _progressTrackingService.UpdateProgressAsync(operationId, 2, "Analyzing system configuration...", 
                    "Collecting performance metrics and system settings");

                result.SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                result.PerformanceMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();

                // Step 3: Usage Pattern Analysis
                OnAnalysisProgress(new AnalysisProgressEventArgs
                {
                    CurrentStep = 3,
                    TotalSteps = 5,
                    StepName = "Usage Analysis",
                    Description = "🎯 Analyzing usage patterns...",
                    Icon = "🎯"
                });

                await _progressTrackingService.UpdateProgressAsync(operationId, 3, "Analyzing usage patterns...", 
                    "Detecting gaming, productivity, and performance patterns");

                result.UsagePatterns = await AnalyzeUsagePatternsAsync();

                // Step 4: Generate Smart Recommendations
                OnAnalysisProgress(new AnalysisProgressEventArgs
                {
                    CurrentStep = 4,
                    TotalSteps = 5,
                    StepName = "Recommendations",
                    Description = "🚀 Generating smart recommendations...",
                    Icon = "🚀"
                });

                await _progressTrackingService.UpdateProgressAsync(operationId, 4, "Generating recommendations...", 
                    "Creating personalized optimization recommendations");

                result.Recommendations = await GenerateSmartRecommendationsAsync(result.SystemInfo);

                // Step 5: Analysis Complete
                OnAnalysisProgress(new AnalysisProgressEventArgs
                {
                    CurrentStep = 5,
                    TotalSteps = 5,
                    StepName = "Complete",
                    Description = "✅ Analysis complete!",
                    Icon = "✅"
                });

                await _progressTrackingService.UpdateProgressAsync(operationId, 5, "Analysis completed", 
                    $"Found {result.HardwareDetections.Count} hardware optimizations and {result.Recommendations.Count} recommendations");

                result.EndTime = DateTime.Now;
                result.Success = true;

                await _progressTrackingService.CompleteOperationAsync(operationId, true, 
                    $"Smart analysis completed: {result.HardwareDetections.Count} hardware detections, {result.Recommendations.Count} recommendations");

                _logger.Information("Smart analysis completed successfully. Duration: {Duration}", result.Duration);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during smart analysis");
                
                await _progressTrackingService.LogOperationDetailAsync(operationId, "Error", 
                    "Exception during smart analysis", ex);
                await _progressTrackingService.CompleteOperationAsync(operationId, false, 
                    $"Analysis failed: {ex.Message}");

                return new SmartAnalysisResult
                {
                    AnalysisId = operationId,
                    StartTime = DateTime.Now,
                    EndTime = DateTime.Now,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<List<HardwareDetectionResult>> DetectHardwareCapabilitiesAsync()
        {
            try
            {
                var detections = new List<HardwareDetectionResult>();
                var systemInfo = await _hardwareDetectionService.DetectHardwareAsync();

                // CPU Detection
                if (!string.IsNullOrEmpty(systemInfo.ProcessorName))
                {
                    var cpuDetection = new HardwareDetectionResult
                    {
                        ComponentType = "CPU",
                        ComponentName = systemInfo.ProcessorName,
                        Icon = "🔧",
                        OptimizationsEnabled = new List<string>()
                    };

                    if (systemInfo.ProcessorName.Contains("Intel", StringComparison.OrdinalIgnoreCase))
                    {
                        cpuDetection.DetectionMessage = $"✓ Detected: {systemInfo.ProcessorName} → Enabling Intel-specific optimizations";
                        cpuDetection.OptimizationsEnabled.Add("Intel Turbo Boost optimization");
                        cpuDetection.OptimizationsEnabled.Add("Intel SpeedStep power management");
                    }
                    else if (systemInfo.ProcessorName.Contains("AMD", StringComparison.OrdinalIgnoreCase))
                    {
                        cpuDetection.DetectionMessage = $"✓ Detected: {systemInfo.ProcessorName} → Enabling AMD-specific optimizations";
                        cpuDetection.OptimizationsEnabled.Add("AMD Precision Boost optimization");
                        cpuDetection.OptimizationsEnabled.Add("AMD Cool'n'Quiet power management");
                    }

                    if (systemInfo.ProcessorCores >= 4)
                    {
                        cpuDetection.OptimizationsEnabled.Add("Multi-core CPU scheduling optimization");
                    }

                    detections.Add(cpuDetection);
                }

                // Memory Detection
                if (systemInfo.TotalMemoryGB > 0)
                {
                    var memoryDetection = new HardwareDetectionResult
                    {
                        ComponentType = "Memory",
                        ComponentName = $"{systemInfo.TotalMemoryGB}GB RAM",
                        Icon = "💾",
                        DetectionMessage = $"✓ Detected: {systemInfo.TotalMemoryGB}GB RAM → Configuring optimal memory settings",
                        OptimizationsEnabled = new List<string>()
                    };

                    if (systemInfo.TotalMemoryGB >= 16)
                    {
                        memoryDetection.OptimizationsEnabled.Add("High-memory virtual memory optimization");
                        memoryDetection.OptimizationsEnabled.Add("Memory compression optimization");
                    }
                    else if (systemInfo.TotalMemoryGB >= 8)
                    {
                        memoryDetection.OptimizationsEnabled.Add("Memory compression optimization");
                    }

                    detections.Add(memoryDetection);
                }

                // Storage Detection
                foreach (var storage in systemInfo.StorageDevices)
                {
                    var storageDetection = new HardwareDetectionResult
                    {
                        ComponentType = "Storage",
                        ComponentName = $"{storage.Model} ({storage.Type})",
                        Icon = storage.Type == StorageType.SSD || storage.Type == StorageType.NVMe ? "⚡" : "💿",
                        OptimizationsEnabled = new List<string>()
                    };

                    switch (storage.Type)
                    {
                        case StorageType.NVMe:
                            storageDetection.DetectionMessage = $"✓ Detected: {storage.Model} NVMe → Adding NVMe-specific optimizations";
                            storageDetection.OptimizationsEnabled.Add("NVMe power management optimization");
                            storageDetection.OptimizationsEnabled.Add("SSD TRIM optimization");
                            storageDetection.OptimizationsEnabled.Add("Superfetch disable for SSD");
                            break;
                        case StorageType.SSD:
                            storageDetection.DetectionMessage = $"✓ Detected: {storage.Model} SSD → Adding SSD-specific optimizations";
                            storageDetection.OptimizationsEnabled.Add("SSD TRIM optimization");
                            storageDetection.OptimizationsEnabled.Add("SSD write cache optimization");
                            storageDetection.OptimizationsEnabled.Add("Superfetch disable for SSD");
                            break;
                        case StorageType.HDD:
                            storageDetection.DetectionMessage = $"✓ Detected: {storage.Model} HDD → Adding HDD-specific optimizations";
                            storageDetection.OptimizationsEnabled.Add("HDD defragmentation scheduling");
                            break;
                    }

                    detections.Add(storageDetection);
                }

                // GPU Detection
                if (systemInfo.GraphicsCard != null && !string.IsNullOrEmpty(systemInfo.GraphicsCard.Name))
                {
                    var gpuDetection = new HardwareDetectionResult
                    {
                        ComponentType = "Graphics",
                        ComponentName = systemInfo.GraphicsCard.Name,
                        Icon = "🎮",
                        OptimizationsEnabled = new List<string>()
                    };

                    var gpuName = systemInfo.GraphicsCard.Name.ToLower();
                    if (gpuName.Contains("nvidia") || gpuName.Contains("geforce") || gpuName.Contains("rtx") || gpuName.Contains("gtx"))
                    {
                        gpuDetection.DetectionMessage = $"✓ Detected: {systemInfo.GraphicsCard.Name} → Enabling NVIDIA optimizations";
                        gpuDetection.OptimizationsEnabled.Add("NVIDIA GPU scheduling optimization");
                        gpuDetection.OptimizationsEnabled.Add("Gaming mode optimization");
                    }
                    else if (gpuName.Contains("amd") || gpuName.Contains("radeon") || gpuName.Contains("rx"))
                    {
                        gpuDetection.DetectionMessage = $"✓ Detected: {systemInfo.GraphicsCard.Name} → Enabling AMD GPU optimizations";
                        gpuDetection.OptimizationsEnabled.Add("AMD GPU power optimization");
                        gpuDetection.OptimizationsEnabled.Add("Gaming mode optimization");
                    }

                    detections.Add(gpuDetection);
                }

                return detections;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error detecting hardware capabilities");
                return new List<HardwareDetectionResult>();
            }
        }

        public async Task<List<UsagePatternResult>> AnalyzeUsagePatternsAsync()
        {
            try
            {
                var patterns = new List<UsagePatternResult>();

                // Analyze running processes to detect usage patterns
                var topProcesses = await _systemInfoService.GetTopProcessesAsync(20);

                // Gaming Pattern Detection
                var gamingProcesses = topProcesses.Where(p =>
                    IsGamingProcess(p.Name) ||
                    IsGamingProcess(p.Description)).ToList();

                if (gamingProcesses.Any())
                {
                    patterns.Add(new UsagePatternResult
                    {
                        PatternType = "Gaming",
                        Icon = "🎮",
                        DetectionMessage = "✓ Detected: Gaming usage pattern → Applying gaming performance tweaks",
                        Confidence = CalculateGamingConfidence(gamingProcesses, topProcesses),
                        RecommendedOptimizations = new List<string>
                        {
                            "Gaming mode optimization",
                            "High performance power plan",
                            "Visual effects for performance",
                            "GPU-specific optimizations"
                        }
                    });
                }

                // Productivity Pattern Detection
                var productivityProcesses = topProcesses.Where(p =>
                    IsProductivityProcess(p.Name) ||
                    IsProductivityProcess(p.Description)).ToList();

                if (productivityProcesses.Any())
                {
                    patterns.Add(new UsagePatternResult
                    {
                        PatternType = "Productivity",
                        Icon = "💼",
                        DetectionMessage = "✓ Detected: Productivity usage pattern → Optimizing for efficiency",
                        Confidence = CalculateProductivityConfidence(productivityProcesses, topProcesses),
                        RecommendedOptimizations = new List<string>
                        {
                            "Startup programs cleanup",
                            "Memory optimization",
                            "Background services optimization",
                            "Power efficiency settings"
                        }
                    });
                }

                // Development Pattern Detection
                var developmentProcesses = topProcesses.Where(p =>
                    IsDevelopmentProcess(p.Name) ||
                    IsDevelopmentProcess(p.Description)).ToList();

                if (developmentProcesses.Any())
                {
                    patterns.Add(new UsagePatternResult
                    {
                        PatternType = "Development",
                        Icon = "💻",
                        DetectionMessage = "✓ Detected: Development usage pattern → Optimizing for performance",
                        Confidence = CalculateDevelopmentConfidence(developmentProcesses, topProcesses),
                        RecommendedOptimizations = new List<string>
                        {
                            "Multi-core CPU optimization",
                            "High memory optimization",
                            "SSD optimization",
                            "Background process cleanup"
                        }
                    });
                }

                return patterns;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error analyzing usage patterns");
                return new List<UsagePatternResult>();
            }
        }

        public async Task<List<OptimizationRecommendation>> GenerateSmartRecommendationsAsync(SystemInfo systemInfo)
        {
            try
            {
                var recommendations = new List<OptimizationRecommendation>();
                var availableOptimizations = await _optimizationService.GetAvailableOptimizationsAsync();

                // Prioritize recommendations based on system characteristics
                foreach (var optimization in availableOptimizations.Where(o => o.IsApplicable && !o.IsApplied))
                {
                    var recommendation = new OptimizationRecommendation
                    {
                        OptimizationId = optimization.Id ?? "",
                        Name = optimization.Name ?? "",
                        Description = optimization.Description ?? "",
                        ExpectedImprovement = optimization.ExpectedImprovement ?? "",
                        Priority = CalculateOptimizationPriority(optimization, systemInfo),
                        SafetyLevel = optimization.Safety,
                        ImpactLevel = optimization.Impact,
                        Category = optimization.Category ?? "General"
                    };

                    recommendations.Add(recommendation);
                }

                // Sort by priority (highest first)
                return recommendations.OrderByDescending(r => r.Priority).ToList();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error generating smart recommendations");
                return new List<OptimizationRecommendation>();
            }
        }

        private void OnAnalysisProgress(AnalysisProgressEventArgs args)
        {
            AnalysisProgressUpdated?.Invoke(this, args);
        }

        private static bool IsGamingProcess(string? processName)
        {
            if (string.IsNullOrEmpty(processName)) return false;

            var gamingKeywords = new[] { "steam", "game", "epic", "origin", "uplay", "battle.net", "launcher", "nvidia", "amd", "discord" };
            return gamingKeywords.Any(keyword => processName.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsProductivityProcess(string? processName)
        {
            if (string.IsNullOrEmpty(processName)) return false;

            var productivityKeywords = new[] { "office", "word", "excel", "powerpoint", "outlook", "teams", "zoom", "slack", "chrome", "firefox", "edge" };
            return productivityKeywords.Any(keyword => processName.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsDevelopmentProcess(string? processName)
        {
            if (string.IsNullOrEmpty(processName)) return false;

            var developmentKeywords = new[] { "visual studio", "code", "intellij", "eclipse", "git", "docker", "node", "python", "java", "dotnet" };
            return developmentKeywords.Any(keyword => processName.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private static double CalculateGamingConfidence(List<ProcessInfo> gamingProcesses, List<ProcessInfo> allProcesses)
        {
            if (!allProcesses.Any()) return 0;
            return Math.Min(100, (double)gamingProcesses.Count / allProcesses.Count * 100 * 2);
        }

        private static double CalculateProductivityConfidence(List<ProcessInfo> productivityProcesses, List<ProcessInfo> allProcesses)
        {
            if (!allProcesses.Any()) return 0;
            return Math.Min(100, (double)productivityProcesses.Count / allProcesses.Count * 100 * 1.5);
        }

        private static double CalculateDevelopmentConfidence(List<ProcessInfo> developmentProcesses, List<ProcessInfo> allProcesses)
        {
            if (!allProcesses.Any()) return 0;
            return Math.Min(100, (double)developmentProcesses.Count / allProcesses.Count * 100 * 3);
        }

        private static int CalculateOptimizationPriority(OptimizationItem optimization, SystemInfo systemInfo)
        {
            var priority = (int)optimization.Impact * 10 + (int)optimization.Safety * 5;

            // Boost priority for hardware-specific optimizations
            if (optimization.Id?.Contains("intel") == true && systemInfo.ProcessorName?.Contains("Intel") == true)
                priority += 20;
            if (optimization.Id?.Contains("amd") == true && systemInfo.ProcessorName?.Contains("AMD") == true)
                priority += 20;
            if (optimization.Id?.Contains("ssd") == true && systemInfo.StorageDevices.Any(d => d.Type == StorageType.SSD))
                priority += 15;
            if (optimization.Id?.Contains("nvme") == true && systemInfo.StorageDevices.Any(d => d.Type == StorageType.NVMe))
                priority += 25;

            return priority;
        }
    }
}
