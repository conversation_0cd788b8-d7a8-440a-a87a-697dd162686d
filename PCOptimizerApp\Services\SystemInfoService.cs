using System.IO;
using System.Management;
using Microsoft.Win32;
using PCOptimizerApp.Models;
using Serilog;

namespace PCOptimizerApp.Services
{
    public class SystemInfoService : ISystemInfoService
    {
        private readonly ILogger _logger = Log.ForContext<SystemInfoService>();

        public async Task<SystemInfo> GetSystemInfoAsync()
        {
            try
            {
                var systemInfo = new SystemInfo();

                await Task.Run(async () =>
                {
                    // Get OS information
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            systemInfo.OperatingSystem = obj["Caption"]?.ToString();
                            break;
                        }
                    }

                    // Get enhanced CPU information
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            systemInfo.ProcessorName = CleanCpuName(obj["Name"]?.ToString());
                            systemInfo.ProcessorCores = Convert.ToInt32(obj["NumberOfCores"] ?? 0);
                            systemInfo.ProcessorSpeedGHz = Convert.ToDouble(obj["MaxClockSpeed"] ?? 0) / 1000.0;

                            // Log additional CPU details for optimization decisions
                            var architecture = obj["Architecture"]?.ToString();
                            var family = obj["Family"]?.ToString();
                            var manufacturer = obj["Manufacturer"]?.ToString();

                            _logger.Information("CPU Details - Name: {Name}, Cores: {Cores}, Speed: {Speed}GHz, Arch: {Architecture}, Manufacturer: {Manufacturer}",
                                systemInfo.ProcessorName, systemInfo.ProcessorCores, systemInfo.ProcessorSpeedGHz, architecture, manufacturer);
                            break;
                        }
                    }

                    // Get CPU temperature (multiple methods for better compatibility)
                    systemInfo.CpuTemperature = await GetCpuTemperatureAsync();

                    // Get Memory information
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_ComputerSystem"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            var totalMemory = Convert.ToInt64(obj["TotalPhysicalMemory"] ?? 0);

                            // Convert bytes to GB using binary calculation (1024³)
                            var totalMemoryGB = totalMemory / (1024.0 * 1024.0 * 1024.0);

                            // Round to nearest GB for display purposes
                            // This helps show 16GB instead of 15.x GB for common memory sizes
                            systemInfo.TotalMemoryGB = (long)Math.Round(totalMemoryGB);

                            // If the rounded value seems too low, check for common memory sizes
                            if (totalMemoryGB > 15.5 && systemInfo.TotalMemoryGB == 15)
                            {
                                systemInfo.TotalMemoryGB = 16; // Likely 16GB with hardware reservation
                            }
                            else if (totalMemoryGB > 31.5 && systemInfo.TotalMemoryGB == 31)
                            {
                                systemInfo.TotalMemoryGB = 32; // Likely 32GB with hardware reservation
                            }
                            else if (totalMemoryGB > 7.5 && systemInfo.TotalMemoryGB == 7)
                            {
                                systemInfo.TotalMemoryGB = 8; // Likely 8GB with hardware reservation
                            }

                            break;
                        }
                    }

                    // Get available memory
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            var availableMemoryKB = Convert.ToInt64(obj["FreePhysicalMemory"] ?? 0);
                            systemInfo.AvailableMemoryGB = availableMemoryKB / (1024L * 1024L); // Convert from KB to GB
                            
                            // Fix memory usage percentage calculation using double precision
                            if (systemInfo.TotalMemoryGB > 0)
                            {
                                var totalMemoryPrecise = (double)systemInfo.TotalMemoryGB;
                                var availableMemoryPrecise = (double)systemInfo.AvailableMemoryGB;
                                var usedMemoryPrecise = totalMemoryPrecise - availableMemoryPrecise;
                                systemInfo.MemoryUsagePercentage = (usedMemoryPrecise / totalMemoryPrecise) * 100.0;
                                // Ensure percentage is between 0 and 100
                                systemInfo.MemoryUsagePercentage = Math.Max(0, Math.Min(100, systemInfo.MemoryUsagePercentage));
                            }
                            break;
                        }
                    }

                    // Get Storage devices
                    systemInfo.StorageDevices = GetStorageDevices();

                    // Get Graphics card information
                    systemInfo.GraphicsCard = GetGraphicsCardInfo();
                });

                systemInfo.LastUpdated = DateTime.Now;
                return systemInfo;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting system information");
                throw;
            }
        }

        public async Task<PerformanceMetrics> GetCurrentPerformanceMetricsAsync()
        {
            try
            {
                var metrics = new PerformanceMetrics();

                await Task.Run(() =>
                {
                    // Get CPU usage using performance counters
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_Processor"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            metrics.CpuUsagePercentage = Convert.ToDouble(obj["LoadPercentage"] ?? 0);
                            break;
                        }
                    }

                    // Get memory usage
                    using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_OperatingSystem"))
                    using (var collection = searcher.Get())
                    {
                        foreach (ManagementObject obj in collection)
                        {
                            var totalMemory = Convert.ToInt64(obj["TotalVisibleMemorySize"] ?? 0);
                            var freeMemory = Convert.ToInt64(obj["FreePhysicalMemory"] ?? 0);
                            if (totalMemory > 0)
                            {
                                metrics.MemoryUsagePercentage = ((double)(totalMemory - freeMemory) / totalMemory) * 100;
                            }
                            break;
                        }
                    }

                    // Get disk usage (simplified - could be enhanced with performance counters)
                    var drives = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
                    if (drives.Any())
                    {
                        var primaryDrive = drives.First();
                        metrics.DiskUsagePercentage = ((double)(primaryDrive.TotalSize - primaryDrive.AvailableFreeSpace) / primaryDrive.TotalSize) * 100;
                    }
                });

                metrics.Timestamp = DateTime.Now;
                return metrics;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting performance metrics");
                throw;
            }
        }

        public async Task<SystemHealthScore> CalculateSystemHealthScoreAsync()
        {
            try
            {
                var systemInfo = await GetSystemInfoAsync();
                var metrics = await GetCurrentPerformanceMetricsAsync();
                
                var healthScore = new SystemHealthScore();
                var factors = new List<HealthFactor>();

                // CPU Health Factor
                var cpuScore = metrics.CpuUsagePercentage < 50 ? 100 : 
                              metrics.CpuUsagePercentage < 80 ? 80 : 50;
                factors.Add(new HealthFactor
                {
                    Name = "CPU Performance",
                    Score = cpuScore,
                    Description = $"CPU usage: {metrics.CpuUsagePercentage:F1}%",
                    Impact = metrics.CpuUsagePercentage > 80 ? HealthImpact.High : HealthImpact.Medium
                });

                // Memory Health Factor
                var memoryScore = systemInfo.MemoryUsagePercentage < 60 ? 100 :
                                 systemInfo.MemoryUsagePercentage < 80 ? 80 : 50;
                factors.Add(new HealthFactor
                {
                    Name = "Memory Usage",
                    Score = memoryScore,
                    Description = $"RAM usage: {systemInfo.MemoryUsagePercentage:F1}%",
                    Impact = systemInfo.MemoryUsagePercentage > 80 ? HealthImpact.High : HealthImpact.Medium
                });

                // Storage Health Factor
                var storageScore = 100;
                foreach (var drive in systemInfo.StorageDevices)
                {
                    if (drive.UsagePercentage > 90)
                        storageScore = Math.Min(storageScore, 40);
                    else if (drive.UsagePercentage > 80)
                        storageScore = Math.Min(storageScore, 70);
                }
                factors.Add(new HealthFactor
                {
                    Name = "Storage Space",
                    Score = storageScore,
                    Description = "Disk space availability",
                    Impact = storageScore < 50 ? HealthImpact.High : HealthImpact.Low
                });

                healthScore.Factors = factors;
                healthScore.OverallScore = (int)factors.Average(f => f.Score);
                healthScore.Status = healthScore.OverallScore switch
                {
                    >= 90 => SystemHealthStatus.Excellent,
                    >= 80 => SystemHealthStatus.Good,
                    >= 60 => SystemHealthStatus.Fair,
                    >= 40 => SystemHealthStatus.Poor,
                    _ => SystemHealthStatus.Critical
                };

                // Generate recommendations
                healthScore.Recommendations = GenerateRecommendations(factors);

                return healthScore;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error calculating system health score");
                throw;
            }
        }

        public async Task<List<ProcessInfo>> GetTopProcessesAsync(int count = 10)
        {
            try
            {
                var processes = new List<ProcessInfo>();

                await Task.Run(() =>
                {
                    var systemProcesses = System.Diagnostics.Process.GetProcesses()
                        .Where(p => !p.HasExited)
                        .OrderByDescending(p => p.WorkingSet64)
                        .Take(count);

                    foreach (var process in systemProcesses)
                    {
                        try
                        {
                            processes.Add(new ProcessInfo
                            {
                                Name = process.ProcessName,
                                ProcessId = process.Id,
                                MemoryUsageMB = process.WorkingSet64 / (1024 * 1024),
                                Description = process.MainWindowTitle
                            });
                        }
                        catch
                        {
                            // Some processes might not be accessible
                        }
                    }
                });

                return processes;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting top processes");
                throw;
            }
        }

        public async Task<List<StartupProgram>> GetStartupProgramsAsync()
        {
            try
            {
                var startupPrograms = new List<StartupProgram>();

                await Task.Run(() =>
                {
                    // Get startup programs from registry
                    var registryKeys = new[]
                    {
                        @"SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
                        @"SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
                    };

                    foreach (var keyPath in registryKeys)
                    {
                        try
                        {
                            using var key = Registry.LocalMachine.OpenSubKey(keyPath);
                            if (key != null)
                            {
                                foreach (var valueName in key.GetValueNames())
                                {
                                    var command = key.GetValue(valueName)?.ToString();
                                    if (!string.IsNullOrEmpty(command))
                                    {
                                        startupPrograms.Add(new StartupProgram
                                        {
                                            Name = valueName,
                                            Command = command,
                                            Location = StartupLocation.Registry,
                                            IsEnabled = true,
                                            CanDisable = true,
                                            Impact = StartupImpact.Medium
                                        });
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Warning(ex, "Could not read registry key: {KeyPath}", keyPath);
                        }
                    }
                });

                return startupPrograms;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting startup programs");
                throw;
            }
        }

        private List<StorageDevice> GetStorageDevices()
        {
            var devices = new List<StorageDevice>();

            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_DiskDrive"))
                using (var collection = searcher.Get())
                {
                    foreach (ManagementObject drive in collection)
                    {
                        var device = new StorageDevice
                        {
                            Name = drive["Caption"]?.ToString(),
                            Model = drive["Model"]?.ToString(),
                            TotalSizeGB = Convert.ToInt64(drive["Size"] ?? 0) / (1024 * 1024 * 1024)
                        };

                        // Enhanced storage type detection
                        device.Type = DetectStorageType(drive, device.Model);

                        // Get health status (basic implementation)
                        device.Health = GetStorageHealth(drive);

                        // Check TRIM support for SSDs
                        device.TrimEnabled = device.Type != StorageType.HDD && CheckTrimSupport(device.Model);

                        devices.Add(device);
                    }
                }

                // Get free space for each device
                var logicalDisks = DriveInfo.GetDrives().Where(d => d.IsReady && d.DriveType == DriveType.Fixed);
                foreach (var disk in logicalDisks)
                {
                    var matchingDevice = devices.FirstOrDefault();
                    if (matchingDevice != null)
                    {
                        matchingDevice.FreeSpaceGB = disk.AvailableFreeSpace / (1024 * 1024 * 1024);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting storage devices");
            }

            return devices;
        }

        private GraphicsCard? GetGraphicsCardInfo()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_VideoController"))
                using (var collection = searcher.Get())
                {
                    GraphicsCard? bestGpu = null;
                    long maxVRam = 0;

                    foreach (ManagementObject gpu in collection)
                    {
                        var name = gpu["Name"]?.ToString();
                        if (string.IsNullOrEmpty(name) || IsIntegratedGraphics(name))
                            continue;

                        var vramBytes = Convert.ToInt64(gpu["AdapterRAM"] ?? 0);
                        var vramMB = vramBytes / (1024 * 1024);

                        // Prefer dedicated GPUs with more VRAM
                        if (vramMB > maxVRam || bestGpu == null)
                        {
                            bestGpu = new GraphicsCard
                            {
                                Name = CleanGpuName(name),
                                DriverVersion = gpu["DriverVersion"]?.ToString(),
                                VRamMB = vramMB,
                                DriverUpToDate = CheckDriverStatus(name, gpu["DriverVersion"]?.ToString())
                            };
                            maxVRam = vramMB;
                        }
                    }

                    return bestGpu;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error getting graphics card information");
            }

            return null;
        }

        private static bool IsIntegratedGraphics(string name)
        {
            var integratedKeywords = new[] { "basic", "microsoft", "intel hd", "intel uhd", "intel iris", "amd radeon graphics" };
            return integratedKeywords.Any(keyword => name.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private static string CleanGpuName(string name)
        {
            // Remove common prefixes/suffixes to clean up GPU names
            return name.Replace("NVIDIA ", "").Replace("AMD ", "").Replace("Radeon ", "").Trim();
        }

        private static bool CheckDriverStatus(string gpuName, string? driverVersion)
        {
            // Basic driver status check - in a real implementation, you'd check against latest versions
            if (string.IsNullOrEmpty(driverVersion))
                return false;

            // Assume drivers from the last year are reasonably up to date
            // This is a simplified check - real implementation would query manufacturer APIs
            return !string.IsNullOrEmpty(driverVersion) && driverVersion.Length > 5;
        }

        private static string? CleanCpuName(string? cpuName)
        {
            if (string.IsNullOrEmpty(cpuName))
                return cpuName;

            // Remove common CPU name clutter
            return cpuName
                .Replace("(R)", "")
                .Replace("(TM)", "")
                .Replace("CPU", "")
                .Replace("Processor", "")
                .Replace("  ", " ")
                .Trim();
        }

        private List<string> GenerateRecommendations(List<HealthFactor> factors)
        {
            var recommendations = new List<string>();

            foreach (var factor in factors.Where(f => f.Score < 80))
            {
                switch (factor.Name)
                {
                    case "CPU Performance":
                        recommendations.Add("Consider closing unnecessary programs to reduce CPU usage");
                        recommendations.Add("Check for background processes consuming CPU resources");
                        break;
                    case "Memory Usage":
                        recommendations.Add("Close unused applications to free up RAM");
                        recommendations.Add("Consider adding more RAM if usage is consistently high");
                        break;
                    case "Storage Space":
                        recommendations.Add("Run disk cleanup to remove temporary files");
                        recommendations.Add("Consider moving files to external storage");
                        break;
                }
            }

            return recommendations;
        }

        private StorageType DetectStorageType(ManagementObject drive, string? model)
        {
            try
            {
                var mediaType = drive["MediaType"]?.ToString()?.ToLower() ?? "";
                var interfaceType = drive["InterfaceType"]?.ToString()?.ToLower() ?? "";
                var modelLower = model?.ToLower() ?? "";

                // Check for NVMe drives
                if (modelLower.Contains("nvme") || interfaceType.Contains("nvme") ||
                    modelLower.Contains("pcie") || modelLower.Contains("m.2"))
                {
                    return StorageType.NVMe;
                }

                // Check for SSDs
                if (mediaType.Contains("ssd") || modelLower.Contains("ssd") ||
                    modelLower.Contains("solid state") || modelLower.Contains("flash"))
                {
                    return StorageType.SSD;
                }

                // Additional SSD detection patterns
                var ssdBrands = new[] { "samsung", "crucial", "intel", "kingston", "sandisk", "wd", "corsair", "adata" };
                var ssdKeywords = new[] { "evo", "pro", "ultra", "extreme", "mx", "bx", "blue", "green", "black" };

                if (ssdBrands.Any(brand => modelLower.Contains(brand)) &&
                    ssdKeywords.Any(keyword => modelLower.Contains(keyword)))
                {
                    return StorageType.SSD;
                }

                // Default to HDD for traditional drives
                return StorageType.HDD;
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Error detecting storage type for {Model}", model);
                return StorageType.Unknown;
            }
        }

        private string GetStorageHealth(ManagementObject drive)
        {
            try
            {
                // Basic health check - in a real implementation, you'd query SMART data
                var status = drive["Status"]?.ToString();
                return status?.Equals("OK", StringComparison.OrdinalIgnoreCase) == true ? "Good" : "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        private bool CheckTrimSupport(string? model)
        {
            // Basic TRIM support check - most modern SSDs support TRIM
            // In a real implementation, you'd check the actual TRIM status
            return !string.IsNullOrEmpty(model);
        }

        private async Task<double> GetCpuTemperatureAsync()
        {
            try
            {
                // Method 1: Try WMI thermal zone (Windows 10/11)
                var temperature = await TryGetTemperatureFromThermalZone();
                if (temperature > 0) return temperature;

                // Method 2: Try WMI MSAcpi_ThermalZoneTemperature (older Windows)
                temperature = await TryGetTemperatureFromMSAcpi();
                if (temperature > 0) return temperature;

                // Method 3: Try hardware-specific queries (Intel/AMD)
                temperature = await TryGetTemperatureFromHardwareSpecific();
                if (temperature > 0) return temperature;

                // Fallback: Estimate based on CPU usage
                return EstimateTemperatureFromCpuUsage();
            }
            catch (Exception ex)
            {
                _logger.Warning(ex, "Failed to get CPU temperature");
                return 0; // Return 0 if temperature cannot be determined
            }
        }

        private async Task<double> TryGetTemperatureFromThermalZone()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher(@"root\WMI", "SELECT * FROM MSAcpi_ThermalZoneTemperature");
                using var collection = searcher.Get();

                foreach (ManagementObject obj in collection)
                {
                    var temp = Convert.ToDouble(obj["CurrentTemperature"] ?? 0);
                    if (temp > 0)
                    {
                        // Convert from tenths of Kelvin to Celsius
                        var celsius = (temp / 10.0) - 273.15;
                        if (celsius > 0 && celsius < 150) // Sanity check
                        {
                            _logger.Debug("CPU temperature from thermal zone: {Temperature}°C", celsius);
                            return celsius;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Failed to get temperature from thermal zone");
            }

            await Task.CompletedTask;
            return 0;
        }

        private async Task<double> TryGetTemperatureFromMSAcpi()
        {
            try
            {
                using var searcher = new ManagementObjectSearcher("SELECT * FROM Win32_PerfRawData_Counters_ThermalZoneInformation");
                using var collection = searcher.Get();

                foreach (ManagementObject obj in collection)
                {
                    var temp = Convert.ToDouble(obj["Temperature"] ?? 0);
                    if (temp > 0)
                    {
                        // Convert from Kelvin to Celsius
                        var celsius = temp - 273.15;
                        if (celsius > 0 && celsius < 150) // Sanity check
                        {
                            _logger.Debug("CPU temperature from MSAcpi: {Temperature}°C", celsius);
                            return celsius;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Failed to get temperature from MSAcpi");
            }

            await Task.CompletedTask;
            return 0;
        }

        private async Task<double> TryGetTemperatureFromHardwareSpecific()
        {
            try
            {
                // Try Intel-specific temperature sensors
                using var searcher = new ManagementObjectSearcher(@"root\WMI", "SELECT * FROM MSAcpi_ThermalZoneTemperature");
                using var collection = searcher.Get();

                foreach (ManagementObject obj in collection)
                {
                    var instanceName = obj["InstanceName"]?.ToString();
                    if (instanceName?.Contains("CPU") == true || instanceName?.Contains("Core") == true)
                    {
                        var temp = Convert.ToDouble(obj["CurrentTemperature"] ?? 0);
                        if (temp > 0)
                        {
                            var celsius = (temp / 10.0) - 273.15;
                            if (celsius > 0 && celsius < 150)
                            {
                                _logger.Debug("CPU temperature from hardware-specific: {Temperature}°C", celsius);
                                return celsius;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Debug(ex, "Failed to get temperature from hardware-specific sensors");
            }

            await Task.CompletedTask;
            return 0;
        }

        private double EstimateTemperatureFromCpuUsage()
        {
            try
            {
                // Fallback estimation based on CPU usage
                // This is a rough estimate and not accurate, but provides some indication
                var cpuUsage = GetCurrentCpuUsage();

                // Base temperature around 35°C, increase with CPU usage
                var estimatedTemp = 35.0 + (cpuUsage * 0.5); // Rough estimation

                _logger.Debug("Estimated CPU temperature based on usage: {Temperature}°C (CPU: {Usage}%)",
                              estimatedTemp, cpuUsage);

                return estimatedTemp;
            }
            catch
            {
                return 45.0; // Default safe estimate
            }
        }

        private double GetCurrentCpuUsage()
        {
            try
            {
                // Alternative: Use PerformanceCounter for current CPU usage
                using var cpuCounter = new System.Diagnostics.PerformanceCounter("Processor", "% Processor Time", "_Total");
                cpuCounter.NextValue(); // First call returns 0
                System.Threading.Thread.Sleep(100); // Small delay
                return cpuCounter.NextValue();
            }
            catch
            {
                return 50.0; // Default moderate usage estimate
            }
        }
    }
}
