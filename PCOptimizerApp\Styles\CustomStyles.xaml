<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Custom Brushes -->
    <SolidColorBrush x:Key="AccentBrush" Color="#00D4FF"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="#33E6FF"/>
    <SolidColorBrush x:Key="SuccessBrush" Color="#00FF88"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FFAA00"/>
    <SolidColorBrush x:Key="DangerBrush" Color="#FF4757"/>
    <SolidColorBrush x:Key="BackgroundBrush" Color="#1E1E2E"/>
    <SolidColorBrush x:Key="SurfaceBrush" Color="#2D2D44"/>
    <SolidColorBrush x:Key="SurfaceHoverBrush" Color="#383859"/>
    <SolidColorBrush x:Key="TextPrimaryBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="TextSecondaryBrush" Color="#CCCCCC"/>
    <SolidColorBrush x:Key="TextDisabledBrush" Color="#999999"/>

    <!-- Card Style -->
    <Style x:Key="CardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="10"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Opacity" Value="0.95"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" BlurRadius="15" ShadowDepth="5" Opacity="0.3"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource SurfaceHoverBrush}"/>
                <Setter Property="BorderBrush" Value="{StaticResource AccentHoverBrush}"/>
                <Setter Property="Effect">
                    <Setter.Value>
                        <DropShadowEffect Color="#00D4FF" BlurRadius="20" ShadowDepth="0" Opacity="0.4"/>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Primary Button Style -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="#00D4FF" Offset="0"/>
                    <GradientStop Color="#0066CC" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            CornerRadius="8"
                            RenderTransformOrigin="0.5,0.5">
                        <Border.RenderTransform>
                            <ScaleTransform x:Name="scaleTransform" ScaleX="1" ScaleY="1"/>
                        </Border.RenderTransform>
                        <ContentPresenter x:Name="contentPresenter" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.EnterActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="scaleTransform"
                                                         Storyboard.TargetProperty="ScaleX"
                                                         To="1.05" Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="scaleTransform"
                                                         Storyboard.TargetProperty="ScaleY"
                                                         To="1.05" Duration="0:0:0.1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.EnterActions>
                            <Trigger.ExitActions>
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="scaleTransform"
                                                         Storyboard.TargetProperty="ScaleX"
                                                         To="1.0" Duration="0:0:0.1"/>
                                        <DoubleAnimation Storyboard.TargetName="scaleTransform"
                                                         Storyboard.TargetProperty="ScaleY"
                                                         To="1.0" Duration="0:0:0.1"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </Trigger.ExitActions>
                            <Setter Property="Effect" TargetName="border">
                                <Setter.Value>
                                    <DropShadowEffect Color="#00D4FF" BlurRadius="20" ShadowDepth="0" Opacity="0.6"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="RenderTransform" TargetName="border">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Secondary Button Style -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            RenderTransformOrigin="0.5,0.5">
                        <Border.RenderTransform>
                            <ScaleTransform x:Name="scaleTransform" ScaleX="1" ScaleY="1"/>
                        </Border.RenderTransform>
                        <ContentPresenter x:Name="contentPresenter" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource AccentBrush}"/>
                            <Setter Property="Opacity" TargetName="border" Value="0.2"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource AccentHoverBrush}"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="RenderTransform" TargetName="border">
                                <Setter.Value>
                                    <ScaleTransform ScaleX="0.98" ScaleY="0.98"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Navigation Button Style -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="15,12"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Margin="5,2">
                        <ContentPresenter x:Name="contentPresenter" 
                                          Focusable="False" 
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                          Margin="{TemplateBinding Padding}" 
                                          RecognizesAccessKey="True" 
                                          SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource AccentBrush}"/>
                            <Setter Property="Opacity" TargetName="border" Value="0.3"/>
                            <Setter Property="BorderBrush" TargetName="border" Value="{StaticResource AccentBrush}"/>
                            <Setter Property="BorderThickness" TargetName="border" Value="1"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="true">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource AccentBrush}"/>
                            <Setter Property="Opacity" TargetName="border" Value="0.5"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Progress Ring Style -->
    <Style x:Key="ProgressRingStyle" TargetType="Border">
        <Setter Property="Width" Value="100"/>
        <Setter Property="Height" Value="100"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="8"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="CornerRadius" Value="50"/>
        <Setter Property="Opacity" Value="0.3"/>
    </Style>

    <!-- Metric Card Style -->
    <Style x:Key="MetricCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="15"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Opacity" Value="0.8"/>
    </Style>

    <!-- Title Text Style -->
    <Style x:Key="TitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="24"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Margin" Value="0,0,0,20"/>
    </Style>

    <!-- Subtitle Text Style -->
    <Style x:Key="SubtitleTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Margin" Value="0,0,0,15"/>
    </Style>

    <!-- Body Text Style -->
    <Style x:Key="BodyTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{StaticResource TextPrimaryBrush}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="LineHeight" Value="20"/>
    </Style>

    <!-- Caption Text Style -->
    <Style x:Key="CaptionTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="{StaticResource TextSecondaryBrush}"/>
        <Setter Property="FontWeight" Value="Normal"/>
    </Style>

    <!-- Status Badge Style -->
    <Style x:Key="StatusBadgeStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="5,0"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Success Badge -->
    <Style x:Key="SuccessBadgeStyle" TargetType="Border" BasedOn="{StaticResource StatusBadgeStyle}">
        <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="Opacity" Value="0.2"/>
    </Style>

    <!-- Warning Badge -->
    <Style x:Key="WarningBadgeStyle" TargetType="Border" BasedOn="{StaticResource StatusBadgeStyle}">
        <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        <Setter Property="Opacity" Value="0.2"/>
    </Style>

    <!-- Danger Badge -->
    <Style x:Key="DangerBadgeStyle" TargetType="Border" BasedOn="{StaticResource StatusBadgeStyle}">
        <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        <Setter Property="Opacity" Value="0.2"/>
    </Style>

    <!-- Separator Style -->
    <Style x:Key="SeparatorStyle" TargetType="Rectangle">
        <Setter Property="Height" Value="1"/>
        <Setter Property="Fill" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Opacity" Value="0.3"/>
        <Setter Property="Margin" Value="0,10"/>
    </Style>

    <!-- Glass Panel Style -->
    <Style x:Key="GlassPanelStyle" TargetType="Border">
        <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
        <Setter Property="Opacity" Value="0.9"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <BlurEffect Radius="10"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Quick Action Button Style -->
    <Style x:Key="QuickActionButtonStyle" TargetType="Button">
        <Setter Property="Background" Value="{StaticResource AccentBrush}"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Padding" Value="15,10"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="border" 
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="8"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource AccentHoverBrush}"/>
                            <Setter Property="Effect" TargetName="border">
                                <Setter.Value>
                                    <DropShadowEffect Color="#00D4FF" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter Property="Background" TargetName="border" Value="#0099CC"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Background" TargetName="border" Value="{StaticResource TextDisabledBrush}"/>
                            <Setter Property="Foreground" Value="#666666"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
