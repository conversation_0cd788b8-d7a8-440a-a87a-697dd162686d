using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Diagnostics;

namespace PCOptimizerApp.Tests
{
    /// <summary>
    /// Validates the "magical" user experience and value communication
    /// Tests the premium feel, clear value proposition, and user engagement
    /// </summary>
    public class UserExperienceValidator
    {
        private readonly ILogger _logger = Log.ForContext<UserExperienceValidator>();
        private readonly ISmartAnalysisService _smartAnalysisService;

        public UserExperienceValidator()
        {
            _smartAnalysisService = ServiceLocator.GetService<ISmartAnalysisService>();
        }

        /// <summary>
        /// Test the "magical" AI-like intelligence experience
        /// Validates hardware-specific messaging and smart recommendations
        /// </summary>
        public async Task<UXTestResult> ValidateMagicalIntelligence()
        {
            var result = new UXTestResult("Magical AI-like Intelligence");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Validating magical intelligence experience");

                var analysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                // Test 1: Hardware-specific messaging
                var hardwareMessages = new List<string>();
                foreach (var detection in analysisResult.HardwareDetections)
                {
                    if (detection.DetectionMessage.Contains("Intel") || detection.DetectionMessage.Contains("AMD"))
                    {
                        hardwareMessages.Add($"✓ CPU-specific: {detection.DetectionMessage}");
                    }
                    if (detection.DetectionMessage.Contains("SSD") || detection.DetectionMessage.Contains("NVMe"))
                    {
                        hardwareMessages.Add($"✓ Storage-specific: {detection.DetectionMessage}");
                    }
                    if (detection.DetectionMessage.Contains("NVIDIA") || detection.DetectionMessage.Contains("AMD"))
                    {
                        hardwareMessages.Add($"✓ GPU-specific: {detection.DetectionMessage}");
                    }
                }

                result.AddValidation("Hardware-Specific Messaging", hardwareMessages.Count >= 2, 
                    $"Found {hardwareMessages.Count} hardware-specific messages");

                // Test 2: AI-like language patterns
                var aiLikePatterns = new[]
                {
                    "detected", "noticed", "found", "identified", "optimized", "enabled", "configured"
                };

                var aiLanguageCount = 0;
                foreach (var detection in analysisResult.HardwareDetections)
                {
                    if (aiLikePatterns.Any(pattern => detection.DetectionMessage.ToLower().Contains(pattern)))
                    {
                        aiLanguageCount++;
                    }
                }

                result.AddValidation("AI-like Language", aiLanguageCount >= 3,
                    $"Found {aiLanguageCount} AI-like language patterns");

                // Test 3: Specific optimization mentions
                var optimizationMentions = 0;
                foreach (var detection in analysisResult.HardwareDetections)
                {
                    optimizationMentions += detection.OptimizationsEnabled?.Count ?? 0;
                }

                result.AddValidation("Specific Optimizations", optimizationMentions >= 5,
                    $"Found {optimizationMentions} specific optimization mentions");

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Validations.All(v => v.Passed);

                _logger.Information("Magical intelligence validation completed. Success: {Success}", result.Success);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Magical intelligence validation failed");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        /// <summary>
        /// Test value communication effectiveness
        /// Validates clear value propositions and benefit statements
        /// </summary>
        public async Task<UXTestResult> ValidateValueCommunication()
        {
            var result = new UXTestResult("Value Communication");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Validating value communication");

                var analysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                // Test 1: Monetary value communication
                var recommendations = analysisResult.Recommendations;
                var totalValue = recommendations.Count * 15; // $15 per optimization
                
                result.AddValidation("Monetary Value", totalValue >= 100,
                    $"Equivalent PC upgrade value: ${totalValue}+");

                // Test 2: Time savings communication
                var timeSavings = recommendations.Count * 2.5; // 2.5 minutes per week per optimization
                
                result.AddValidation("Time Savings", timeSavings >= 10,
                    $"Time saved per week: {timeSavings:F1} minutes");

                // Test 3: Performance improvement claims
                var performanceImprovements = new List<string>();
                
                // Simulate performance improvements based on recommendations
                if (recommendations.Any(r => r.Category?.Contains("CPU") == true || r.Name.Contains("processor")))
                {
                    performanceImprovements.Add("15-25% faster processing");
                }
                if (recommendations.Any(r => r.Category?.Contains("Storage") == true || r.Name.Contains("SSD")))
                {
                    performanceImprovements.Add("30-50% faster boot times");
                }
                if (recommendations.Any(r => r.Category?.Contains("Memory") == true || r.Name.Contains("RAM")))
                {
                    performanceImprovements.Add("Better multitasking performance");
                }

                result.AddValidation("Performance Claims", performanceImprovements.Count >= 2,
                    $"Performance improvements: {string.Join(", ", performanceImprovements)}");

                // Test 4: Professional credibility
                var professionalOptimizations = recommendations.Count;
                
                result.AddValidation("Professional Credibility", professionalOptimizations >= 10,
                    $"Applied {professionalOptimizations} professional-grade optimizations");

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Validations.All(v => v.Passed);

                _logger.Information("Value communication validation completed. Success: {Success}", result.Success);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Value communication validation failed");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        /// <summary>
        /// Test educational experience quality
        /// Validates "Why This Matters" explanations and user education
        /// </summary>
        public async Task<UXTestResult> ValidateEducationalExperience()
        {
            var result = new UXTestResult("Educational Experience");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Validating educational experience");

                var analysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                // Test 1: Explanation quality
                var explanationKeywords = new[]
                {
                    "helps", "improves", "extends", "reduces", "optimizes", "enables", "prevents"
                };

                var qualityExplanations = 0;
                foreach (var recommendation in analysisResult.Recommendations)
                {
                    if (explanationKeywords.Any(keyword => 
                        recommendation.Description?.ToLower().Contains(keyword) == true))
                    {
                        qualityExplanations++;
                    }
                }

                result.AddValidation("Explanation Quality", qualityExplanations >= 5,
                    $"Found {qualityExplanations} quality explanations");

                // Test 2: Technical detail appropriateness
                var technicalTerms = new[]
                {
                    "TRIM", "cache", "registry", "scheduling", "compression", "optimization"
                };

                var technicalDetails = 0;
                foreach (var recommendation in analysisResult.Recommendations)
                {
                    if (technicalTerms.Any(term => 
                        recommendation.Description?.ToLower().Contains(term.ToLower()) == true))
                    {
                        technicalDetails++;
                    }
                }

                result.AddValidation("Technical Details", technicalDetails >= 3,
                    $"Found {technicalDetails} appropriate technical details");

                // Test 3: Impact clarity
                var impactStatements = 0;
                foreach (var recommendation in analysisResult.Recommendations)
                {
                    if (!string.IsNullOrEmpty(recommendation.ExpectedImprovement) &&
                        (recommendation.ExpectedImprovement.Contains("%") || 
                         recommendation.ExpectedImprovement.Contains("faster") ||
                         recommendation.ExpectedImprovement.Contains("better")))
                    {
                        impactStatements++;
                    }
                }

                result.AddValidation("Impact Clarity", impactStatements >= 5,
                    $"Found {impactStatements} clear impact statements");

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Validations.All(v => v.Passed);

                _logger.Information("Educational experience validation completed. Success: {Success}", result.Success);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Educational experience validation failed");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        /// <summary>
        /// Test trust building elements
        /// Validates transparency, safety, and credibility factors
        /// </summary>
        public async Task<UXTestResult> ValidateTrustBuilding()
        {
            var result = new UXTestResult("Trust Building");
            var stopwatch = Stopwatch.StartNew();

            try
            {
                _logger.Information("Validating trust building elements");

                var analysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                // Test 1: Safety indicators
                var safetyIndicators = new[]
                {
                    "backup", "restore", "safe", "reversible", "tested"
                };

                var safetyMentions = 0;
                foreach (var recommendation in analysisResult.Recommendations)
                {
                    if (safetyIndicators.Any(indicator => 
                        recommendation.Description?.ToLower().Contains(indicator) == true))
                    {
                        safetyMentions++;
                    }
                }

                result.AddValidation("Safety Indicators", safetyMentions >= 2,
                    $"Found {safetyMentions} safety indicators");

                // Test 2: Transparency in actions
                var transparencyScore = 0;
                foreach (var detection in analysisResult.HardwareDetections)
                {
                    if (detection.OptimizationsEnabled?.Any() == true)
                    {
                        transparencyScore += detection.OptimizationsEnabled.Count;
                    }
                }

                result.AddValidation("Action Transparency", transparencyScore >= 10,
                    $"Transparent about {transparencyScore} specific actions");

                // Test 3: Professional terminology
                var professionalTerms = new[]
                {
                    "optimization", "configuration", "performance", "efficiency", "analysis"
                };

                var professionalLanguage = 0;
                foreach (var recommendation in analysisResult.Recommendations)
                {
                    if (professionalTerms.Any(term => 
                        recommendation.Name?.ToLower().Contains(term) == true ||
                        recommendation.Description?.ToLower().Contains(term) == true))
                    {
                        professionalLanguage++;
                    }
                }

                result.AddValidation("Professional Language", professionalLanguage >= 8,
                    $"Found {professionalLanguage} professional terminology uses");

                stopwatch.Stop();
                result.Duration = stopwatch.Elapsed;
                result.Success = result.Validations.All(v => v.Passed);

                _logger.Information("Trust building validation completed. Success: {Success}", result.Success);
                return result;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Trust building validation failed");
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Duration = stopwatch.Elapsed;
                return result;
            }
        }

        /// <summary>
        /// Run all UX validation tests
        /// </summary>
        public async Task<List<UXTestResult>> RunAllValidationsAsync()
        {
            _logger.Information("Starting comprehensive UX validation suite");

            var results = new List<UXTestResult>();

            try
            {
                results.Add(await ValidateMagicalIntelligence());
                results.Add(await ValidateValueCommunication());
                results.Add(await ValidateEducationalExperience());
                results.Add(await ValidateTrustBuilding());

                var totalTests = results.Count;
                var passedTests = results.Count(r => r.Success);
                var totalValidations = results.Sum(r => r.Validations.Count);
                var passedValidations = results.Sum(r => r.Validations.Count(v => v.Passed));

                _logger.Information("UX validation suite completed. Tests: {Passed}/{Total}, Validations: {PassedValidations}/{TotalValidations}", 
                    passedTests, totalTests, passedValidations, totalValidations);

                return results;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "UX validation suite failed");
                throw;
            }
        }
    }

    public class UXTestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public List<UXValidation> Validations { get; set; } = new();
        public TimeSpan Duration { get; set; }
        public string? ErrorMessage { get; set; }

        public UXTestResult(string testName)
        {
            TestName = testName;
        }

        public void AddValidation(string validationName, bool passed, string details)
        {
            Validations.Add(new UXValidation 
            { 
                Name = validationName, 
                Passed = passed, 
                Details = details 
            });
        }
    }

    public class UXValidation
    {
        public string Name { get; set; } = string.Empty;
        public bool Passed { get; set; }
        public string Details { get; set; } = string.Empty;
    }
}
