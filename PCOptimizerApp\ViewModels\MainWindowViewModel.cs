using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using System.Collections.ObjectModel;
using System.Windows.Threading;

namespace PCOptimizerApp.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        private readonly ISystemInfoService _systemInfoService;
        private readonly IOptimizationService _optimizationService;
        private readonly IPerformanceMonitoringService _performanceMonitoringService;
        private readonly IBackupService _backupService;
        private readonly DispatcherTimer _updateTimer;

        [ObservableProperty]
        private string _currentView = "Dashboard";

        [ObservableProperty]
        private SystemInfo? _systemInfo;

        [ObservableProperty]
        private PerformanceMetrics? _currentMetrics;

        [ObservableProperty]
        private SystemHealthScore? _healthScore;

        partial void OnHealthScoreChanged(SystemHealthScore? value)
        {
            OnPropertyChanged(nameof(SystemHealthScore));
        }

        public string SystemHealthScore => HealthScore?.OverallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good", 
            >= 60 => "Fair",
            >= 40 => "Poor",
            _ => "Critical"
        };

        [ObservableProperty]
        private bool _isLoading = true;

        [ObservableProperty]
        private string _loadingMessage = "Loading system information...";

        public string SystemHealthScore => HealthScore?.OverallScore switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good", 
            >= 60 => "Fair",
            >= 40 => "Poor",
            _ => "Critical"
        };

        public ObservableCollection<NavigationItem> NavigationItems { get; } = new();

        public MainWindowViewModel(
            ISystemInfoService systemInfoService,
            IOptimizationService optimizationService,
            IPerformanceMonitoringService performanceMonitoringService,
            IBackupService backupService)
        {
            _systemInfoService = systemInfoService;
            _optimizationService = optimizationService;
            _performanceMonitoringService = performanceMonitoringService;
            _backupService = backupService;

            InitializeNavigationItems();
            
            _updateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5)
            };
            _updateTimer.Tick += UpdateTimer_Tick;

            _ = InitializeAsync();
        }

        private void InitializeNavigationItems()
        {
            NavigationItems.Clear();
            NavigationItems.Add(new NavigationItem("Dashboard", "🏠", "Dashboard"));
            NavigationItems.Add(new NavigationItem("System Analysis", "📊", "SystemAnalysis"));
            NavigationItems.Add(new NavigationItem("Quick Optimize", "⚡", "QuickOptimize"));
            NavigationItems.Add(new NavigationItem("Advanced Settings", "🔧", "AdvancedSettings"));
            NavigationItems.Add(new NavigationItem("Storage Optimizer", "💾", "StorageOptimizer"));
            NavigationItems.Add(new NavigationItem("Hardware Tweaks", "🖥️", "HardwareTweaks"));
            NavigationItems.Add(new NavigationItem("Startup Manager", "🚀", "StartupManager"));
            NavigationItems.Add(new NavigationItem("Safety & Backups", "🛡️", "SafetyBackups"));
            NavigationItems.Add(new NavigationItem("Performance History", "📈", "PerformanceHistory"));
            NavigationItems.Add(new NavigationItem("Settings", "⚙️", "Settings"));
        }

        private async Task InitializeAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Detecting hardware...";

                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                
                LoadingMessage = "Calculating system health...";
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                
                LoadingMessage = "Getting performance metrics...";
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();

                _performanceMonitoringService.PerformanceUpdated += OnPerformanceUpdated;
                _performanceMonitoringService.StartMonitoring();
                
                _updateTimer.Start();
            }
            catch (Exception ex)
            {
                // Handle error - could show error dialog
                LoadingMessage = $"Error loading system information: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OnPerformanceUpdated(object? sender, PerformanceMetrics metrics)
        {
            App.Current.Dispatcher.Invoke(() =>
            {
                CurrentMetrics = metrics;
            });
        }

        private async void UpdateTimer_Tick(object? sender, EventArgs e)
        {
            try
            {
                if (!IsLoading)
                {
                    CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
                    HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                }
            }
            catch (Exception ex)
            {
                // Log error but don't stop the timer
            }
        }

        [RelayCommand]
        private void NavigateToView(string viewName)
        {
            CurrentView = viewName;
        }

        [RelayCommand]
        private async Task QuickOptimizeAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Running quick optimization...";

                var result = await _optimizationService.RunQuickOptimizeAsync();
                
                if (result.Success)
                {
                    // Refresh system information after optimization
                    SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                    HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                }
            }
            catch (Exception ex)
            {
                // Handle error
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task RefreshSystemInfoAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Refreshing system information...";

                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
            }
            catch (Exception ex)
            {
                // Handle error
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CleanDiskAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Cleaning temporary files and disk space...";

                var result = await _optimizationService.ApplyOptimizationAsync("temporary_files_cleanup");
                
                if (result)
                {
                    LoadingMessage = "Disk cleanup completed successfully!";
                    // Refresh system information to show updated disk space
                    SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                    await Task.Delay(2000); // Show success message for 2 seconds
                }
                else
                {
                    LoadingMessage = "Disk cleanup failed. Please try again.";
                    await Task.Delay(2000);
                }
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error during disk cleanup: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task AnalyzeSystemAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Analyzing system performance and configuration...";

                // Comprehensive system analysis
                LoadingMessage = "Scanning hardware configuration...";
                await Task.Delay(1000);
                
                LoadingMessage = "Analyzing performance bottlenecks...";
                SystemInfo = await _systemInfoService.GetSystemInfoAsync();
                await Task.Delay(1000);
                
                LoadingMessage = "Calculating system health score...";
                HealthScore = await _systemInfoService.CalculateSystemHealthScoreAsync();
                await Task.Delay(1000);
                
                LoadingMessage = "Generating optimization recommendations...";
                CurrentMetrics = await _systemInfoService.GetCurrentPerformanceMetricsAsync();
                await Task.Delay(1000);
                
                LoadingMessage = "System analysis completed successfully!";
                await Task.Delay(2000);
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error during system analysis: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private async Task CreateBackupAsync()
        {
            try
            {
                IsLoading = true;
                LoadingMessage = "Creating system restore point...";

                // Add backup service dependency injection first
                var backupService = new BackupService(); // Temporary - should be injected
                var result = await backupService.CreateSystemRestorePointAsync("PC Optimizer Pro - Pre-optimization backup");
                
                if (result)
                {
                    LoadingMessage = "System restore point created successfully!";
                    await Task.Delay(2000);
                }
                else
                {
                    LoadingMessage = "Failed to create system restore point.";
                    await Task.Delay(2000);
                }
            }
            catch (Exception ex)
            {
                LoadingMessage = "Error creating backup: " + ex.Message;
                await Task.Delay(2000);
            }
            finally
            {
                IsLoading = false;
            }
        }

        public void Cleanup()
        {
            _updateTimer?.Stop();
            _performanceMonitoringService?.StopMonitoring();
            if (_performanceMonitoringService != null)
            {
                _performanceMonitoringService.PerformanceUpdated -= OnPerformanceUpdated;
            }
        }
    }

    public class NavigationItem
    {
        public string Name { get; set; }
        public string Icon { get; set; }
        public string ViewName { get; set; }

        public NavigationItem(string name, string icon, string viewName)
        {
            Name = name;
            Icon = icon;
            ViewName = viewName;
        }
    }
}
