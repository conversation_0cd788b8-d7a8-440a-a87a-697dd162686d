<Page x:Class="PCOptimizerApp.Views.HelpSystemPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Help and Documentation"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="HelpCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="HelpSectionStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="📚 Help and Documentation" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="Complete guide to PC Optimizer Pro's intelligent optimization features" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBox Name="SearchBox" Width="200" Height="30" Padding="10,5" 
                             Text="Search help topics..." Foreground="#7F8C8D"
                             GotFocus="SearchBox_GotFocus" LostFocus="SearchBox_LostFocus"/>
                    <Button Name="SearchButton" Content="🔍" Width="40" Height="30" 
                            Background="#3498DB" Foreground="White" BorderThickness="0" 
                            Margin="5,0,0,0" Click="SearchButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- Quick Start Guide -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🚀 Quick Start Guide" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="1. Smart Analysis" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Start with the Smart Analysis feature to let our AI analyze your hardware and usage patterns. 
                                    This creates a personalized optimization plan tailored to your specific system.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="2. Review Optimization Plan" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Review the categorized optimizations (CPU, Memory, Storage, Graphics) and see 
                                    exactly what will be optimized and why it matters for your PC's performance.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="3. Apply Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Watch the live optimization process with real-time explanations of what's happening 
                                    and why each optimization helps your PC perform better.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="4. View Results" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    See the measurable performance improvements with before/after metrics and 
                                    understand the value delivered (equivalent to $200+ in PC upgrades).
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Feature Explanations -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🧠 Smart Features Explained" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Hardware Detection" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Our AI automatically detects your CPU (Intel/AMD), memory, storage type (NVMe/SSD/HDD), 
                                    and graphics card to provide hardware-specific optimizations that work best for your setup.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Usage Pattern Analysis" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    We analyze your running programs to detect if you're a gamer, productivity user, 
                                    or developer, then recommend optimizations that match your usage patterns.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Intelligent Recommendations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Each recommendation is prioritized based on your hardware and usage, with clear 
                                    explanations of why it matters and what performance improvement to expect.
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Optimization Categories -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🔧 Optimization Categories" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="🔧 CPU Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    • Power plan optimization for better performance
                                    • CPU core scheduling improvements
                                    • Intel Turbo Boost or AMD Precision Boost configuration
                                    • Thermal throttling prevention
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="💾 Memory Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    • Virtual memory configuration
                                    • Memory compression settings
                                    • RAM usage optimization
                                    • Memory leak prevention
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="⚡ Storage Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    • SSD TRIM command enablement
                                    • Disk defragmentation management
                                    • Cache optimization
                                    • Superfetch and Prefetch tuning
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="🎮 Graphics Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    • Hardware-accelerated GPU scheduling
                                    • Graphics driver optimizations
                                    • Gaming performance enhancements
                                    • Video playback improvements
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Safety & Backup -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🛡️ Safety and Backup" FontSize="22" FontWeight="Bold"
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Automatic Backups" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Before applying any optimizations, PC Optimizer Pro automatically creates a 
                                    system restore point so you can easily revert changes if needed.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Safe Optimizations" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    All optimizations are thoroughly tested and use only safe, reversible changes 
                                    to Windows settings. No system files are modified or deleted.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Transparency" FontWeight="Bold" FontSize="16" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50">
                                    Every optimization shows exactly what changes are being made and why, 
                                    so you always know what's happening to your system.
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Troubleshooting -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🔧 Troubleshooting" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Q: The optimization seems stuck" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="#2C3E50">
                                    A: Some optimizations take longer on certain systems. Wait a few minutes, 
                                    or use the pause button to temporarily stop the process.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Q: I want to undo an optimization" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="#2C3E50">
                                    A: Use Windows System Restore to revert to the restore point created 
                                    before optimization, or contact support for specific reversals.
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <Border Style="{StaticResource HelpSectionStyle}">
                            <StackPanel>
                                <TextBlock Text="Q: My system feels slower after optimization" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock TextWrapping="Wrap" FontSize="12" Foreground="#2C3E50">
                                    A: Restart your computer to ensure all changes take effect. Some optimizations 
                                    require a reboot to show performance improvements.
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Contact Support -->
                <Border Style="{StaticResource HelpCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📞 Contact Support" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <TextBlock TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50" Margin="0,0,0,10">
                            Need additional help? Our support team is here to assist you with any questions 
                            about PC Optimizer Pro's features and optimizations.
                        </TextBlock>

                        <StackPanel Orientation="Horizontal">
                            <Button Name="EmailSupportButton" Content="📧 Email Support" 
                                    Background="#27AE60" Foreground="White" FontSize="14" FontWeight="Bold"
                                    Padding="15,8" BorderThickness="0" Margin="0,0,10,0" Click="EmailSupportButton_Click"/>
                            
                            <Button Name="ViewLogsButton" Content="📋 View Logs" 
                                    Background="#3498DB" Foreground="White" FontSize="14" FontWeight="Bold"
                                    Padding="15,8" BorderThickness="0" Click="ViewLogsButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
