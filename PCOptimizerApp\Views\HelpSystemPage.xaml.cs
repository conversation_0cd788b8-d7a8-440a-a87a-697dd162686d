using Serilog;
using System.Diagnostics;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace PCOptimizerApp.Views
{
    public partial class HelpSystemPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<HelpSystemPage>();
        private readonly string _defaultSearchText = "Search help topics...";

        public HelpSystemPage()
        {
            InitializeComponent();
            InitializeSearchBox();
        }

        private void InitializeSearchBox()
        {
            SearchBox.Text = _defaultSearchText;
            SearchBox.Foreground = new SolidColorBrush(Color.FromRgb(127, 140, 141)); // #7F8C8D
        }

        private void SearchBox_GotFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                if (SearchBox.Text == _defaultSearchText)
                {
                    SearchBox.Text = "";
                    SearchBox.Foreground = new SolidColorBrush(Color.FromRgb(44, 62, 80)); // #2C3E50
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling search box focus");
            }
        }

        private void SearchBox_LostFocus(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(SearchBox.Text))
                {
                    SearchBox.Text = _defaultSearchText;
                    SearchBox.Foreground = new SolidColorBrush(Color.FromRgb(127, 140, 141)); // #7F8C8D
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error handling search box lost focus");
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var searchTerm = SearchBox.Text;
                
                if (string.IsNullOrWhiteSpace(searchTerm) || searchTerm == _defaultSearchText)
                {
                    MessageBox.Show("Please enter a search term.", "Search Help", 
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                _logger.Information("User searched help for: {SearchTerm}", searchTerm);

                // Simple search implementation - highlight matching sections
                PerformHelpSearch(searchTerm);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error performing help search");
                MessageBox.Show("Error performing search. Please try again.", 
                                "Search Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PerformHelpSearch(string searchTerm)
        {
            try
            {
                // Simple search implementation - show message with search results
                var searchResults = new List<string>();

                var lowerSearchTerm = searchTerm.ToLower();

                // Search through help topics
                if (lowerSearchTerm.Contains("smart") || lowerSearchTerm.Contains("analysis"))
                {
                    searchResults.Add("Smart Analysis - AI-powered hardware detection and optimization planning");
                }

                if (lowerSearchTerm.Contains("cpu") || lowerSearchTerm.Contains("processor"))
                {
                    searchResults.Add("CPU Optimizations - Power plan, core scheduling, and thermal management");
                }

                if (lowerSearchTerm.Contains("memory") || lowerSearchTerm.Contains("ram"))
                {
                    searchResults.Add("Memory Optimizations - Virtual memory and RAM usage improvements");
                }

                if (lowerSearchTerm.Contains("storage") || lowerSearchTerm.Contains("ssd") || lowerSearchTerm.Contains("disk"))
                {
                    searchResults.Add("Storage Optimizations - SSD TRIM, defragmentation, and cache settings");
                }

                if (lowerSearchTerm.Contains("graphics") || lowerSearchTerm.Contains("gpu") || lowerSearchTerm.Contains("gaming"))
                {
                    searchResults.Add("Graphics Optimizations - GPU scheduling and gaming performance");
                }

                if (lowerSearchTerm.Contains("backup") || lowerSearchTerm.Contains("restore") || lowerSearchTerm.Contains("safety"))
                {
                    searchResults.Add("Safety & Backup - Automatic restore points and safe optimizations");
                }

                if (lowerSearchTerm.Contains("troubleshoot") || lowerSearchTerm.Contains("problem") || lowerSearchTerm.Contains("issue"))
                {
                    searchResults.Add("Troubleshooting - Common issues and solutions");
                }

                if (lowerSearchTerm.Contains("start") || lowerSearchTerm.Contains("begin") || lowerSearchTerm.Contains("quick"))
                {
                    searchResults.Add("Quick Start Guide - Step-by-step optimization process");
                }

                // Display search results
                if (searchResults.Any())
                {
                    var resultMessage = $"Found {searchResults.Count} help topic(s) for '{searchTerm}':\n\n" +
                                       string.Join("\n• ", searchResults.Select(r => "• " + r));
                    
                    MessageBox.Show(resultMessage, "Search Results", 
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show($"No help topics found for '{searchTerm}'.\n\n" +
                                   "Try searching for: smart analysis, cpu, memory, storage, graphics, backup, troubleshooting, or quick start.",
                                   "No Results", MessageBoxButton.OK, MessageBoxImage.Information);
                }

                _logger.Information("Help search for '{SearchTerm}' returned {Count} results", searchTerm, searchResults.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error in help search implementation");
            }
        }

        private void EmailSupportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Information("User requested email support");

                // Create email with system information
                var systemInfo = GetSystemInfoForSupport();
                var emailBody = Uri.EscapeDataString($"Hello PC Optimizer Pro Support,\n\n" +
                                                    $"I need assistance with:\n\n" +
                                                    $"[Please describe your issue here]\n\n" +
                                                    $"System Information:\n{systemInfo}\n\n" +
                                                    $"Thank you for your help!");

                var emailUri = $"mailto:<EMAIL>?subject=PC%20Optimizer%20Pro%20Support%20Request&body={emailBody}";

                Process.Start(new ProcessStartInfo
                {
                    FileName = emailUri,
                    UseShellExecute = true
                });

                MessageBox.Show("Your default email client will open with a pre-filled support request.", 
                                "Email Support", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error opening email support");
                
                // Fallback - copy email to clipboard
                try
                {
                    Clipboard.SetText("<EMAIL>");
                    MessageBox.Show("Could not open email client. Support email address copied to clipboard:\n\n" +
                                   "<EMAIL>", 
                                   "Email Support", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch
                {
                    MessageBox.Show("Please contact support at: <EMAIL>", 
                                    "Email Support", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void ViewLogsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Information("User requested to view logs");

                // Try to find and open the log file
                var logPaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), 
                                "PCOptimizerApp", "Logs"),
                    Path.Combine(Environment.CurrentDirectory, "Logs"),
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs")
                };

                string? logDirectory = null;
                foreach (var path in logPaths)
                {
                    if (Directory.Exists(path))
                    {
                        logDirectory = path;
                        break;
                    }
                }

                if (logDirectory != null)
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = logDirectory,
                        UseShellExecute = true
                    });

                    MessageBox.Show("Log directory opened in File Explorer.\n\n" +
                                   "Look for the most recent .log file to view application logs.", 
                                   "View Logs", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("Log directory not found. Logs may be stored in:\n\n" +
                                   "• %LocalAppData%\\PCOptimizerApp\\Logs\n" +
                                   "• Application installation directory\\Logs", 
                                   "View Logs", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error opening log directory");
                MessageBox.Show("Could not open log directory. Please check the application installation folder.", 
                                "View Logs", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GetSystemInfoForSupport()
        {
            try
            {
                var info = new System.Text.StringBuilder();
                info.AppendLine($"OS: {Environment.OSVersion}");
                info.AppendLine($"Machine: {Environment.MachineName}");
                info.AppendLine($"User: {Environment.UserName}");
                info.AppendLine($"Processor Count: {Environment.ProcessorCount}");
                info.AppendLine($"Working Set: {Environment.WorkingSet / 1024 / 1024} MB");
                info.AppendLine($"App Version: {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}");
                info.AppendLine($"CLR Version: {Environment.Version}");
                info.AppendLine($"Current Directory: {Environment.CurrentDirectory}");
                
                return info.ToString();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error gathering system info for support");
                return "System information could not be gathered.";
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Debug("Help system page unloaded");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during help page unload");
            }
        }
    }
}
