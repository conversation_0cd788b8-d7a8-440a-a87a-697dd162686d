<Page x:Class="PCOptimizerApp.Views.LiveOptimizationPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Live Optimization"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="MainCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Margin" Value="20"/>
            <Setter Property="Padding" Value="30"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="12"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ExplanationCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="0,15,0,0"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style x:Key="ProgressItemStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="0,8,0,0"/>
            <Setter Property="Padding" Value="15"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🚀 Live Optimization in Progress" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="Watch your PC get optimized in real-time with detailed explanations" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Name="OverallProgressText" Text="0%" FontSize="24" FontWeight="Bold" 
                               Foreground="#27AE60" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button Name="PauseResumeButton" Content="⏸️ Pause" 
                            Background="#E67E22" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="15,8" BorderThickness="0" Click="PauseResumeButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#D35400"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Current Optimization Card -->
                <Border Style="{StaticResource MainCardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Name="CurrentOptimizationIcon" Text="🔧" FontSize="32" Margin="0,0,15,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Name="CurrentOptimizationTitle" Text="Enabling SSD TRIM" 
                                           FontSize="24" FontWeight="Bold" Foreground="#2C3E50"/>
                                <TextBlock Name="CurrentOptimizationStatus" Text="Configuring storage optimization..." 
                                           FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>

                        <!-- Progress Bar -->
                        <ProgressBar Name="CurrentOptimizationProgress" Height="12" Background="#E9ECEF"
                                     Foreground="#3498DB" Margin="0,0,0,20"/>

                        <!-- Why This Matters Section -->
                        <Border Style="{StaticResource ExplanationCardStyle}">
                            <StackPanel>
                                <TextBlock Text="💡 Why This Matters" FontSize="16" FontWeight="Bold" 
                                           Foreground="#8E44AD" Margin="0,0,0,10"/>
                                <TextBlock Name="WhyThisMattersText" TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50"
                                           Text="TRIM helps your SSD maintain peak performance by efficiently managing deleted data blocks, extending drive lifespan by 30-40%"/>
                            </StackPanel>
                        </Border>

                        <!-- Impact Section -->
                        <Border Style="{StaticResource ExplanationCardStyle}">
                            <StackPanel>
                                <TextBlock Text="📈 Expected Impact" FontSize="16" FontWeight="Bold" 
                                           Foreground="#27AE60" Margin="0,0,0,10"/>
                                <TextBlock Name="ExpectedImpactText" TextWrapping="Wrap" FontSize="14" Foreground="#2C3E50"
                                           Text="Faster boot times, improved application loading, extended SSD lifespan"/>
                            </StackPanel>
                        </Border>

                        <!-- Technical Details Section -->
                        <Border Style="{StaticResource ExplanationCardStyle}">
                            <StackPanel>
                                <TextBlock Text="🔧 Technical Details" FontSize="16" FontWeight="Bold" 
                                           Foreground="#3498DB" Margin="0,0,0,10"/>
                                <TextBlock Name="TechnicalDetailsText" TextWrapping="Wrap" FontSize="12" Foreground="#7F8C8D"
                                           Text="Enabling TRIM command support in Windows registry and storage policies"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>

                <!-- Progress History -->
                <Border Style="{StaticResource MainCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📋 Optimization Progress" FontSize="20" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <ScrollViewer Name="ProgressHistoryScroll" MaxHeight="300" VerticalScrollBarVisibility="Auto">
                            <ItemsControl Name="ProgressHistoryList">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Style="{StaticResource ProgressItemStyle}">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" FontSize="16"
                                                           Margin="0,0,10,0" VerticalAlignment="Top"/>
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                    <TextBlock Text="{Binding Description}" FontSize="12"
                                                               Foreground="#7F8C8D" Margin="0,2,0,0" TextWrapping="Wrap"/>
                                                    <TextBlock Text="{Binding Impact}" FontSize="11"
                                                               Foreground="#27AE60" Margin="0,2,0,0"/>
                                                </StackPanel>
                                                <TextBlock Grid.Column="2" Text="{Binding Timestamp, StringFormat=HH:mm:ss}" FontSize="10"
                                                           Foreground="#BDC3C7" VerticalAlignment="Top"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Performance Metrics -->
                <Border Style="{StaticResource MainCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📊 Real-Time Performance Impact" FontSize="20" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Name="BootTimeImprovement" Text="+0%" FontSize="20" FontWeight="Bold" 
                                           Foreground="#27AE60" HorizontalAlignment="Center"/>
                                <TextBlock Text="Boot Time" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                                <TextBlock Name="BootTimeDetails" Text="45s → 45s" FontSize="10" Foreground="#BDC3C7" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Name="AppLoadImprovement" Text="+0%" FontSize="20" FontWeight="Bold" 
                                           Foreground="#3498DB" HorizontalAlignment="Center"/>
                                <TextBlock Text="App Loading" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                                <TextBlock Name="AppLoadDetails" Text="3.2s → 3.2s" FontSize="10" Foreground="#BDC3C7" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Name="MemoryImprovement" Text="+0%" FontSize="20" FontWeight="Bold" 
                                           Foreground="#9B59B6" HorizontalAlignment="Center"/>
                                <TextBlock Text="Memory Efficiency" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                                <TextBlock Name="MemoryDetails" Text="72% → 72%" FontSize="10" Foreground="#BDC3C7" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock Name="DiskImprovement" Text="+0%" FontSize="20" FontWeight="Bold" 
                                           Foreground="#E67E22" HorizontalAlignment="Center"/>
                                <TextBlock Text="Disk Performance" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                                <TextBlock Name="DiskDetails" Text="450MB/s → 450MB/s" FontSize="10" Foreground="#BDC3C7" 
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Value Communication -->
                <Border Style="{StaticResource MainCardStyle}">
                    <StackPanel>
                        <TextBlock Text="💰 Value Delivered" FontSize="20" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Name="MoneyValueText" Text="$0" FontSize="24" FontWeight="Bold" 
                                           Foreground="#27AE60" HorizontalAlignment="Center"/>
                                <TextBlock Text="Equivalent PC Upgrade Value" FontSize="12" Foreground="#7F8C8D" 
                                           HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Name="TimeSavedText" Text="0 min" FontSize="24" FontWeight="Bold" 
                                           Foreground="#3498DB" HorizontalAlignment="Center"/>
                                <TextBlock Text="Time Saved Per Week" FontSize="12" Foreground="#7F8C8D" 
                                           HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Name="OptimizationsAppliedText" Text="0" FontSize="24" FontWeight="Bold" 
                                           Foreground="#E74C3C" HorizontalAlignment="Center"/>
                                <TextBlock Text="Professional Optimizations" FontSize="12" Foreground="#7F8C8D" 
                                           HorizontalAlignment="Center" TextWrapping="Wrap"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
