using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;

namespace PCOptimizerApp.Views
{
    public partial class LiveOptimizationPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<LiveOptimizationPage>();
        private readonly IOptimizationService _optimizationService;
        private readonly IProgressTrackingService _progressTrackingService;
        private readonly DispatcherTimer _metricsUpdateTimer;

        private ObservableCollection<OptimizationProgressItem> _progressHistory = new();
        private bool _isPaused = false;
        private int _totalOptimizations = 0;
        private int _completedOptimizations = 0;
        private DateTime _startTime = DateTime.Now;

        // Performance metrics tracking
        private double _initialBootTime = 45.0;
        private double _initialAppLoad = 3.2;
        private double _initialMemoryEfficiency = 72.0;
        private double _initialDiskPerformance = 450.0;

        public LiveOptimizationPage()
        {
            InitializeComponent();
            
            // Get services from DI container
            _optimizationService = ServiceLocator.GetService<IOptimizationService>();
            _progressTrackingService = ServiceLocator.GetService<IProgressTrackingService>();

            // Set up data binding
            ProgressHistoryList.ItemsSource = _progressHistory;

            // Subscribe to progress events
            _progressTrackingService.ProgressUpdated += OnProgressUpdated;

            // Set up metrics update timer
            _metricsUpdateTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(2)
            };
            _metricsUpdateTimer.Tick += UpdateMetrics;
            _metricsUpdateTimer.Start();

            // Initialize UI
            InitializeOptimizationDisplay();
        }

        private void InitializeOptimizationDisplay()
        {
            try
            {
                // Set initial state
                CurrentOptimizationTitle.Text = "Preparing optimization...";
                CurrentOptimizationStatus.Text = "Analyzing system and preparing optimization plan";
                CurrentOptimizationIcon.Text = "🔍";
                CurrentOptimizationProgress.Value = 0;

                WhyThisMattersText.Text = "We're analyzing your system to create a personalized optimization plan that will improve performance, extend hardware lifespan, and enhance your overall PC experience.";
                ExpectedImpactText.Text = "Faster boot times, improved application responsiveness, better system stability";
                TechnicalDetailsText.Text = "Scanning hardware configuration and system settings";

                // Initialize metrics
                UpdatePerformanceMetrics();
                UpdateValueMetrics();

                _logger.Information("Live optimization display initialized");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error initializing optimization display");
            }
        }

        private void OnProgressUpdated(object? sender, ProgressUpdateEventArgs e)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    // Update current optimization display
                    CurrentOptimizationTitle.Text = e.CurrentStepDescription;
                    CurrentOptimizationStatus.Text = e.Details ?? "Processing...";
                    CurrentOptimizationProgress.Value = e.ProgressPercentage;
                    OverallProgressText.Text = $"{e.ProgressPercentage:F0}%";

                    // Update explanations based on current step
                    UpdateExplanationsForCurrentStep(e.CurrentStepDescription);

                    // Add to progress history
                    var progressItem = new OptimizationProgressItem
                    {
                        Name = e.CurrentStepDescription,
                        Description = e.Details ?? "",
                        Impact = GetExpectedImpactForStep(e.CurrentStepDescription),
                        StatusIcon = e.ProgressPercentage >= 100 ? "✅" : "🔄",
                        Timestamp = DateTime.Now
                    };

                    _progressHistory.Insert(0, progressItem);

                    // Keep only last 20 items
                    while (_progressHistory.Count > 20)
                    {
                        _progressHistory.RemoveAt(_progressHistory.Count - 1);
                    }

                    // Auto-scroll to top
                    ProgressHistoryScroll.ScrollToTop();

                    _logger.Debug("Progress updated: {Step} - {Progress}%", e.CurrentStepDescription, e.ProgressPercentage);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error updating progress UI");
                }
            });
        }

        private void UpdateExplanationsForCurrentStep(string stepDescription)
        {
            var step = stepDescription.ToLower();

            if (step.Contains("ssd") || step.Contains("trim"))
            {
                CurrentOptimizationIcon.Text = "⚡";
                WhyThisMattersText.Text = "TRIM helps your SSD maintain peak performance by efficiently managing deleted data blocks, extending drive lifespan by 30-40% and preventing performance degradation over time.";
                ExpectedImpactText.Text = "Faster boot times, improved application loading, extended SSD lifespan, consistent performance";
                TechnicalDetailsText.Text = "Enabling TRIM command support in Windows registry and storage policies";
            }
            else if (step.Contains("cpu") || step.Contains("processor"))
            {
                CurrentOptimizationIcon.Text = "🔧";
                WhyThisMattersText.Text = "CPU optimizations improve processing efficiency by enabling advanced features like Turbo Boost and optimizing core scheduling, resulting in better performance for demanding applications.";
                ExpectedImpactText.Text = "15-25% faster processing, reduced latency, better multitasking performance";
                TechnicalDetailsText.Text = "Configuring CPU power management and scheduling policies";
            }
            else if (step.Contains("memory") || step.Contains("ram"))
            {
                CurrentOptimizationIcon.Text = "💾";
                WhyThisMattersText.Text = "Memory optimizations reduce RAM usage and improve allocation efficiency, allowing your system to run more programs simultaneously without slowdowns.";
                ExpectedImpactText.Text = "Better multitasking, reduced memory pressure, faster application switching";
                TechnicalDetailsText.Text = "Optimizing virtual memory settings and memory compression";
            }
            else if (step.Contains("graphics") || step.Contains("gpu"))
            {
                CurrentOptimizationIcon.Text = "🎮";
                WhyThisMattersText.Text = "Graphics optimizations enable hardware-accelerated features and improve GPU scheduling, resulting in smoother gaming and better visual performance.";
                ExpectedImpactText.Text = "5-15% gaming performance improvement, smoother video playback, reduced input lag";
                TechnicalDetailsText.Text = "Enabling GPU hardware scheduling and graphics optimizations";
            }
            else if (step.Contains("startup") || step.Contains("boot"))
            {
                CurrentOptimizationIcon.Text = "🚀";
                WhyThisMattersText.Text = "Startup optimizations reduce boot time by disabling unnecessary programs and services that automatically start with Windows, freeing up system resources.";
                ExpectedImpactText.Text = "30-50% faster boot times, quicker desktop readiness, more available memory";
                TechnicalDetailsText.Text = "Disabling unnecessary startup programs and services";
            }
            else
            {
                CurrentOptimizationIcon.Text = "⚙️";
                WhyThisMattersText.Text = "System optimizations fine-tune various Windows settings to improve overall performance, stability, and user experience.";
                ExpectedImpactText.Text = "General performance improvements, better system responsiveness";
                TechnicalDetailsText.Text = "Applying system-wide performance optimizations";
            }
        }

        private string GetExpectedImpactForStep(string stepDescription)
        {
            var step = stepDescription.ToLower();

            if (step.Contains("ssd") || step.Contains("trim"))
                return "Extended SSD lifespan, consistent performance";
            else if (step.Contains("cpu") || step.Contains("processor"))
                return "15-25% faster processing";
            else if (step.Contains("memory") || step.Contains("ram"))
                return "Better multitasking performance";
            else if (step.Contains("graphics") || step.Contains("gpu"))
                return "5-15% gaming performance boost";
            else if (step.Contains("startup") || step.Contains("boot"))
                return "30-50% faster boot times";
            else
                return "General performance improvement";
        }

        private void UpdateMetrics(object? sender, EventArgs e)
        {
            try
            {
                UpdatePerformanceMetrics();
                UpdateValueMetrics();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error updating metrics");
            }
        }

        private void UpdatePerformanceMetrics()
        {
            // Simulate performance improvements based on completed optimizations
            var improvementFactor = Math.Min(_completedOptimizations * 0.05, 0.3); // Max 30% improvement

            var currentBootTime = _initialBootTime * (1 - improvementFactor);
            var currentAppLoad = _initialAppLoad * (1 - improvementFactor);
            var currentMemoryEfficiency = _initialMemoryEfficiency * (1 + improvementFactor * 0.5);
            var currentDiskPerformance = _initialDiskPerformance * (1 + improvementFactor);

            var bootImprovement = ((_initialBootTime - currentBootTime) / _initialBootTime) * 100;
            var appImprovement = ((_initialAppLoad - currentAppLoad) / _initialAppLoad) * 100;
            var memoryImprovement = ((currentMemoryEfficiency - _initialMemoryEfficiency) / _initialMemoryEfficiency) * 100;
            var diskImprovement = ((currentDiskPerformance - _initialDiskPerformance) / _initialDiskPerformance) * 100;

            BootTimeImprovement.Text = $"+{bootImprovement:F0}%";
            AppLoadImprovement.Text = $"+{appImprovement:F0}%";
            MemoryImprovement.Text = $"+{memoryImprovement:F0}%";
            DiskImprovement.Text = $"+{diskImprovement:F0}%";

            BootTimeDetails.Text = $"{_initialBootTime:F0}s → {currentBootTime:F0}s";
            AppLoadDetails.Text = $"{_initialAppLoad:F1}s → {currentAppLoad:F1}s";
            MemoryDetails.Text = $"{_initialMemoryEfficiency:F0}% → {currentMemoryEfficiency:F0}%";
            DiskDetails.Text = $"{_initialDiskPerformance:F0}MB/s → {currentDiskPerformance:F0}MB/s";
        }

        private void UpdateValueMetrics()
        {
            var moneyValue = _completedOptimizations * 15; // $15 per optimization
            var timeSaved = _completedOptimizations * 2.5; // 2.5 minutes per week per optimization

            MoneyValueText.Text = $"${moneyValue}";
            TimeSavedText.Text = $"{timeSaved:F0} min";
            OptimizationsAppliedText.Text = _completedOptimizations.ToString();
        }

        private void PauseResumeButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _isPaused = !_isPaused;
                
                if (_isPaused)
                {
                    PauseResumeButton.Content = "▶️ Resume";
                    CurrentOptimizationStatus.Text = "Optimization paused by user";
                    _metricsUpdateTimer.Stop();
                    _logger.Information("Optimization paused by user");
                }
                else
                {
                    PauseResumeButton.Content = "⏸️ Pause";
                    CurrentOptimizationStatus.Text = "Resuming optimization...";
                    _metricsUpdateTimer.Start();
                    _logger.Information("Optimization resumed by user");
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error toggling pause/resume");
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Cleanup
                _progressTrackingService.ProgressUpdated -= OnProgressUpdated;
                _metricsUpdateTimer?.Stop();
                _logger.Debug("Live optimization page unloaded");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during page unload");
            }
        }

        public void SetOptimizationCounts(int total, int completed)
        {
            _totalOptimizations = total;
            _completedOptimizations = completed;
            UpdateValueMetrics();
        }
    }

    public class OptimizationProgressItem
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Impact { get; set; } = string.Empty;
        public string StatusIcon { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
