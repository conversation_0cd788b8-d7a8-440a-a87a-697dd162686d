<Window x:Class="PCOptimizerApp.Views.MainWindow"
           xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:ui="http://schemas.modernwpf.com/2019"
           xmlns:vm="clr-namespace:PCOptimizerApp.ViewModels"
           Title="PC Optimizer Pro" 
           Height="800" 
           Width="1200"
           MinHeight="600"
           MinWidth="900"
           WindowStartupLocation="CenterScreen"
           ui:WindowHelper.UseModernWindowStyle="True"
           Background="{StaticResource BackgroundBrush}">
    
    <Window.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Navigation Item Template -->
        <DataTemplate x:Key="NavigationItemTemplate">
            <Button Command="{Binding DataContext.NavigateToViewCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                    CommandParameter="{Binding ViewName}"
                    Style="{StaticResource NavigationButtonStyle}"
                    Margin="0,2">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="{Binding Icon}" 
                               FontSize="16" 
                               Margin="0,0,10,0" 
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Name}" 
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Button>
        </DataTemplate>

        <!-- Navigation Button Style -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6"
                                Margin="5,2">
                            <ContentPresenter x:Name="contentPresenter" 
                                              Focusable="False" 
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" 
                                              Margin="{TemplateBinding Padding}" 
                                              RecognizesAccessKey="True" 
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" 
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter Property="Background" TargetName="border" Value="{StaticResource AccentBrush}"/>
                                <Setter Property="Opacity" TargetName="border" Value="0.3"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="true">
                                <Setter Property="Background" TargetName="border" Value="{StaticResource AccentBrush}"/>
                                <Setter Property="Opacity" TargetName="border" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Card Style -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource SurfaceBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource AccentBrush}"/>
            <Setter Property="Opacity" Value="0.9"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" BlurRadius="10" ShadowDepth="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Quick Action Button Style -->
        <Style x:Key="QuickActionButtonStyle" TargetType="Button">
            <Setter Property="Background">
                <Setter.Value>
                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                        <GradientStop Color="#FF00D4FF" Offset="0"/>
                        <GradientStop Color="#FF0066CC" Offset="1"/>
                    </LinearGradientBrush>
                </Setter.Value>
            </Setter>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="border" 
                                Background="{TemplateBinding Background}"
                                CornerRadius="6">
                            <ContentPresenter x:Name="contentPresenter" 
                                              HorizontalAlignment="Center" 
                                              VerticalAlignment="Center"
                                              Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="true">
                                <Setter Property="RenderTransform" TargetName="border">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Effect" TargetName="border">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#FF00D4FF" BlurRadius="15" ShadowDepth="0" Opacity="0.6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid>
        <!-- Loading Overlay -->
        <Border Background="#80000000" 
                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                Panel.ZIndex="1000">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <ui:ProgressRing IsActive="True" 
                                 Width="60" 
                                 Height="60" 
                                 Foreground="{StaticResource AccentBrush}"/>
                <TextBlock Text="{Binding LoadingMessage}" 
                           Foreground="White" 
                           FontSize="16" 
                           Margin="0,20,0,0" 
                           HorizontalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Main Layout -->
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="280"/>
            </Grid.ColumnDefinitions>

            <!-- Left Sidebar Navigation -->
            <Border Grid.Column="0" 
                    Background="{StaticResource SurfaceBrush}" 
                    BorderThickness="0,0,1,0" 
                    BorderBrush="{StaticResource AccentBrush}" 
                    Opacity="0.95">
                <StackPanel>
                    <!-- Logo -->
                    <Border Padding="20" Margin="0,0,0,20">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⚡" 
                                       FontSize="32" 
                                       HorizontalAlignment="Center" 
                                       Foreground="{StaticResource AccentBrush}"/>
                            <TextBlock Text="PC Optimizer Pro" 
                                       FontSize="18" 
                                       FontWeight="Bold" 
                                       HorizontalAlignment="Center" 
                                       Foreground="{StaticResource AccentBrush}" 
                                       Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- Navigation Items -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="10">
                        <ItemsControl ItemsSource="{Binding NavigationItems}"
                                      ItemTemplate="{StaticResource NavigationItemTemplate}"/>
                    </ScrollViewer>
                </StackPanel>
            </Border>

            <!-- Main Content Area -->
            <ScrollViewer Grid.Column="1" 
                          VerticalScrollBarVisibility="Auto" 
                          HorizontalScrollBarVisibility="Disabled" 
                          Padding="20">
                <StackPanel>
                    <!-- Top Status Bar -->
                    <Border Style="{StaticResource CardStyle}" Margin="0,0,0,20">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- System Health Score -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="System Health:" 
                                           FontSize="16" 
                                           Foreground="White" 
                                           VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding HealthScore.OverallScore, StringFormat='{}{0}/100'}" 
                                           FontSize="24" 
                                           FontWeight="Bold" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           Margin="10,0,0,0" 
                                           VerticalAlignment="Center"/>
                                <Ellipse Width="12" 
                                         Height="12" 
                                         Fill="{StaticResource SuccessBrush}" 
                                         Margin="10,0,0,0" 
                                         VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding HealthScore.Status}" 
                                           FontSize="16" 
                                           Foreground="{StaticResource SuccessBrush}" 
                                           Margin="5,0,0,0" 
                                           VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Safety Status -->
                            <Border Grid.Column="1" 
                                    Background="{StaticResource SuccessBrush}" 
                                    CornerRadius="15" 
                                    Padding="10,5" 
                                    Opacity="0.2">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🛡️" FontSize="14" VerticalAlignment="Center"/>
                                    <TextBlock Text="Backup Ready" 
                                               FontSize="14" 
                                               Foreground="White" 
                                               Margin="5,0,0,0" 
                                               VerticalAlignment="Center"/>
                                    <TextBlock Text="✓" 
                                               FontSize="14" 
                                               Foreground="{StaticResource SuccessBrush}" 
                                               Margin="5,0,0,0" 
                                               VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- Quick Actions -->
                    <UniformGrid Columns="4" Margin="0,0,0,20">
                        <Button Content="🚀 Quick Optimize" 
                                Style="{StaticResource QuickActionButtonStyle}"
                                Command="{Binding QuickOptimizeCommand}"/>
                        <Button Content="🧹 Clean Disk" 
                                Style="{StaticResource QuickActionButtonStyle}"/>
                        <Button Content="📊 Analyze System" 
                                Style="{StaticResource QuickActionButtonStyle}"
                                Command="{Binding RefreshSystemInfoCommand}"/>
                        <Button Content="🛡️ Create Backup" 
                                Style="{StaticResource QuickActionButtonStyle}"/>
                    </UniformGrid>

                    <!-- Dashboard Content -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- System Performance Card -->
                        <Border Grid.Column="0" Grid.Row="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="System Performance" 
                                           FontSize="18" 
                                           FontWeight="SemiBold" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           Margin="0,0,0,15"/>
                                
                                <UniformGrid Columns="2">
                                    <!-- CPU Usage -->
                                    <StackPanel HorizontalAlignment="Center" Margin="10">
                                        <Ellipse Width="80" Height="80" Stroke="{StaticResource AccentBrush}" StrokeThickness="6" Fill="Transparent"/>
                                        <TextBlock Text="{Binding CurrentMetrics.CpuUsagePercentage, StringFormat='{}{0:F1}%'}" 
                                                   HorizontalAlignment="Center" 
                                                   VerticalAlignment="Center" 
                                                   FontSize="16" 
                                                   FontWeight="Bold" 
                                                   Foreground="White" 
                                                   Margin="0,-50,0,0"/>
                                        <TextBlock Text="CPU Usage" 
                                                   HorizontalAlignment="Center" 
                                                   FontSize="12" 
                                                   Foreground="#CCCCCC" 
                                                   Margin="0,25,0,0"/>
                                    </StackPanel>

                                    <!-- RAM Usage -->
                                    <StackPanel HorizontalAlignment="Center" Margin="10">
                                        <Ellipse Width="80" Height="80" Stroke="{StaticResource SuccessBrush}" StrokeThickness="6" Fill="Transparent"/>
                                        <TextBlock Text="{Binding SystemInfo.MemoryUsagePercentage, StringFormat='{}{0:F1}%'}" 
                                                   HorizontalAlignment="Center" 
                                                   VerticalAlignment="Center" 
                                                   FontSize="16" 
                                                   FontWeight="Bold" 
                                                   Foreground="White" 
                                                   Margin="0,-50,0,0"/>
                                        <TextBlock Text="RAM Usage" 
                                                   HorizontalAlignment="Center" 
                                                   FontSize="12" 
                                                   Foreground="#CCCCCC" 
                                                   Margin="0,25,0,0"/>
                                    </StackPanel>
                                </UniformGrid>
                            </StackPanel>
                        </Border>

                        <!-- Hardware Detection Card -->
                        <Border Grid.Column="1" Grid.Row="0" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="Detected Hardware" 
                                           FontSize="18" 
                                           FontWeight="SemiBold" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           Margin="0,0,0,15"/>
                                
                                <UniformGrid Columns="2" Rows="2">
                                    <!-- Storage -->
                                    <StackPanel HorizontalAlignment="Center" Margin="5">
                                        <TextBlock Text="💿" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="Storage" 
                                                   FontSize="12" 
                                                   FontWeight="SemiBold" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="White"/>
                                        <TextBlock Text="{Binding SystemInfo.StorageDevices[0].Name}" 
                                                   FontSize="10" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="#CCCCCC" 
                                                   TextWrapping="Wrap"/>
                                    </StackPanel>

                                    <!-- CPU -->
                                    <StackPanel HorizontalAlignment="Center" Margin="5">
                                        <TextBlock Text="🖥️" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="Processor" 
                                                   FontSize="12" 
                                                   FontWeight="SemiBold" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="White"/>
                                        <TextBlock Text="{Binding SystemInfo.ProcessorName}" 
                                                   FontSize="10" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="#CCCCCC" 
                                                   TextWrapping="Wrap"/>
                                    </StackPanel>

                                    <!-- RAM -->
                                    <StackPanel HorizontalAlignment="Center" Margin="5">
                                        <TextBlock Text="💾" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="Memory" 
                                                   FontSize="12" 
                                                   FontWeight="SemiBold" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="White"/>
                                        <TextBlock Text="{Binding SystemInfo.TotalMemoryGB, StringFormat='{}{0}GB RAM'}" 
                                                   FontSize="10" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="#CCCCCC"/>
                                    </StackPanel>

                                    <!-- GPU -->
                                    <StackPanel HorizontalAlignment="Center" Margin="5">
                                        <TextBlock Text="🎮" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                        <TextBlock Text="Graphics" 
                                                   FontSize="12" 
                                                   FontWeight="SemiBold" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="White"/>
                                        <TextBlock Text="{Binding SystemInfo.GraphicsCard.Name}" 
                                                   FontSize="10" 
                                                   HorizontalAlignment="Center" 
                                                   Foreground="#CCCCCC" 
                                                   TextWrapping="Wrap"/>
                                    </StackPanel>
                                </UniformGrid>
                            </StackPanel>
                        </Border>

                        <!-- Recent Activity Card -->
                        <Border Grid.Column="0" Grid.Row="1" Style="{StaticResource CardStyle}">
                            <StackPanel>
                                <TextBlock Text="Recent Optimizations" 
                                           FontSize="18" 
                                           FontWeight="SemiBold" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           Margin="0,0,0,15"/>
                                
                                <!-- Sample Activity Items -->
                                <StackPanel>
                                    <Border Background="Transparent" Padding="0,5" BorderThickness="0,0,0,1" BorderBrush="#333">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <Ellipse Grid.Column="0" Width="24" Height="24" Fill="{StaticResource SuccessBrush}" Margin="0,0,10,0"/>
                                            <TextBlock Grid.Column="0" Text="✅" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="SSD Optimization Completed" FontSize="12" Foreground="White"/>
                                                <TextBlock Text="15% boot time improvement" FontSize="10" Foreground="#CCCCCC"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="2 min ago" FontSize="10" Foreground="#999" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </Border>

                        <!-- Removed Smart Recommendations Card - will be integrated into main optimization flow -->
                    </Grid>
                </StackPanel>
            </ScrollViewer>

            <!-- Right Panel - Real-time Monitoring -->
            <Border Grid.Column="2" 
                    Background="{StaticResource SurfaceBrush}" 
                    BorderThickness="1,0,0,0" 
                    BorderBrush="{StaticResource AccentBrush}" 
                    Opacity="0.95">
                <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="20">
                    <StackPanel>
                        <TextBlock Text="Real-time Monitoring" 
                                   FontSize="16" 
                                   FontWeight="SemiBold" 
                                   Foreground="{StaticResource AccentBrush}" 
                                   Margin="0,0,0,20"/>

                        <!-- System Metrics -->
                        <StackPanel Margin="0,0,0,30">
                            <TextBlock Text="System Metrics" 
                                       FontSize="14" 
                                       FontWeight="SemiBold" 
                                       Foreground="White" 
                                       Margin="0,0,0,15"/>
                            
                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="CPU Temperature" Foreground="#CCCCCC"/>
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding CurrentMetrics.CpuTemperature, StringFormat='{}{0:F0}°C'}" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           FontWeight="Bold"/>
                            </Grid>

                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Available RAM" Foreground="#CCCCCC"/>
                                <TextBlock Grid.Column="1" 
                                           Text="{Binding SystemInfo.AvailableMemoryGB, StringFormat='{}{0}GB'}" 
                                           Foreground="{StaticResource AccentBrush}" 
                                           FontWeight="Bold"/>
                            </Grid>

                            <Grid Margin="0,0,0,10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="System Health" Foreground="#CCCCCC"/>
                                <TextBlock Grid.Column="1" Text="{Binding SystemHealthScore}" Foreground="{StaticResource AccentBrush}" 
                                           FontWeight="Bold"/>
                            </Grid>
                        </StackPanel>

                        <!-- Quick Controls -->
                        <StackPanel>
                            <TextBlock Text="Quick Controls" 
                                       FontSize="14" 
                                       FontWeight="SemiBold" 
                                       Foreground="White" 
                                       Margin="0,0,0,15"/>
                            
                            <Button Content="🔄 Refresh Metrics" 
                                    Background="Transparent" 
                                    BorderBrush="{StaticResource AccentBrush}" 
                                    BorderThickness="1" 
                                    Foreground="White" 
                                    Padding="10" 
                                    Margin="0,0,0,10" 
                                    HorizontalAlignment="Stretch"
                                    Command="{Binding RefreshSystemInfoCommand}"/>
                            
                            <Button Content="🛡️ Create Backup" 
                                    Background="Transparent" 
                                    BorderBrush="{StaticResource SuccessBrush}" 
                                    BorderThickness="1" 
                                    Foreground="White" 
                                    Padding="10" 
                                    Margin="0,0,0,10" 
                                    HorizontalAlignment="Stretch"
                                    Command="{Binding CreateBackupCommand}"/>
                            
                            <Button Content="⚙️ Advanced Mode" 
                                    Background="Transparent" 
                                    BorderBrush="{StaticResource WarningBrush}" 
                                    BorderThickness="1" 
                                    Foreground="White" 
                                    Padding="10" 
                                    HorizontalAlignment="Stretch"
                                    Command="{Binding NavigateToViewCommand}"
                                    CommandParameter="AdvancedSettings"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>
</Window>
