using System.Windows;
using PCOptimizerApp.ViewModels;

namespace PCOptimizerApp.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow(MainWindowViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        protected override void OnClosed(EventArgs e)
        {
            if (DataContext is MainWindowViewModel viewModel)
            {
                viewModel.Cleanup();
            }
            base.OnClosed(e);
        }
    }
}
