using System.Windows;
using System.Windows.Controls;
using PCOptimizerApp.ViewModels;
using PCOptimizerApp.Services;

namespace PCOptimizerApp.Views
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly MainWindowViewModel _viewModel;

        public MainWindow(MainWindowViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            DataContext = viewModel;

            // Subscribe to navigation events
            _viewModel.NavigationRequested += OnNavigationRequested;

            // Load default page
            LoadDefaultPage();
        }

        private void OnNavigationRequested(object? sender, string viewName)
        {
            NavigateToPage(viewName);
        }

        private void LoadDefaultPage()
        {
            // Load the Smart Analysis page as the default
            NavigateToPage("SmartAnalysis");
        }

        private void NavigateToPage(string viewName)
        {
            try
            {
                Page? page = viewName switch
                {
                    "SmartAnalysis" => new SmartAnalysisPage(),
                    "OptimizationPlanning" => new OptimizationPlanningPage(),
                    "LiveOptimization" => new LiveOptimizationPage(),
                    "OptimizationResults" => new OptimizationResultsPage(),
                    "HelpSystem" => new HelpSystemPage(),
                    "Dashboard" => CreateDashboardPage(), // Create a simple dashboard page
                    _ => new SmartAnalysisPage() // Default fallback
                };

                if (page != null)
                {
                    MainContentFrame.Navigate(page);
                }
            }
            catch (Exception ex)
            {
                // Log error and show fallback content
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");

                // Show a simple error page or fallback
                var errorPage = new Page();
                var errorContent = new TextBlock
                {
                    Text = $"Navigation Error: Could not load {viewName}. Error: {ex.Message}",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    FontSize = 16,
                    Foreground = System.Windows.Media.Brushes.White,
                    TextWrapping = TextWrapping.Wrap,
                    Margin = new Thickness(20)
                };
                errorPage.Content = errorContent;
                MainContentFrame.Navigate(errorPage);
            }
        }

        private Page CreateDashboardPage()
        {
            // Create a simple dashboard page as fallback
            var page = new Page();
            var content = new TextBlock
            {
                Text = "Classic Dashboard - Coming Soon",
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 24,
                Foreground = System.Windows.Media.Brushes.White
            };
            page.Content = content;
            return page;
        }

        protected override void OnClosed(EventArgs e)
        {
            if (_viewModel != null)
            {
                _viewModel.NavigationRequested -= OnNavigationRequested;
                _viewModel.Cleanup();
            }
            base.OnClosed(e);
        }
    }
}
