<Page x:Class="PCOptimizerApp.Views.OptimizationPlanningPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Optimization Planning"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="CategoryCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="OptimizationItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="5,3"/>
            <Setter Property="Padding" Value="15,10"/>
        </Style>

        <Style x:Key="StatusIconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="⚡ Intelligent Optimization Plan" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="Tailored optimizations based on your hardware and usage patterns" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="StartOptimizationButton" Content="🚀 Start Optimization" 
                            Background="#27AE60" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="20,10" BorderThickness="0" Margin="0,0,10,0" Click="StartOptimizationButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#229954"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    
                    <Button Name="RefreshPlanButton" Content="🔄 Refresh Plan" 
                            Background="#3498DB" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="20,10" BorderThickness="0" Click="RefreshPlanButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#2980B9"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel Name="OptimizationCategoriesPanel">
                <!-- CPU Optimizations -->
                <Border Style="{StaticResource CategoryCardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="🔧" FontSize="24" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="CPU Optimizations" FontSize="20" FontWeight="Bold"/>
                                <TextBlock Name="CpuProgressText" Text="0/0 completed" FontSize="12" Foreground="#7F8C8D"/>
                            </StackPanel>
                            <ProgressBar Grid.Column="2" Name="CpuProgressBar" Width="150" Height="8" 
                                         Background="#E9ECEF" Foreground="#3498DB" Margin="10,0,0,0"/>
                        </Grid>
                        
                        <ItemsControl Name="CpuOptimizationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource OptimizationItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" Style="{StaticResource StatusIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding ExpectedImpact}" FontSize="12" Foreground="#27AE60" Margin="0,2,0,0"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="{Binding Status}" FontSize="12" 
                                                       Foreground="#7F8C8D" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Memory Optimizations -->
                <Border Style="{StaticResource CategoryCardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="💾" FontSize="24" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Memory Optimizations" FontSize="20" FontWeight="Bold"/>
                                <TextBlock Name="MemoryProgressText" Text="0/0 completed" FontSize="12" Foreground="#7F8C8D"/>
                            </StackPanel>
                            <ProgressBar Grid.Column="2" Name="MemoryProgressBar" Width="150" Height="8" 
                                         Background="#E9ECEF" Foreground="#9B59B6" Margin="10,0,0,0"/>
                        </Grid>
                        
                        <ItemsControl Name="MemoryOptimizationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource OptimizationItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" Style="{StaticResource StatusIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding ExpectedImpact}" FontSize="12" Foreground="#27AE60" Margin="0,2,0,0"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="{Binding Status}" FontSize="12" 
                                                       Foreground="#7F8C8D" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Storage Optimizations -->
                <Border Style="{StaticResource CategoryCardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="⚡" FontSize="24" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Storage Optimizations" FontSize="20" FontWeight="Bold"/>
                                <TextBlock Name="StorageProgressText" Text="0/0 completed" FontSize="12" Foreground="#7F8C8D"/>
                            </StackPanel>
                            <ProgressBar Grid.Column="2" Name="StorageProgressBar" Width="150" Height="8" 
                                         Background="#E9ECEF" Foreground="#E67E22" Margin="10,0,0,0"/>
                        </Grid>
                        
                        <ItemsControl Name="StorageOptimizationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource OptimizationItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" Style="{StaticResource StatusIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding ExpectedImpact}" FontSize="12" Foreground="#27AE60" Margin="0,2,0,0"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="{Binding Status}" FontSize="12" 
                                                       Foreground="#7F8C8D" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Graphics Optimizations -->
                <Border Style="{StaticResource CategoryCardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="🎮" FontSize="24" Margin="0,0,10,0"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Graphics Optimizations" FontSize="20" FontWeight="Bold"/>
                                <TextBlock Name="GraphicsProgressText" Text="0/0 completed" FontSize="12" Foreground="#7F8C8D"/>
                            </StackPanel>
                            <ProgressBar Grid.Column="2" Name="GraphicsProgressBar" Width="150" Height="8" 
                                         Background="#E9ECEF" Foreground="#E74C3C" Margin="10,0,0,0"/>
                        </Grid>
                        
                        <ItemsControl Name="GraphicsOptimizationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource OptimizationItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding StatusIcon}" Style="{StaticResource StatusIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding ExpectedImpact}" FontSize="12" Foreground="#27AE60" Margin="0,2,0,0"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="{Binding Status}" FontSize="12" 
                                                       Foreground="#7F8C8D" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer Summary -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="30,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Name="TotalOptimizationsCount" Text="0" FontSize="24" FontWeight="Bold" 
                               Foreground="#3498DB" HorizontalAlignment="Center"/>
                    <TextBlock Text="Total Optimizations" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Name="CompletedOptimizationsCount" Text="0" FontSize="24" FontWeight="Bold" 
                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                    <TextBlock Text="Completed" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Name="PendingOptimizationsCount" Text="0" FontSize="24" FontWeight="Bold" 
                               Foreground="#E67E22" HorizontalAlignment="Center"/>
                    <TextBlock Text="Pending" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Name="EstimatedImprovementText" Text="0%" FontSize="24" FontWeight="Bold" 
                               Foreground="#9B59B6" HorizontalAlignment="Center"/>
                    <TextBlock Text="Expected Improvement" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Page>
