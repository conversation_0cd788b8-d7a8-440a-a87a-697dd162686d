using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;

namespace PCOptimizerApp.Views
{
    public partial class OptimizationPlanningPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<OptimizationPlanningPage>();
        private readonly ISmartAnalysisService _smartAnalysisService;
        private readonly IOptimizationService _optimizationService;
        private readonly IProgressTrackingService _progressTrackingService;

        private ObservableCollection<OptimizationPlanItem> _cpuOptimizations = new();
        private ObservableCollection<OptimizationPlanItem> _memoryOptimizations = new();
        private ObservableCollection<OptimizationPlanItem> _storageOptimizations = new();
        private ObservableCollection<OptimizationPlanItem> _graphicsOptimizations = new();

        public OptimizationPlanningPage()
        {
            InitializeComponent();
            
            // Get services from DI container
            _smartAnalysisService = ServiceLocator.GetService<ISmartAnalysisService>();
            _optimizationService = ServiceLocator.GetService<IOptimizationService>();
            _progressTrackingService = ServiceLocator.GetService<IProgressTrackingService>();

            // Set up data binding
            CpuOptimizationsList.ItemsSource = _cpuOptimizations;
            MemoryOptimizationsList.ItemsSource = _memoryOptimizations;
            StorageOptimizationsList.ItemsSource = _storageOptimizations;
            GraphicsOptimizationsList.ItemsSource = _graphicsOptimizations;

            // Load optimization plan
            _ = LoadOptimizationPlanAsync();
        }

        private async Task LoadOptimizationPlanAsync()
        {
            try
            {
                _logger.Information("Loading optimization plan");

                // Get available optimizations
                var availableOptimizations = await _optimizationService.GetAvailableOptimizationsAsync();
                var recommendations = await _smartAnalysisService.GenerateSmartRecommendationsAsync(new SystemInfo());

                // Clear existing collections
                _cpuOptimizations.Clear();
                _memoryOptimizations.Clear();
                _storageOptimizations.Clear();
                _graphicsOptimizations.Clear();

                // Categorize optimizations
                foreach (var optimization in availableOptimizations.Where(o => o.IsApplicable))
                {
                    var planItem = new OptimizationPlanItem
                    {
                        Id = optimization.Id ?? "",
                        Name = optimization.Name ?? "",
                        ExpectedImpact = optimization.ExpectedImprovement ?? "",
                        Status = optimization.IsApplied ? "Completed" : "Pending",
                        StatusIcon = optimization.IsApplied ? "✓" : "⏳",
                        Category = optimization.Category ?? "General",
                        IsCompleted = optimization.IsApplied
                    };

                    // Categorize by type
                    var category = optimization.Category?.ToLower() ?? "";
                    if (category.Contains("performance") || category.Contains("cpu") || optimization.Id?.Contains("cpu") == true || optimization.Id?.Contains("intel") == true || optimization.Id?.Contains("amd") == true)
                    {
                        _cpuOptimizations.Add(planItem);
                    }
                    else if (category.Contains("memory") || optimization.Id?.Contains("memory") == true || optimization.Id?.Contains("ram") == true)
                    {
                        _memoryOptimizations.Add(planItem);
                    }
                    else if (category.Contains("storage") || optimization.Id?.Contains("ssd") == true || optimization.Id?.Contains("hdd") == true || optimization.Id?.Contains("nvme") == true)
                    {
                        _storageOptimizations.Add(planItem);
                    }
                    else if (category.Contains("graphics") || optimization.Id?.Contains("gpu") == true || optimization.Id?.Contains("nvidia") == true || optimization.Id?.Contains("gaming") == true)
                    {
                        _graphicsOptimizations.Add(planItem);
                    }
                    else
                    {
                        // Default to CPU category for general optimizations
                        _cpuOptimizations.Add(planItem);
                    }
                }

                // Update progress bars and counters
                UpdateCategoryProgress();
                UpdateSummaryCounters();

                _logger.Information("Optimization plan loaded with {Total} optimizations", 
                    _cpuOptimizations.Count + _memoryOptimizations.Count + _storageOptimizations.Count + _graphicsOptimizations.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error loading optimization plan");
                MessageBox.Show($"Error loading optimization plan: {ex.Message}", 
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateCategoryProgress()
        {
            // CPU Progress
            var cpuCompleted = _cpuOptimizations.Count(o => o.IsCompleted);
            var cpuTotal = _cpuOptimizations.Count;
            CpuProgressBar.Value = cpuTotal > 0 ? (double)cpuCompleted / cpuTotal * 100 : 0;
            CpuProgressText.Text = $"{cpuCompleted}/{cpuTotal} completed";

            // Memory Progress
            var memoryCompleted = _memoryOptimizations.Count(o => o.IsCompleted);
            var memoryTotal = _memoryOptimizations.Count;
            MemoryProgressBar.Value = memoryTotal > 0 ? (double)memoryCompleted / memoryTotal * 100 : 0;
            MemoryProgressText.Text = $"{memoryCompleted}/{memoryTotal} completed";

            // Storage Progress
            var storageCompleted = _storageOptimizations.Count(o => o.IsCompleted);
            var storageTotal = _storageOptimizations.Count;
            StorageProgressBar.Value = storageTotal > 0 ? (double)storageCompleted / storageTotal * 100 : 0;
            StorageProgressText.Text = $"{storageCompleted}/{storageTotal} completed";

            // Graphics Progress
            var graphicsCompleted = _graphicsOptimizations.Count(o => o.IsCompleted);
            var graphicsTotal = _graphicsOptimizations.Count;
            GraphicsProgressBar.Value = graphicsTotal > 0 ? (double)graphicsCompleted / graphicsTotal * 100 : 0;
            GraphicsProgressText.Text = $"{graphicsCompleted}/{graphicsTotal} completed";
        }

        private void UpdateSummaryCounters()
        {
            var totalOptimizations = _cpuOptimizations.Count + _memoryOptimizations.Count + 
                                   _storageOptimizations.Count + _graphicsOptimizations.Count;
            var completedOptimizations = _cpuOptimizations.Count(o => o.IsCompleted) + 
                                       _memoryOptimizations.Count(o => o.IsCompleted) +
                                       _storageOptimizations.Count(o => o.IsCompleted) + 
                                       _graphicsOptimizations.Count(o => o.IsCompleted);
            var pendingOptimizations = totalOptimizations - completedOptimizations;

            TotalOptimizationsCount.Text = totalOptimizations.ToString();
            CompletedOptimizationsCount.Text = completedOptimizations.ToString();
            PendingOptimizationsCount.Text = pendingOptimizations.ToString();

            // Calculate estimated improvement (5% per optimization, max 50%)
            var estimatedImprovement = Math.Min(pendingOptimizations * 5, 50);
            EstimatedImprovementText.Text = $"{estimatedImprovement}%";
        }

        private async void StartOptimizationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var pendingOptimizations = GetPendingOptimizations();
                
                if (!pendingOptimizations.Any())
                {
                    MessageBox.Show("All optimizations are already applied!", 
                                    "No Optimizations Needed", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"This will apply {pendingOptimizations.Count} pending optimizations. " +
                    "A backup will be created before applying changes. Continue?",
                    "Start Optimization", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _logger.Information("Starting optimization process for {Count} optimizations", pendingOptimizations.Count);

                    StartOptimizationButton.IsEnabled = false;
                    StartOptimizationButton.Content = "🔄 Optimizing...";

                    // Apply optimizations
                    var appliedCount = 0;
                    var failedCount = 0;

                    foreach (var optimization in pendingOptimizations)
                    {
                        try
                        {
                            // Update status to in progress
                            optimization.Status = "In Progress";
                            optimization.StatusIcon = "🔄";

                            var success = await _optimizationService.ApplyOptimizationAsync(optimization.Id);
                            
                            if (success)
                            {
                                optimization.Status = "Completed";
                                optimization.StatusIcon = "✓";
                                optimization.IsCompleted = true;
                                appliedCount++;
                                _logger.Information("Applied optimization: {Name}", optimization.Name);
                            }
                            else
                            {
                                optimization.Status = "Failed";
                                optimization.StatusIcon = "❌";
                                failedCount++;
                                _logger.Warning("Failed to apply optimization: {Name}", optimization.Name);
                            }

                            // Update progress
                            UpdateCategoryProgress();
                            UpdateSummaryCounters();

                            // Small delay for visual feedback
                            await Task.Delay(500);
                        }
                        catch (Exception ex)
                        {
                            optimization.Status = "Failed";
                            optimization.StatusIcon = "❌";
                            failedCount++;
                            _logger.Error(ex, "Error applying optimization: {Name}", optimization.Name);
                        }
                    }

                    // Show results
                    var message = $"Applied {appliedCount} optimizations successfully.";
                    if (failedCount > 0)
                    {
                        message += $" {failedCount} optimizations failed to apply.";
                    }

                    MessageBox.Show(message, "Optimization Complete", 
                                    MessageBoxButton.OK, 
                                    failedCount > 0 ? MessageBoxImage.Warning : MessageBoxImage.Information);

                    _logger.Information("Optimization process completed. Applied: {Applied}, Failed: {Failed}", 
                                        appliedCount, failedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during optimization process");
                MessageBox.Show($"An error occurred during optimization: {ex.Message}", 
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                StartOptimizationButton.IsEnabled = true;
                StartOptimizationButton.Content = "🚀 Start Optimization";
            }
        }

        private async void RefreshPlanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                RefreshPlanButton.IsEnabled = false;
                RefreshPlanButton.Content = "🔄 Refreshing...";

                await LoadOptimizationPlanAsync();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error refreshing optimization plan");
                MessageBox.Show($"Error refreshing plan: {ex.Message}", 
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                RefreshPlanButton.IsEnabled = true;
                RefreshPlanButton.Content = "🔄 Refresh Plan";
            }
        }

        private List<OptimizationPlanItem> GetPendingOptimizations()
        {
            var pending = new List<OptimizationPlanItem>();
            pending.AddRange(_cpuOptimizations.Where(o => !o.IsCompleted));
            pending.AddRange(_memoryOptimizations.Where(o => !o.IsCompleted));
            pending.AddRange(_storageOptimizations.Where(o => !o.IsCompleted));
            pending.AddRange(_graphicsOptimizations.Where(o => !o.IsCompleted));
            return pending;
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Cleanup if needed
                _logger.Debug("Optimization planning page unloaded");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during page unload");
            }
        }
    }

    public class OptimizationPlanItem
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string ExpectedImpact { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public string StatusIcon { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public bool IsCompleted { get; set; }
    }
}
