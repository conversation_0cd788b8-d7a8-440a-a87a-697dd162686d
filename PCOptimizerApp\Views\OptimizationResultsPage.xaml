<Page x:Class="PCOptimizerApp.Views.OptimizationResultsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      mc:Ignorable="d" 
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Optimization Results"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="ResultCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="12"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="MetricCardStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="20"/>
        </Style>

        <Style x:Key="ImprovementTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="#27AE60"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🎉 Optimization Complete!" FontSize="28" FontWeight="Bold" Foreground="#27AE60"/>
                    <TextBlock Text="Your PC has been successfully optimized with measurable performance improvements" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="ShareResultsButton" Content="📤 Share Results" 
                            Background="#3498DB" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="15,8" BorderThickness="0" Margin="0,0,10,0" Click="ShareResultsButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#2980B9"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                    
                    <Button Name="OptimizeMoreButton" Content="🚀 Optimize More" 
                            Background="#27AE60" Foreground="White" FontSize="14" FontWeight="Bold"
                            Padding="15,8" BorderThickness="0" Click="OptimizeMoreButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="6" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#229954"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Performance Improvements -->
                <Border Style="{StaticResource ResultCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📈 Performance Improvements" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Boot Time -->
                            <Border Grid.Column="0" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🚀" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="Boot Time" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="BootTimeBefore" Text="45.0s" FontSize="12" Foreground="#E74C3C" 
                                               HorizontalAlignment="Center" TextDecorations="Strikethrough"/>
                                    <TextBlock Name="BootTimeAfter" Text="28.0s" FontSize="16" FontWeight="Bold" 
                                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                                    <TextBlock Name="BootTimeImprovement" Style="{StaticResource ImprovementTextStyle}" Text="+38%"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- App Loading -->
                            <Border Grid.Column="1" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="⚡" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="App Loading" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="AppLoadBefore" Text="3.2s" FontSize="12" Foreground="#E74C3C" 
                                               HorizontalAlignment="Center" TextDecorations="Strikethrough"/>
                                    <TextBlock Name="AppLoadAfter" Text="1.8s" FontSize="16" FontWeight="Bold" 
                                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                                    <TextBlock Name="AppLoadImprovement" Style="{StaticResource ImprovementTextStyle}" Text="+44%"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Memory Efficiency -->
                            <Border Grid.Column="2" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💾" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="Memory Efficiency" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="MemoryBefore" Text="72%" FontSize="12" Foreground="#E74C3C" 
                                               HorizontalAlignment="Center" TextDecorations="Strikethrough"/>
                                    <TextBlock Name="MemoryAfter" Text="89%" FontSize="16" FontWeight="Bold" 
                                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                                    <TextBlock Name="MemoryImprovement" Style="{StaticResource ImprovementTextStyle}" Text="+24%"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Disk Performance -->
                            <Border Grid.Column="3" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💿" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="Disk Performance" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    <TextBlock Name="DiskBefore" Text="450MB/s" FontSize="12" Foreground="#E74C3C" 
                                               HorizontalAlignment="Center" TextDecorations="Strikethrough"/>
                                    <TextBlock Name="DiskAfter" Text="520MB/s" FontSize="16" FontWeight="Bold" 
                                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                                    <TextBlock Name="DiskImprovement" Style="{StaticResource ImprovementTextStyle}" Text="+16%"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Value Delivered -->
                <Border Style="{StaticResource ResultCardStyle}">
                    <StackPanel>
                        <TextBlock Text="💰 Value Delivered" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,20"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <Border Grid.Column="0" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="💵" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Name="EquivalentValue" Text="$200+" FontSize="28" FontWeight="Bold" 
                                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Equivalent PC Upgrade Value" FontSize="12" Foreground="#7F8C8D" 
                                               HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                            
                            <Border Grid.Column="1" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="⏰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Name="TimeSaved" Text="2.5 hours" FontSize="28" FontWeight="Bold" 
                                               Foreground="#3498DB" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Time Saved Per Week" FontSize="12" Foreground="#7F8C8D" 
                                               HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                            
                            <Border Grid.Column="2" Style="{StaticResource MetricCardStyle}">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🔧" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Name="OptimizationsApplied" Text="47" FontSize="28" FontWeight="Bold" 
                                               Foreground="#E74C3C" HorizontalAlignment="Center"/>
                                    <TextBlock Text="Professional Optimizations" FontSize="12" Foreground="#7F8C8D" 
                                               HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Optimization Summary -->
                <Border Style="{StaticResource ResultCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📋 Optimization Summary" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                            <ItemsControl Name="OptimizationSummaryList">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1"
                                                CornerRadius="6" Margin="0,5" Padding="15">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0" Text="✅" FontSize="16" Margin="0,0,10,0"/>
                                                <StackPanel Grid.Column="1">
                                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                    <TextBlock Text="{Binding Description}" FontSize="12"
                                                               Foreground="#7F8C8D" Margin="0,2,0,0"/>
                                                </StackPanel>
                                                <TextBlock Grid.Column="2" Text="{Binding Impact}" FontSize="12"
                                                           Foreground="#27AE60" FontWeight="Bold" VerticalAlignment="Center"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- Future Recommendations -->
                <Border Style="{StaticResource ResultCardStyle}">
                    <StackPanel>
                        <TextBlock Text="🔮 Future Recommendations" FontSize="22" FontWeight="Bold" 
                                   Foreground="#2C3E50" Margin="0,0,0,15"/>
                        
                        <ItemsControl Name="FutureRecommendationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" 
                                            CornerRadius="6" Margin="0,5" Padding="15">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="💡" FontSize="16" Margin="0,0,10,0"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding Description}" FontSize="12" 
                                                           Foreground="#856404" Margin="0,2,0,0" TextWrapping="Wrap"/>
                                            </StackPanel>
                                            <TextBlock Grid.Column="2" Text="{Binding Priority}" FontSize="12" 
                                                       Foreground="#E67E22" FontWeight="Bold" VerticalAlignment="Center"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0" Padding="30,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Name="CompletionMessage" Text="Optimization completed in 3 minutes and 47 seconds" 
                               FontSize="14" FontWeight="Bold" Foreground="#27AE60"/>
                    <TextBlock Text="Your PC is now running at peak performance!" 
                               FontSize="12" Foreground="#7F8C8D" Margin="0,2,0,0"/>
                </StackPanel>
                
                <Button Grid.Column="1" Name="FinishButton" Content="🏁 Finish" 
                        Background="#2C3E50" Foreground="White" FontSize="14" FontWeight="Bold"
                        Padding="20,10" BorderThickness="0" Click="FinishButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#34495E"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Page>
