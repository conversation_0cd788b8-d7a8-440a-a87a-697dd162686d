<Page x:Class="PCOptimizerApp.Views.SmartAnalysisPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      mc:Ignorable="d"
      d:DesignHeight="800" d:DesignWidth="1200"
      Title="Smart Analysis"
      Unloaded="Page_Unloaded">

    <Page.Resources>
        <Style x:Key="AnalysisCardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="StepIconStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <Style x:Key="DetectionItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#E9ECEF"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="15"/>
        </Style>
    </Page.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="30,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="🧠 Smart Analysis" FontSize="28" FontWeight="Bold" Foreground="#2C3E50"/>
                    <TextBlock Text="AI-powered system analysis with hardware-specific optimizations" 
                               FontSize="14" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                </StackPanel>

                <Button Grid.Column="1" Name="StartAnalysisButton" Content="🚀 Start Analysis" 
                        Background="#3498DB" Foreground="White" FontSize="14" FontWeight="Bold"
                        Padding="20,10" BorderThickness="0" Click="StartAnalysisButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="6" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#2980B9"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                <!-- Analysis Progress Section -->
                <Border Name="AnalysisProgressSection" Style="{StaticResource AnalysisCardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="🔍 Analysis in Progress" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
                        
                        <ProgressBar Name="AnalysisProgressBar" Height="8" Background="#E9ECEF" 
                                     Foreground="#3498DB" Margin="0,0,0,10"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Name="AnalysisStepIcon" Grid.Column="0" Style="{StaticResource StepIconStyle}"/>
                            <StackPanel Grid.Column="1">
                                <TextBlock Name="AnalysisStepName" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Name="AnalysisStepDescription" FontSize="12" Foreground="#7F8C8D" Margin="0,2,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Hardware Detection Results -->
                <Border Name="HardwareDetectionSection" Style="{StaticResource AnalysisCardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="🔧 Hardware Detection Results" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
                        <ItemsControl Name="HardwareDetectionList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource DetectionItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding Icon}" Style="{StaticResource StepIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding DetectionMessage}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding ComponentName}" FontSize="12" Foreground="#7F8C8D" Margin="0,2,0,5"/>
                                                <ItemsControl ItemsSource="{Binding OptimizationsEnabled}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <StackPanel Orientation="Horizontal" Margin="0,1">
                                                                <TextBlock Text="  • " FontSize="11" Foreground="#27AE60"/>
                                                                <TextBlock Text="{Binding}" FontSize="11" Foreground="#27AE60"/>
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Usage Pattern Results -->
                <Border Name="UsagePatternsSection" Style="{StaticResource AnalysisCardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="🎯 Usage Pattern Analysis" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
                        <ItemsControl Name="UsagePatternsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource DetectionItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0" Text="{Binding Icon}" Style="{StaticResource StepIconStyle}"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding DetectionMessage}" FontWeight="Bold" FontSize="14"/>
                                                <ItemsControl ItemsSource="{Binding RecommendedOptimizations}">
                                                    <ItemsControl.ItemTemplate>
                                                        <DataTemplate>
                                                            <StackPanel Orientation="Horizontal" Margin="0,1">
                                                                <TextBlock Text="  • " FontSize="11" Foreground="#8E44AD"/>
                                                                <TextBlock Text="{Binding}" FontSize="11" Foreground="#8E44AD"/>
                                                            </StackPanel>
                                                        </DataTemplate>
                                                    </ItemsControl.ItemTemplate>
                                                </ItemsControl>
                                            </StackPanel>
                                            <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                                <TextBlock Text="{Binding Confidence, StringFormat={}{0:F0}%}" 
                                                           FontWeight="Bold" FontSize="12" Foreground="#E67E22"/>
                                                <TextBlock Text="Confidence" FontSize="10" Foreground="#7F8C8D"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Smart Recommendations -->
                <Border Name="RecommendationsSection" Style="{StaticResource AnalysisCardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="🚀 Smart Recommendations" FontSize="20" FontWeight="Bold"/>
                            <Button Grid.Column="1" Name="ApplyRecommendationsButton" Content="Apply Selected" 
                                    Background="#27AE60" Foreground="White" FontSize="12" FontWeight="Bold"
                                    Padding="15,8" BorderThickness="0" Click="ApplyRecommendationsButton_Click">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" 
                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <ControlTemplate.Triggers>
                                                        <Trigger Property="IsMouseOver" Value="True">
                                                            <Setter Property="Background" Value="#229954"/>
                                                        </Trigger>
                                                    </ControlTemplate.Triggers>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </Grid>
                        
                        <ItemsControl Name="RecommendationsList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource DetectionItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <CheckBox Grid.Column="0" IsChecked="True" VerticalAlignment="Top" Margin="0,0,10,0"/>
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                                <TextBlock Text="{Binding Description}" FontSize="12" Foreground="#7F8C8D" 
                                                           Margin="0,2,0,5" TextWrapping="Wrap"/>
                                                <TextBlock Text="{Binding ExpectedImprovement}" FontSize="11" Foreground="#27AE60"/>
                                            </StackPanel>
                                            <StackPanel Grid.Column="2" HorizontalAlignment="Right" Margin="10,0">
                                                <TextBlock Text="{Binding Priority}" FontWeight="Bold" FontSize="12" 
                                                           Foreground="#E74C3C" HorizontalAlignment="Center"/>
                                                <TextBlock Text="Priority" FontSize="10" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                                            </StackPanel>
                                            <Border Grid.Column="3" Background="#3498DB" CornerRadius="3" Padding="8,4" Margin="5,0,0,0">
                                                <TextBlock Text="{Binding Category}" FontSize="10" Foreground="White" FontWeight="Bold"/>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Analysis Summary -->
                <Border Name="AnalysisSummarySection" Style="{StaticResource AnalysisCardStyle}" Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="📊 Analysis Summary" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                <TextBlock Name="HardwareDetectionsCount" Text="0" FontSize="24" FontWeight="Bold" 
                                           Foreground="#3498DB" HorizontalAlignment="Center"/>
                                <TextBlock Text="Hardware Detections" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <TextBlock Name="UsagePatternsCount" Text="0" FontSize="24" FontWeight="Bold" 
                                           Foreground="#9B59B6" HorizontalAlignment="Center"/>
                                <TextBlock Text="Usage Patterns" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                                <TextBlock Name="RecommendationsCount" Text="0" FontSize="24" FontWeight="Bold" 
                                           Foreground="#27AE60" HorizontalAlignment="Center"/>
                                <TextBlock Text="Recommendations" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                                <TextBlock Name="AnalysisDuration" Text="0s" FontSize="24" FontWeight="Bold" 
                                           Foreground="#E67E22" HorizontalAlignment="Center"/>
                                <TextBlock Text="Analysis Time" FontSize="12" Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Page>
