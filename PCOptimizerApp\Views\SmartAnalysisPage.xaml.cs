using Microsoft.Extensions.DependencyInjection;
using PCOptimizerApp.Models;
using PCOptimizerApp.Services;
using Serilog;
using System.Windows;
using System.Windows.Controls;

namespace PCOptimizerApp.Views
{
    public partial class SmartAnalysisPage : Page
    {
        private readonly ILogger _logger = Log.ForContext<SmartAnalysisPage>();
        private readonly ISmartAnalysisService _smartAnalysisService;
        private readonly IOptimizationService _optimizationService;
        private SmartAnalysisResult? _currentAnalysisResult;

        public SmartAnalysisPage()
        {
            InitializeComponent();

            // Get services from DI container using ServiceLocator pattern
            _smartAnalysisService = ServiceLocator.GetService<ISmartAnalysisService>();
            _optimizationService = ServiceLocator.GetService<IOptimizationService>();

            // Subscribe to analysis progress events
            _smartAnalysisService.AnalysisProgressUpdated += OnAnalysisProgressUpdated;
        }

        private async void StartAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _logger.Information("Starting smart analysis from UI");

                // Reset UI
                ResetAnalysisUI();
                
                // Show progress section
                AnalysisProgressSection.Visibility = Visibility.Visible;
                StartAnalysisButton.IsEnabled = false;
                StartAnalysisButton.Content = "🔄 Analyzing...";

                // Perform analysis
                _currentAnalysisResult = await _smartAnalysisService.PerformSmartAnalysisAsync();

                if (_currentAnalysisResult.Success)
                {
                    // Display results
                    DisplayAnalysisResults(_currentAnalysisResult);
                    _logger.Information("Smart analysis completed successfully");
                }
                else
                {
                    MessageBox.Show($"Analysis failed: {_currentAnalysisResult.ErrorMessage}", 
                                    "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    _logger.Error("Smart analysis failed: {Error}", _currentAnalysisResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during smart analysis");
                MessageBox.Show($"An error occurred during analysis: {ex.Message}", 
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Reset button
                StartAnalysisButton.IsEnabled = true;
                StartAnalysisButton.Content = "🚀 Start Analysis";
                
                // Hide progress section
                AnalysisProgressSection.Visibility = Visibility.Collapsed;
            }
        }

        private async void ApplyRecommendationsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_currentAnalysisResult?.Recommendations == null || !_currentAnalysisResult.Recommendations.Any())
                {
                    MessageBox.Show("No recommendations available to apply.", 
                                    "No Recommendations", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show(
                    $"This will apply {_currentAnalysisResult.Recommendations.Count} recommended optimizations. " +
                    "A backup will be created before applying changes. Continue?",
                    "Apply Recommendations", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _logger.Information("Applying smart analysis recommendations");

                    ApplyRecommendationsButton.IsEnabled = false;
                    ApplyRecommendationsButton.Content = "🔄 Applying...";

                    // Apply each recommendation
                    var appliedCount = 0;
                    var failedCount = 0;

                    foreach (var recommendation in _currentAnalysisResult.Recommendations)
                    {
                        try
                        {
                            var success = await _optimizationService.ApplyOptimizationAsync(recommendation.OptimizationId);
                            if (success)
                            {
                                appliedCount++;
                                _logger.Information("Applied recommendation: {Name}", recommendation.Name);
                            }
                            else
                            {
                                failedCount++;
                                _logger.Warning("Failed to apply recommendation: {Name}", recommendation.Name);
                            }
                        }
                        catch (Exception ex)
                        {
                            failedCount++;
                            _logger.Error(ex, "Error applying recommendation: {Name}", recommendation.Name);
                        }
                    }

                    // Show results
                    var message = $"Applied {appliedCount} optimizations successfully.";
                    if (failedCount > 0)
                    {
                        message += $" {failedCount} optimizations failed to apply.";
                    }

                    MessageBox.Show(message, "Recommendations Applied", 
                                    MessageBoxButton.OK, 
                                    failedCount > 0 ? MessageBoxImage.Warning : MessageBoxImage.Information);

                    _logger.Information("Completed applying recommendations. Applied: {Applied}, Failed: {Failed}", 
                                        appliedCount, failedCount);
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error applying recommendations");
                MessageBox.Show($"An error occurred while applying recommendations: {ex.Message}", 
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                ApplyRecommendationsButton.IsEnabled = true;
                ApplyRecommendationsButton.Content = "Apply Selected";
            }
        }

        private void OnAnalysisProgressUpdated(object? sender, AnalysisProgressEventArgs e)
        {
            // Update UI on the UI thread
            Dispatcher.Invoke(() =>
            {
                try
                {
                    AnalysisProgressBar.Value = e.ProgressPercentage;
                    AnalysisStepIcon.Text = e.Icon;
                    AnalysisStepName.Text = e.StepName;
                    AnalysisStepDescription.Text = e.Description;

                    _logger.Debug("Analysis progress: {Step}/{Total} - {StepName}", 
                                  e.CurrentStep, e.TotalSteps, e.StepName);
                }
                catch (Exception ex)
                {
                    _logger.Error(ex, "Error updating analysis progress UI");
                }
            });
        }

        private void ResetAnalysisUI()
        {
            try
            {
                // Hide all result sections
                HardwareDetectionSection.Visibility = Visibility.Collapsed;
                UsagePatternsSection.Visibility = Visibility.Collapsed;
                RecommendationsSection.Visibility = Visibility.Collapsed;
                AnalysisSummarySection.Visibility = Visibility.Collapsed;

                // Clear lists
                HardwareDetectionList.ItemsSource = null;
                UsagePatternsList.ItemsSource = null;
                RecommendationsList.ItemsSource = null;

                // Reset progress
                AnalysisProgressBar.Value = 0;
                AnalysisStepIcon.Text = "";
                AnalysisStepName.Text = "";
                AnalysisStepDescription.Text = "";

                // Reset summary
                HardwareDetectionsCount.Text = "0";
                UsagePatternsCount.Text = "0";
                RecommendationsCount.Text = "0";
                AnalysisDuration.Text = "0s";
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error resetting analysis UI");
            }
        }

        private void DisplayAnalysisResults(SmartAnalysisResult result)
        {
            try
            {
                _logger.Information("Displaying analysis results");

                // Hardware Detection Results
                if (result.HardwareDetections.Any())
                {
                    HardwareDetectionList.ItemsSource = result.HardwareDetections;
                    HardwareDetectionSection.Visibility = Visibility.Visible;
                }

                // Usage Pattern Results
                if (result.UsagePatterns.Any())
                {
                    UsagePatternsList.ItemsSource = result.UsagePatterns;
                    UsagePatternsSection.Visibility = Visibility.Visible;
                }

                // Smart Recommendations
                if (result.Recommendations.Any())
                {
                    RecommendationsList.ItemsSource = result.Recommendations;
                    RecommendationsSection.Visibility = Visibility.Visible;
                }

                // Analysis Summary
                HardwareDetectionsCount.Text = result.HardwareDetections.Count.ToString();
                UsagePatternsCount.Text = result.UsagePatterns.Count.ToString();
                RecommendationsCount.Text = result.Recommendations.Count.ToString();
                AnalysisDuration.Text = $"{result.Duration.TotalSeconds:F1}s";
                AnalysisSummarySection.Visibility = Visibility.Visible;

                _logger.Information("Analysis results displayed: {Hardware} hardware detections, {Patterns} usage patterns, {Recommendations} recommendations", 
                                    result.HardwareDetections.Count, result.UsagePatterns.Count, result.Recommendations.Count);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error displaying analysis results");
                MessageBox.Show($"Error displaying results: {ex.Message}", 
                                "Display Error", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void Page_Unloaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Unsubscribe from events
                _smartAnalysisService.AnalysisProgressUpdated -= OnAnalysisProgressUpdated;
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Error during page unload");
            }
        }
    }
}
