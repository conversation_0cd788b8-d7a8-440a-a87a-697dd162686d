2025-06-29 16:13:56.314 +05:30 [INF] Performance counters initialized successfully
2025-06-29 16:28:19.627 +05:30 [INF] Performance counters initialized successfully
2025-06-29 16:28:24.576 +05:30 [INF] Starting performance monitoring
2025-06-29 16:33:31.600 +05:30 [INF] Starting quick optimization
2025-06-29 16:33:33.907 +05:30 [INF] Creating system restore point: Quick Optimize
2025-06-29 16:33:37.008 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:37.010 +05:30 [INF] Applying optimization: power_high_performance
2025-06-29 16:33:37.011 +05:30 [INF] Creating system restore point: Before applying power_high_performance
2025-06-29 16:33:39.516 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying power_high_performa ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:39.723 +05:30 [INF] Successfully applied optimization: power_high_performance
2025-06-29 16:33:39.724 +05:30 [INF] Applying optimization: multicore_power_settings
2025-06-29 16:33:39.724 +05:30 [INF] Creating system restore point: Before applying multicore_power_settings
2025-06-29 16:33:42.184 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:42.184 +05:30 [WRN] Failed to apply optimization: multicore_power_settings
2025-06-29 16:33:42.184 +05:30 [INF] Applying optimization: visual_effects_performance
2025-06-29 16:33:42.184 +05:30 [INF] Creating system restore point: Before applying visual_effects_performance
2025-06-29 16:33:44.522 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying visual_effects_perf ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:44.525 +05:30 [INF] Set registry value: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects\VisualFXSetting = 2
2025-06-29 16:33:44.527 +05:30 [INF] Successfully applied optimization: visual_effects_performance
2025-06-29 16:33:44.527 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 16:33:44.527 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 16:33:46.954 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:47.227 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 16:33:47.228 +05:30 [INF] Quick optimization completed. Applied: 3, Failed: 1, Duration: "00:00:15.6272410"
2025-06-29 16:51:50.071 +05:30 [INF] Stopping performance monitoring
2025-06-29 19:25:59.194 +05:30 [INF] Performance counters initialized successfully
2025-06-29 19:26:00.697 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:00.722 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:02.008 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:02.020 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:04.401 +05:30 [INF] Starting performance monitoring
2025-06-29 19:26:06.499 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:06.507 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:06.585 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:08.488 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:08.491 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:08.530 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:10.501 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:10.505 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:10.546 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:11.623 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:11.633 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:12.483 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:12.484 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:12.517 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:14.490 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:14.493 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:14.532 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:16.484 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:16.485 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:16.530 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:16.601 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:16.607 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:18.482 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:18.484 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:18.523 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:20.490 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:20.494 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:20.540 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:21.609 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:21.619 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:22.476 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:22.478 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:22.518 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:24.494 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:24.495 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:24.539 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:26.487 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:26.489 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:26.538 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:26.582 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:26.586 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:28.489 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:28.494 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:28.534 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:30.501 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:30.507 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:30.550 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:31.609 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:31.618 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:32.488 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:32.490 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:32.534 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:33.008 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 19:26:33.009 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 19:26:33.010 +05:30 [INF] [c27a646d-fe56-4111-818b-a15190b5ec8a] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:26:33.011 +05:30 [INF] Started operation c27a646d-fe56-4111-818b-a15190b5ec8a: Create System Restore Point with 3 steps
2025-06-29 19:26:33.012 +05:30 [INF] [c27a646d-fe56-4111-818b-a15190b5ec8a] Step 1: Initializing restore point creation...
2025-06-29 19:26:33.012 +05:30 [INF] Operation c27a646d-fe56-4111-818b-a15190b5ec8a progress: Step 1 - Initializing restore point creation...
2025-06-29 19:26:33.016 +05:30 [INF] [c27a646d-fe56-4111-818b-a15190b5ec8a] Step 2: Creating restore point...
2025-06-29 19:26:33.017 +05:30 [INF] Operation c27a646d-fe56-4111-818b-a15190b5ec8a progress: Step 2 - Creating restore point...
2025-06-29 19:26:34.497 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:34.498 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:34.541 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:35.941 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:26:35.941 +05:30 [ERR] [c27a646d-fe56-4111-818b-a15190b5ec8a] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:26:35.943 +05:30 [INF] Completed operation c27a646d-fe56-4111-818b-a15190b5ec8a: Create System Restore Point. Success: false, Duration: "00:00:02.9325522"
2025-06-29 19:26:35.959 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 19:26:36.487 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:36.489 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:36.533 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:37.074 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:37.078 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:38.483 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:38.485 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:38.520 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:40.534 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:40.539 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:40.585 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:41.632 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:41.642 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:42.512 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:42.516 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:42.555 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:43.508 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:43.520 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:44.511 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:44.515 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:44.561 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:45.752 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:45.762 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:46.489 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:46.493 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:46.533 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:48.516 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:48.517 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:48.561 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:50.515 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:50.517 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:50.561 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:52.519 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:52.521 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:52.565 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:54.515 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:54.517 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:54.560 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:56.511 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:56.515 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:56.561 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:26:56.601 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:26:56.608 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:26:58.519 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:26:58.521 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:26:58.572 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:00.554 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:00.557 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:00.617 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:01.697 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:01.708 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:02.506 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:02.507 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:02.550 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:04.517 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:04.518 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:04.561 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:06.499 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:06.502 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:06.552 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:06.611 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:06.618 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:08.480 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:08.481 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:08.510 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:10.525 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:10.527 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:10.574 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:11.619 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:11.630 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:12.505 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:12.506 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:12.549 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:14.517 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:14.519 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:14.564 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:16.513 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:16.516 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:16.563 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:16.612 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:16.618 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:17.484 +05:30 [INF] Starting quick optimization
2025-06-29 19:27:18.508 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:18.509 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:18.552 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:18.627 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:27:19.775 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:27:20.510 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:20.512 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:20.554 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:20.912 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:27:20.940 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Started operation: Quick Optimization with 6 steps
2025-06-29 19:27:20.940 +05:30 [INF] Started operation e843e6b3-ae21-420b-b973-2875f25bc165: Quick Optimization with 6 steps
2025-06-29 19:27:20.940 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 1: Creating system backup...
2025-06-29 19:27:20.940 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 1 - Creating system backup...
2025-06-29 19:27:20.940 +05:30 [INF] Creating system restore point: Quick Optimize
2025-06-29 19:27:20.940 +05:30 [INF] [d421930c-f0d3-44d2-881f-c429f5a229c7] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:27:20.940 +05:30 [INF] Started operation d421930c-f0d3-44d2-881f-c429f5a229c7: Create System Restore Point with 3 steps
2025-06-29 19:27:20.940 +05:30 [INF] [d421930c-f0d3-44d2-881f-c429f5a229c7] Step 1: Initializing restore point creation...
2025-06-29 19:27:20.940 +05:30 [INF] Operation d421930c-f0d3-44d2-881f-c429f5a229c7 progress: Step 1 - Initializing restore point creation...
2025-06-29 19:27:20.940 +05:30 [INF] [d421930c-f0d3-44d2-881f-c429f5a229c7] Step 2: Creating restore point...
2025-06-29 19:27:20.940 +05:30 [INF] Operation d421930c-f0d3-44d2-881f-c429f5a229c7 progress: Step 2 - Creating restore point...
2025-06-29 19:27:22.515 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:22.517 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:22.559 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:23.239 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:23.239 +05:30 [ERR] [d421930c-f0d3-44d2-881f-c429f5a229c7] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:23.239 +05:30 [INF] Completed operation d421930c-f0d3-44d2-881f-c429f5a229c7: Create System Restore Point. Success: false, Duration: "00:00:02.2983582"
2025-06-29 19:27:23.239 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 2: Applying High Performance Power Plan...
2025-06-29 19:27:23.239 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 2 - Applying High Performance Power Plan...
2025-06-29 19:27:23.239 +05:30 [INF] Applying optimization: power_high_performance
2025-06-29 19:27:23.264 +05:30 [INF] Creating optimization backup for: power_high_performance
2025-06-29 19:27:23.264 +05:30 [INF] [69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:27:23.264 +05:30 [INF] Started operation 69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f: Create Optimization Backup with 3 steps
2025-06-29 19:27:23.264 +05:30 [INF] [69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f] Step 1: Initializing backup...
2025-06-29 19:27:23.264 +05:30 [INF] Operation 69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f progress: Step 1 - Initializing backup...
2025-06-29 19:27:23.264 +05:30 [INF] [69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f] Step 2: Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:27:23.264 +05:30 [INF] Operation 69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f progress: Step 2 - Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:27:23.265 +05:30 [INF] Creating registry backup for: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:27:23.351 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_192723.reg
2025-06-29 19:27:23.595 +05:30 [INF] [69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f] Step 3: Backup completed
2025-06-29 19:27:23.595 +05:30 [INF] Operation 69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f progress: Step 3 - Backup completed
2025-06-29 19:27:23.595 +05:30 [INF] Completed operation 69ac5e55-7b99-4b6c-bdb5-cbac5ff1f34f: Create Optimization Backup. Success: true, Duration: "00:00:00.3313161"
2025-06-29 19:27:23.595 +05:30 [INF] Successfully created optimization backup: optimization_power_high_performance_20250629_192723
2025-06-29 19:27:23.595 +05:30 [INF] Created optimization backup optimization_power_high_performance_20250629_192723 for: power_high_performance
2025-06-29 19:27:23.767 +05:30 [INF] Successfully applied optimization: power_high_performance
2025-06-29 19:27:23.767 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Successfully applied optimization: High Performance Power Plan
2025-06-29 19:27:23.767 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 3: Applying Multi-Core Power Settings...
2025-06-29 19:27:23.768 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 3 - Applying Multi-Core Power Settings...
2025-06-29 19:27:23.768 +05:30 [INF] Applying optimization: multicore_power_settings
2025-06-29 19:27:23.768 +05:30 [INF] Creating system restore point: Before applying multicore_power_settings
2025-06-29 19:27:23.768 +05:30 [INF] [d5625f68-190b-45a8-bc4d-f629c573d17a] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:27:23.768 +05:30 [INF] Started operation d5625f68-190b-45a8-bc4d-f629c573d17a: Create System Restore Point with 3 steps
2025-06-29 19:27:23.768 +05:30 [INF] [d5625f68-190b-45a8-bc4d-f629c573d17a] Step 1: Initializing restore point creation...
2025-06-29 19:27:23.768 +05:30 [INF] Operation d5625f68-190b-45a8-bc4d-f629c573d17a progress: Step 1 - Initializing restore point creation...
2025-06-29 19:27:23.768 +05:30 [INF] [d5625f68-190b-45a8-bc4d-f629c573d17a] Step 2: Creating restore point...
2025-06-29 19:27:23.768 +05:30 [INF] Operation d5625f68-190b-45a8-bc4d-f629c573d17a progress: Step 2 - Creating restore point...
2025-06-29 19:27:24.537 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:24.539 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:24.585 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:26.156 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:26.156 +05:30 [ERR] [d5625f68-190b-45a8-bc4d-f629c573d17a] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:26.156 +05:30 [INF] Completed operation d5625f68-190b-45a8-bc4d-f629c573d17a: Create System Restore Point. Success: false, Duration: "00:00:02.3881567"
2025-06-29 19:27:26.156 +05:30 [WRN] Failed to apply optimization: multicore_power_settings
2025-06-29 19:27:26.158 +05:30 [WRN] [e843e6b3-ae21-420b-b973-2875f25bc165] Failed to apply optimization: Multi-Core Power Settings
2025-06-29 19:27:26.159 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 4: Applying Optimize Visual Effects for Performance...
2025-06-29 19:27:26.159 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 4 - Applying Optimize Visual Effects for Performance...
2025-06-29 19:27:26.159 +05:30 [INF] Applying optimization: visual_effects_performance
2025-06-29 19:27:26.159 +05:30 [INF] Creating optimization backup for: visual_effects_performance
2025-06-29 19:27:26.159 +05:30 [INF] [8676c485-ac2e-4384-be22-0713e2096bfd] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:27:26.159 +05:30 [INF] Started operation 8676c485-ac2e-4384-be22-0713e2096bfd: Create Optimization Backup with 3 steps
2025-06-29 19:27:26.159 +05:30 [INF] [8676c485-ac2e-4384-be22-0713e2096bfd] Step 1: Initializing backup...
2025-06-29 19:27:26.159 +05:30 [INF] Operation 8676c485-ac2e-4384-be22-0713e2096bfd progress: Step 1 - Initializing backup...
2025-06-29 19:27:26.159 +05:30 [INF] [8676c485-ac2e-4384-be22-0713e2096bfd] Step 2: Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:27:26.159 +05:30 [INF] Operation 8676c485-ac2e-4384-be22-0713e2096bfd progress: Step 2 - Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:27:26.159 +05:30 [INF] Creating registry backup for: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:27:26.196 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_192726.reg
2025-06-29 19:27:26.232 +05:30 [INF] [8676c485-ac2e-4384-be22-0713e2096bfd] Step 3: Backup completed
2025-06-29 19:27:26.232 +05:30 [INF] Operation 8676c485-ac2e-4384-be22-0713e2096bfd progress: Step 3 - Backup completed
2025-06-29 19:27:26.232 +05:30 [INF] Completed operation 8676c485-ac2e-4384-be22-0713e2096bfd: Create Optimization Backup. Success: true, Duration: "00:00:00.0734709"
2025-06-29 19:27:26.232 +05:30 [INF] Successfully created optimization backup: optimization_visual_effects_performance_20250629_192726
2025-06-29 19:27:26.232 +05:30 [INF] Created optimization backup optimization_visual_effects_performance_20250629_192726 for: visual_effects_performance
2025-06-29 19:27:26.237 +05:30 [INF] Set registry value: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects\VisualFXSetting = 2
2025-06-29 19:27:26.237 +05:30 [INF] Successfully applied optimization: visual_effects_performance
2025-06-29 19:27:26.237 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Successfully applied optimization: Optimize Visual Effects for Performance
2025-06-29 19:27:26.237 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 5: Applying Temporary Files Cleanup...
2025-06-29 19:27:26.237 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 5 - Applying Temporary Files Cleanup...
2025-06-29 19:27:26.237 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 19:27:26.237 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 19:27:26.237 +05:30 [INF] [c73942be-b4d5-4fb3-a02d-d097492d589a] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:27:26.237 +05:30 [INF] Started operation c73942be-b4d5-4fb3-a02d-d097492d589a: Create System Restore Point with 3 steps
2025-06-29 19:27:26.237 +05:30 [INF] [c73942be-b4d5-4fb3-a02d-d097492d589a] Step 1: Initializing restore point creation...
2025-06-29 19:27:26.237 +05:30 [INF] Operation c73942be-b4d5-4fb3-a02d-d097492d589a progress: Step 1 - Initializing restore point creation...
2025-06-29 19:27:26.237 +05:30 [INF] [c73942be-b4d5-4fb3-a02d-d097492d589a] Step 2: Creating restore point...
2025-06-29 19:27:26.237 +05:30 [INF] Operation c73942be-b4d5-4fb3-a02d-d097492d589a progress: Step 2 - Creating restore point...
2025-06-29 19:27:26.528 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:26.530 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:26.582 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:28.530 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:28.533 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:28.583 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:28.617 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:28.617 +05:30 [ERR] [c73942be-b4d5-4fb3-a02d-d097492d589a] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:27:28.617 +05:30 [INF] Completed operation c73942be-b4d5-4fb3-a02d-d097492d589a: Create System Restore Point. Success: false, Duration: "00:00:02.3799061"
2025-06-29 19:27:28.618 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 19:27:28.619 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Successfully applied optimization: Temporary Files Cleanup
2025-06-29 19:27:28.619 +05:30 [INF] [e843e6b3-ae21-420b-b973-2875f25bc165] Step 6: Optimization completed
2025-06-29 19:27:28.619 +05:30 [INF] Operation e843e6b3-ae21-420b-b973-2875f25bc165 progress: Step 6 - Optimization completed
2025-06-29 19:27:28.619 +05:30 [INF] Completed operation e843e6b3-ae21-420b-b973-2875f25bc165: Quick Optimization. Success: true, Duration: "00:00:07.6786386"
2025-06-29 19:27:28.619 +05:30 [INF] Quick optimization completed. Applied: 3, Failed: 1, Duration: "00:00:11.1340350"
2025-06-29 19:27:29.728 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:29.738 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:30.506 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:30.508 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:30.550 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:30.953 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:27:30.963 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:27:32.510 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:27:32.512 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:27:32.555 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:27:34.160 +05:30 [INF] Stopping performance monitoring
2025-06-29 19:28:57.845 +05:30 [INF] Performance counters initialized successfully
2025-06-29 19:28:59.006 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:28:59.027 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:00.260 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:00.269 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:02.590 +05:30 [INF] Starting performance monitoring
2025-06-29 19:29:04.673 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:04.677 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:04.731 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:05.437 +05:30 [INF] Creating system restore point: PC Optimizer Pro - Pre-optimization backup
2025-06-29 19:29:06.652 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:06.654 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:06.685 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:07.735 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'PC Optimizer Pro - Pre-optimization ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:08.680 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:08.683 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:08.727 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:10.673 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:10.675 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:10.714 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:12.676 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:12.679 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:12.716 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:14.668 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:14.670 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:14.715 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:14.776 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:14.782 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:16.664 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:16.667 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:16.705 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:18.681 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:18.683 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:18.730 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:19.796 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:19.806 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:20.662 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:20.663 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:20.704 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:22.674 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:22.676 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:22.718 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:24.670 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:24.672 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:24.718 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:24.772 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:24.775 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:26.664 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:26.666 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:26.706 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:28.685 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:28.687 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:28.730 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:29.794 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:29.803 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:30.660 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:30.661 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:30.701 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:32.718 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:32.720 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:32.761 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:34.705 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:34.706 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:34.760 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:34.853 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:34.859 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:36.695 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:36.697 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:36.734 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:38.193 +05:30 [INF] Starting quick optimization
2025-06-29 19:29:38.715 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:38.716 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:38.764 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:39.336 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:29:39.839 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:39.849 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:40.485 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:29:40.698 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:40.702 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:40.740 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:41.642 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 0 GB, Usage: 100.0%
2025-06-29 19:29:41.670 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Started operation: Quick Optimization with 6 steps
2025-06-29 19:29:41.670 +05:30 [INF] Started operation cb9ab1a7-8d41-441c-9d89-79607fe84ead: Quick Optimization with 6 steps
2025-06-29 19:29:41.670 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 1: Creating system backup...
2025-06-29 19:29:41.671 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 1 - Creating system backup...
2025-06-29 19:29:41.671 +05:30 [INF] Creating system restore point: Quick Optimize
2025-06-29 19:29:41.671 +05:30 [INF] [537f7a0f-bee7-401e-aa42-4272230d83fa] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:29:41.671 +05:30 [INF] Started operation 537f7a0f-bee7-401e-aa42-4272230d83fa: Create System Restore Point with 3 steps
2025-06-29 19:29:41.671 +05:30 [INF] [537f7a0f-bee7-401e-aa42-4272230d83fa] Step 1: Initializing restore point creation...
2025-06-29 19:29:41.671 +05:30 [INF] Operation 537f7a0f-bee7-401e-aa42-4272230d83fa progress: Step 1 - Initializing restore point creation...
2025-06-29 19:29:41.671 +05:30 [INF] [537f7a0f-bee7-401e-aa42-4272230d83fa] Step 2: Creating restore point...
2025-06-29 19:29:41.671 +05:30 [INF] Operation 537f7a0f-bee7-401e-aa42-4272230d83fa progress: Step 2 - Creating restore point...
2025-06-29 19:29:42.730 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:42.734 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:42.775 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:44.028 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:44.029 +05:30 [ERR] [537f7a0f-bee7-401e-aa42-4272230d83fa] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:44.031 +05:30 [INF] Completed operation 537f7a0f-bee7-401e-aa42-4272230d83fa: Create System Restore Point. Success: false, Duration: "00:00:02.3592116"
2025-06-29 19:29:44.034 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 2: Applying High Performance Power Plan...
2025-06-29 19:29:44.034 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 2 - Applying High Performance Power Plan...
2025-06-29 19:29:44.036 +05:30 [INF] Applying optimization: power_high_performance
2025-06-29 19:29:44.041 +05:30 [INF] Creating optimization backup for: power_high_performance
2025-06-29 19:29:44.041 +05:30 [INF] [01cc9d92-0822-4a5c-b3ac-3772e62e0700] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:29:44.041 +05:30 [INF] Started operation 01cc9d92-0822-4a5c-b3ac-3772e62e0700: Create Optimization Backup with 3 steps
2025-06-29 19:29:44.041 +05:30 [INF] [01cc9d92-0822-4a5c-b3ac-3772e62e0700] Step 1: Initializing backup...
2025-06-29 19:29:44.041 +05:30 [INF] Operation 01cc9d92-0822-4a5c-b3ac-3772e62e0700 progress: Step 1 - Initializing backup...
2025-06-29 19:29:44.041 +05:30 [INF] [01cc9d92-0822-4a5c-b3ac-3772e62e0700] Step 2: Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:29:44.041 +05:30 [INF] Operation 01cc9d92-0822-4a5c-b3ac-3772e62e0700 progress: Step 2 - Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:29:44.041 +05:30 [INF] Creating registry backup for: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:29:44.116 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_192944.reg
2025-06-29 19:29:44.231 +05:30 [INF] [01cc9d92-0822-4a5c-b3ac-3772e62e0700] Step 3: Backup completed
2025-06-29 19:29:44.231 +05:30 [INF] Operation 01cc9d92-0822-4a5c-b3ac-3772e62e0700 progress: Step 3 - Backup completed
2025-06-29 19:29:44.231 +05:30 [INF] Completed operation 01cc9d92-0822-4a5c-b3ac-3772e62e0700: Create Optimization Backup. Success: true, Duration: "00:00:00.1908135"
2025-06-29 19:29:44.232 +05:30 [INF] Successfully created optimization backup: optimization_power_high_performance_20250629_192944
2025-06-29 19:29:44.232 +05:30 [INF] Created optimization backup optimization_power_high_performance_20250629_192944 for: power_high_performance
2025-06-29 19:29:44.415 +05:30 [INF] Successfully applied optimization: power_high_performance
2025-06-29 19:29:44.415 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Successfully applied optimization: High Performance Power Plan
2025-06-29 19:29:44.415 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 3: Applying Multi-Core Power Settings...
2025-06-29 19:29:44.415 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 3 - Applying Multi-Core Power Settings...
2025-06-29 19:29:44.415 +05:30 [INF] Applying optimization: multicore_power_settings
2025-06-29 19:29:44.415 +05:30 [INF] Creating system restore point: Before applying multicore_power_settings
2025-06-29 19:29:44.415 +05:30 [INF] [5b20d24b-328c-4692-82fc-7568c49ae0c7] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:29:44.415 +05:30 [INF] Started operation 5b20d24b-328c-4692-82fc-7568c49ae0c7: Create System Restore Point with 3 steps
2025-06-29 19:29:44.415 +05:30 [INF] [5b20d24b-328c-4692-82fc-7568c49ae0c7] Step 1: Initializing restore point creation...
2025-06-29 19:29:44.415 +05:30 [INF] Operation 5b20d24b-328c-4692-82fc-7568c49ae0c7 progress: Step 1 - Initializing restore point creation...
2025-06-29 19:29:44.415 +05:30 [INF] [5b20d24b-328c-4692-82fc-7568c49ae0c7] Step 2: Creating restore point...
2025-06-29 19:29:44.415 +05:30 [INF] Operation 5b20d24b-328c-4692-82fc-7568c49ae0c7 progress: Step 2 - Creating restore point...
2025-06-29 19:29:44.729 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:44.732 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:44.790 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:46.737 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:46.739 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:46.782 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:46.782 +05:30 [ERR] [5b20d24b-328c-4692-82fc-7568c49ae0c7] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:46.782 +05:30 [INF] Completed operation 5b20d24b-328c-4692-82fc-7568c49ae0c7: Create System Restore Point. Success: false, Duration: "00:00:02.3670005"
2025-06-29 19:29:46.782 +05:30 [WRN] Failed to apply optimization: multicore_power_settings
2025-06-29 19:29:46.783 +05:30 [WRN] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Failed to apply optimization: Multi-Core Power Settings
2025-06-29 19:29:46.783 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 4: Applying Optimize Visual Effects for Performance...
2025-06-29 19:29:46.783 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 4 - Applying Optimize Visual Effects for Performance...
2025-06-29 19:29:46.783 +05:30 [INF] Applying optimization: visual_effects_performance
2025-06-29 19:29:46.783 +05:30 [INF] Creating optimization backup for: visual_effects_performance
2025-06-29 19:29:46.783 +05:30 [INF] [3085c58a-4091-4b50-9ccb-c4b252d8a403] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:29:46.783 +05:30 [INF] Started operation 3085c58a-4091-4b50-9ccb-c4b252d8a403: Create Optimization Backup with 3 steps
2025-06-29 19:29:46.783 +05:30 [INF] [3085c58a-4091-4b50-9ccb-c4b252d8a403] Step 1: Initializing backup...
2025-06-29 19:29:46.783 +05:30 [INF] Operation 3085c58a-4091-4b50-9ccb-c4b252d8a403 progress: Step 1 - Initializing backup...
2025-06-29 19:29:46.783 +05:30 [INF] [3085c58a-4091-4b50-9ccb-c4b252d8a403] Step 2: Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:29:46.783 +05:30 [INF] Operation 3085c58a-4091-4b50-9ccb-c4b252d8a403 progress: Step 2 - Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:29:46.783 +05:30 [INF] Creating registry backup for: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:29:46.797 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:46.827 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_192946.reg
2025-06-29 19:29:46.864 +05:30 [INF] [3085c58a-4091-4b50-9ccb-c4b252d8a403] Step 3: Backup completed
2025-06-29 19:29:46.864 +05:30 [INF] Operation 3085c58a-4091-4b50-9ccb-c4b252d8a403 progress: Step 3 - Backup completed
2025-06-29 19:29:46.864 +05:30 [INF] Completed operation 3085c58a-4091-4b50-9ccb-c4b252d8a403: Create Optimization Backup. Success: true, Duration: "00:00:00.0812088"
2025-06-29 19:29:46.864 +05:30 [INF] Successfully created optimization backup: optimization_visual_effects_performance_20250629_192946
2025-06-29 19:29:46.864 +05:30 [INF] Created optimization backup optimization_visual_effects_performance_20250629_192946 for: visual_effects_performance
2025-06-29 19:29:46.869 +05:30 [INF] Set registry value: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects\VisualFXSetting = 2
2025-06-29 19:29:46.869 +05:30 [INF] Successfully applied optimization: visual_effects_performance
2025-06-29 19:29:46.869 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Successfully applied optimization: Optimize Visual Effects for Performance
2025-06-29 19:29:46.870 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 5: Applying Temporary Files Cleanup...
2025-06-29 19:29:46.870 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 5 - Applying Temporary Files Cleanup...
2025-06-29 19:29:46.870 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 19:29:46.870 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 19:29:46.870 +05:30 [INF] [6e9b2419-85b9-406d-9202-a8620f643e6e] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:29:46.870 +05:30 [INF] Started operation 6e9b2419-85b9-406d-9202-a8620f643e6e: Create System Restore Point with 3 steps
2025-06-29 19:29:46.870 +05:30 [INF] [6e9b2419-85b9-406d-9202-a8620f643e6e] Step 1: Initializing restore point creation...
2025-06-29 19:29:46.870 +05:30 [INF] Operation 6e9b2419-85b9-406d-9202-a8620f643e6e progress: Step 1 - Initializing restore point creation...
2025-06-29 19:29:46.870 +05:30 [INF] [6e9b2419-85b9-406d-9202-a8620f643e6e] Step 2: Creating restore point...
2025-06-29 19:29:46.870 +05:30 [INF] Operation 6e9b2419-85b9-406d-9202-a8620f643e6e progress: Step 2 - Creating restore point...
2025-06-29 19:29:48.708 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:48.711 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:48.753 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:49.226 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:49.226 +05:30 [ERR] [6e9b2419-85b9-406d-9202-a8620f643e6e] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:29:49.226 +05:30 [INF] Completed operation 6e9b2419-85b9-406d-9202-a8620f643e6e: Create System Restore Point. Success: false, Duration: "00:00:02.3568834"
2025-06-29 19:29:49.229 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 19:29:49.230 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Successfully applied optimization: Temporary Files Cleanup
2025-06-29 19:29:49.230 +05:30 [INF] [cb9ab1a7-8d41-441c-9d89-79607fe84ead] Step 6: Optimization completed
2025-06-29 19:29:49.230 +05:30 [INF] Operation cb9ab1a7-8d41-441c-9d89-79607fe84ead progress: Step 6 - Optimization completed
2025-06-29 19:29:49.230 +05:30 [INF] Completed operation cb9ab1a7-8d41-441c-9d89-79607fe84ead: Quick Optimization. Success: true, Duration: "00:00:07.5616410"
2025-06-29 19:29:49.230 +05:30 [INF] Quick optimization completed. Applied: 3, Failed: 1, Duration: "00:00:11.0365849"
2025-06-29 19:29:50.338 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:50.348 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:50.706 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:50.708 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:50.750 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:51.571 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:29:51.580 +05:30 [DBG] CPU temperature from thermal zone: 91.05000000000001°C
2025-06-29 19:29:52.713 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:52.714 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:52.763 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:54.715 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 91.1°C
2025-06-29 19:29:54.717 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:29:54.764 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:29:55.307 +05:30 [INF] Stopping performance monitoring
2025-06-29 19:30:47.564 +05:30 [INF] Performance counters initialized successfully
2025-06-29 19:30:48.714 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:30:48.734 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:30:49.952 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:30:49.962 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:30:52.282 +05:30 [INF] Starting performance monitoring
2025-06-29 19:30:54.109 +05:30 [INF] Starting quick optimization
2025-06-29 19:30:54.383 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:30:54.389 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:30:54.440 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:30:55.240 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 1 GB, Usage: 93.3%
2025-06-29 19:30:56.364 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 1 GB, Usage: 93.3%
2025-06-29 19:30:56.364 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:30:56.366 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:30:56.395 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:30:57.496 +05:30 [INF] Hardware Memory Debug - Total: 15 GB, Available: 1 GB, Usage: 93.3%
2025-06-29 19:30:57.521 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Started operation: Quick Optimization with 6 steps
2025-06-29 19:30:57.522 +05:30 [INF] Started operation 10abc71a-e949-46e2-ae73-4d1592f86fa6: Quick Optimization with 6 steps
2025-06-29 19:30:57.522 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 1: Creating system backup...
2025-06-29 19:30:57.523 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 1 - Creating system backup...
2025-06-29 19:30:57.523 +05:30 [INF] Creating system restore point: Quick Optimize
2025-06-29 19:30:57.523 +05:30 [INF] [f5786822-f91f-4db4-959b-ff0af7f8903b] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:30:57.523 +05:30 [INF] Started operation f5786822-f91f-4db4-959b-ff0af7f8903b: Create System Restore Point with 3 steps
2025-06-29 19:30:57.524 +05:30 [INF] [f5786822-f91f-4db4-959b-ff0af7f8903b] Step 1: Initializing restore point creation...
2025-06-29 19:30:57.524 +05:30 [INF] Operation f5786822-f91f-4db4-959b-ff0af7f8903b progress: Step 1 - Initializing restore point creation...
2025-06-29 19:30:57.525 +05:30 [INF] [f5786822-f91f-4db4-959b-ff0af7f8903b] Step 2: Creating restore point...
2025-06-29 19:30:57.525 +05:30 [INF] Operation f5786822-f91f-4db4-959b-ff0af7f8903b progress: Step 2 - Creating restore point...
2025-06-29 19:30:58.389 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:30:58.391 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:30:58.434 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:30:59.881 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:30:59.882 +05:30 [ERR] [f5786822-f91f-4db4-959b-ff0af7f8903b] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:30:59.884 +05:30 [INF] Completed operation f5786822-f91f-4db4-959b-ff0af7f8903b: Create System Restore Point. Success: false, Duration: "00:00:02.3591394"
2025-06-29 19:30:59.885 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 2: Applying High Performance Power Plan...
2025-06-29 19:30:59.885 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 2 - Applying High Performance Power Plan...
2025-06-29 19:30:59.887 +05:30 [INF] Applying optimization: power_high_performance
2025-06-29 19:30:59.893 +05:30 [INF] Creating optimization backup for: power_high_performance
2025-06-29 19:30:59.893 +05:30 [INF] [ddf0578c-0782-4ad4-9ec9-1f69fd650206] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:30:59.893 +05:30 [INF] Started operation ddf0578c-0782-4ad4-9ec9-1f69fd650206: Create Optimization Backup with 3 steps
2025-06-29 19:30:59.893 +05:30 [INF] [ddf0578c-0782-4ad4-9ec9-1f69fd650206] Step 1: Initializing backup...
2025-06-29 19:30:59.893 +05:30 [INF] Operation ddf0578c-0782-4ad4-9ec9-1f69fd650206 progress: Step 1 - Initializing backup...
2025-06-29 19:30:59.893 +05:30 [INF] [ddf0578c-0782-4ad4-9ec9-1f69fd650206] Step 2: Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:30:59.893 +05:30 [INF] Operation ddf0578c-0782-4ad4-9ec9-1f69fd650206 progress: Step 2 - Backing up registry key: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:30:59.894 +05:30 [INF] Creating registry backup for: HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Power
2025-06-29 19:30:59.969 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_193059.reg
2025-06-29 19:31:00.075 +05:30 [INF] [ddf0578c-0782-4ad4-9ec9-1f69fd650206] Step 3: Backup completed
2025-06-29 19:31:00.075 +05:30 [INF] Operation ddf0578c-0782-4ad4-9ec9-1f69fd650206 progress: Step 3 - Backup completed
2025-06-29 19:31:00.075 +05:30 [INF] Completed operation ddf0578c-0782-4ad4-9ec9-1f69fd650206: Create Optimization Backup. Success: true, Duration: "00:00:00.1824436"
2025-06-29 19:31:00.075 +05:30 [INF] Successfully created optimization backup: optimization_power_high_performance_20250629_193059
2025-06-29 19:31:00.075 +05:30 [INF] Created optimization backup optimization_power_high_performance_20250629_193059 for: power_high_performance
2025-06-29 19:31:00.219 +05:30 [INF] Successfully applied optimization: power_high_performance
2025-06-29 19:31:00.219 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Successfully applied optimization: High Performance Power Plan
2025-06-29 19:31:00.219 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 3: Applying Multi-Core Power Settings...
2025-06-29 19:31:00.219 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 3 - Applying Multi-Core Power Settings...
2025-06-29 19:31:00.219 +05:30 [INF] Applying optimization: multicore_power_settings
2025-06-29 19:31:00.219 +05:30 [INF] Creating system restore point: Before applying multicore_power_settings
2025-06-29 19:31:00.219 +05:30 [INF] [f614f386-11af-4697-bbb2-81b236df217f] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:31:00.219 +05:30 [INF] Started operation f614f386-11af-4697-bbb2-81b236df217f: Create System Restore Point with 3 steps
2025-06-29 19:31:00.219 +05:30 [INF] [f614f386-11af-4697-bbb2-81b236df217f] Step 1: Initializing restore point creation...
2025-06-29 19:31:00.219 +05:30 [INF] Operation f614f386-11af-4697-bbb2-81b236df217f progress: Step 1 - Initializing restore point creation...
2025-06-29 19:31:00.220 +05:30 [INF] [f614f386-11af-4697-bbb2-81b236df217f] Step 2: Creating restore point...
2025-06-29 19:31:00.220 +05:30 [INF] Operation f614f386-11af-4697-bbb2-81b236df217f progress: Step 2 - Creating restore point...
2025-06-29 19:31:00.411 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:00.414 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:00.456 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:02.389 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:02.389 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:31:02.389 +05:30 [ERR] [f614f386-11af-4697-bbb2-81b236df217f] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:31:02.389 +05:30 [INF] Completed operation f614f386-11af-4697-bbb2-81b236df217f: Create System Restore Point. Success: false, Duration: "00:00:02.1700569"
2025-06-29 19:31:02.390 +05:30 [WRN] Failed to apply optimization: multicore_power_settings
2025-06-29 19:31:02.390 +05:30 [WRN] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Failed to apply optimization: Multi-Core Power Settings
2025-06-29 19:31:02.390 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 4: Applying Optimize Visual Effects for Performance...
2025-06-29 19:31:02.390 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 4 - Applying Optimize Visual Effects for Performance...
2025-06-29 19:31:02.390 +05:30 [INF] Applying optimization: visual_effects_performance
2025-06-29 19:31:02.390 +05:30 [INF] Creating optimization backup for: visual_effects_performance
2025-06-29 19:31:02.390 +05:30 [INF] [c067aaf9-5460-496d-afe4-32552218ac8c] Started operation: Create Optimization Backup with 3 steps
2025-06-29 19:31:02.390 +05:30 [INF] Started operation c067aaf9-5460-496d-afe4-32552218ac8c: Create Optimization Backup with 3 steps
2025-06-29 19:31:02.390 +05:30 [INF] [c067aaf9-5460-496d-afe4-32552218ac8c] Step 1: Initializing backup...
2025-06-29 19:31:02.390 +05:30 [INF] Operation c067aaf9-5460-496d-afe4-32552218ac8c progress: Step 1 - Initializing backup...
2025-06-29 19:31:02.390 +05:30 [INF] [c067aaf9-5460-496d-afe4-32552218ac8c] Step 2: Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:31:02.390 +05:30 [INF] Operation c067aaf9-5460-496d-afe4-32552218ac8c progress: Step 2 - Backing up registry key: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:31:02.390 +05:30 [INF] Creating registry backup for: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects
2025-06-29 19:31:02.391 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:02.427 +05:30 [INF] Successfully created registry backup: C:\Users\<USER>\AppData\Roaming\PCOptimizer\Backups\registry_backup_20250629_193102.reg
2025-06-29 19:31:02.432 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:02.458 +05:30 [INF] [c067aaf9-5460-496d-afe4-32552218ac8c] Step 3: Backup completed
2025-06-29 19:31:02.459 +05:30 [INF] Operation c067aaf9-5460-496d-afe4-32552218ac8c progress: Step 3 - Backup completed
2025-06-29 19:31:02.459 +05:30 [INF] Completed operation c067aaf9-5460-496d-afe4-32552218ac8c: Create Optimization Backup. Success: true, Duration: "00:00:00.0684623"
2025-06-29 19:31:02.459 +05:30 [INF] Successfully created optimization backup: optimization_visual_effects_performance_20250629_193102
2025-06-29 19:31:02.459 +05:30 [INF] Created optimization backup optimization_visual_effects_performance_20250629_193102 for: visual_effects_performance
2025-06-29 19:31:02.461 +05:30 [INF] Set registry value: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects\VisualFXSetting = 2
2025-06-29 19:31:02.461 +05:30 [INF] Successfully applied optimization: visual_effects_performance
2025-06-29 19:31:02.461 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Successfully applied optimization: Optimize Visual Effects for Performance
2025-06-29 19:31:02.461 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 5: Applying Temporary Files Cleanup...
2025-06-29 19:31:02.461 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 5 - Applying Temporary Files Cleanup...
2025-06-29 19:31:02.461 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 19:31:02.461 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 19:31:02.461 +05:30 [INF] [06bdf97b-7467-4eb2-a7cd-e6a047173d23] Started operation: Create System Restore Point with 3 steps
2025-06-29 19:31:02.461 +05:30 [INF] Started operation 06bdf97b-7467-4eb2-a7cd-e6a047173d23: Create System Restore Point with 3 steps
2025-06-29 19:31:02.461 +05:30 [INF] [06bdf97b-7467-4eb2-a7cd-e6a047173d23] Step 1: Initializing restore point creation...
2025-06-29 19:31:02.461 +05:30 [INF] Operation 06bdf97b-7467-4eb2-a7cd-e6a047173d23 progress: Step 1 - Initializing restore point creation...
2025-06-29 19:31:02.461 +05:30 [INF] [06bdf97b-7467-4eb2-a7cd-e6a047173d23] Step 2: Creating restore point...
2025-06-29 19:31:02.461 +05:30 [INF] Operation 06bdf97b-7467-4eb2-a7cd-e6a047173d23 progress: Step 2 - Creating restore point...
2025-06-29 19:31:04.371 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:04.372 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:04.407 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:04.667 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:31:04.667 +05:30 [ERR] [06bdf97b-7467-4eb2-a7cd-e6a047173d23] Failed to create restore point: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 19:31:04.667 +05:30 [INF] Completed operation 06bdf97b-7467-4eb2-a7cd-e6a047173d23: Create System Restore Point. Success: false, Duration: "00:00:02.2060297"
2025-06-29 19:31:04.669 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 19:31:04.669 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Successfully applied optimization: Temporary Files Cleanup
2025-06-29 19:31:04.669 +05:30 [INF] [10abc71a-e949-46e2-ae73-4d1592f86fa6] Step 6: Optimization completed
2025-06-29 19:31:04.669 +05:30 [INF] Operation 10abc71a-e949-46e2-ae73-4d1592f86fa6 progress: Step 6 - Optimization completed
2025-06-29 19:31:04.669 +05:30 [INF] Completed operation 10abc71a-e949-46e2-ae73-4d1592f86fa6: Quick Optimization. Success: true, Duration: "00:00:07.1501770"
2025-06-29 19:31:04.670 +05:30 [INF] Quick optimization completed. Applied: 3, Failed: 1, Duration: "00:00:10.5605684"
2025-06-29 19:31:05.760 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:05.769 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:06.361 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:06.362 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:06.402 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:06.957 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:06.966 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:08.371 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:08.372 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:08.408 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:10.369 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:10.370 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:10.412 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:12.367 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:12.369 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:12.405 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:14.367 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:14.369 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:14.411 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:14.466 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:14.470 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:16.368 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:16.369 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:16.404 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:18.378 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:18.380 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:18.419 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:19.471 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:19.480 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:20.386 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:20.390 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:20.439 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:22.401 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:22.403 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:22.438 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:24.406 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:24.409 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:24.452 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:24.621 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:24.625 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:26.397 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:26.398 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:26.430 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:28.416 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:28.418 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:28.462 +05:30 [INF] No GPU temperature sensors available, using estimation
2025-06-29 19:31:29.520 +05:30 [INF] CPU Details - Name: 11th Gen Intel Core i5-11300H @ 3.10GHz, Cores: 4, Speed: 3.11GHz, Arch: 9, Manufacturer: GenuineIntel
2025-06-29 19:31:29.528 +05:30 [DBG] CPU temperature from thermal zone: 92.05000000000001°C
2025-06-29 19:31:30.402 +05:30 [INF] CPU Temperature from MSAcpi_ThermalZoneTemperature: 92.1°C
2025-06-29 19:31:30.404 +05:30 [DBG] OpenHardwareMonitor GPU temperature not available
System.Management.ManagementException: Invalid namespace 
   at System.Management.ManagementException.ThrowWithExtendedInfo(ManagementStatus errorCode)
   at System.Management.ManagementScope.InitializeGuts(Object o)
   at System.Management.ManagementScope.Initialize()
   at System.Management.ManagementObjectSearcher.Initialize()
   at System.Management.ManagementObjectSearcher.Get()
   at PCOptimizerApp.Services.PerformanceMonitoringService.GetGpuTemperatureFromWMI() in D:\OneDrive\personal\Coding\PCfast\PCOptimizerApp\Services\PerformanceMonitoringService.cs:line 635
2025-06-29 19:31:30.444 +05:30 [INF] No GPU temperature sensors available, using estimation
