2025-06-29 16:13:56.314 +05:30 [INF] Performance counters initialized successfully
2025-06-29 16:28:19.627 +05:30 [INF] Performance counters initialized successfully
2025-06-29 16:28:24.576 +05:30 [INF] Starting performance monitoring
2025-06-29 16:33:31.600 +05:30 [INF] Starting quick optimization
2025-06-29 16:33:33.907 +05:30 [INF] Creating system restore point: Quick Optimize
2025-06-29 16:33:37.008 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Quick Optimize' -RestorePointType ' ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:37.010 +05:30 [INF] Applying optimization: power_high_performance
2025-06-29 16:33:37.011 +05:30 [INF] Creating system restore point: Before applying power_high_performance
2025-06-29 16:33:39.516 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying power_high_performa ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:39.723 +05:30 [INF] Successfully applied optimization: power_high_performance
2025-06-29 16:33:39.724 +05:30 [INF] Applying optimization: multicore_power_settings
2025-06-29 16:33:39.724 +05:30 [INF] Creating system restore point: Before applying multicore_power_settings
2025-06-29 16:33:42.184 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying multicore_power_set ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:42.184 +05:30 [WRN] Failed to apply optimization: multicore_power_settings
2025-06-29 16:33:42.184 +05:30 [INF] Applying optimization: visual_effects_performance
2025-06-29 16:33:42.184 +05:30 [INF] Creating system restore point: Before applying visual_effects_performance
2025-06-29 16:33:44.522 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying visual_effects_perf ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:44.525 +05:30 [INF] Set registry value: HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects\VisualFXSetting = 2
2025-06-29 16:33:44.527 +05:30 [INF] Successfully applied optimization: visual_effects_performance
2025-06-29 16:33:44.527 +05:30 [INF] Applying optimization: temporary_files_cleanup
2025-06-29 16:33:44.527 +05:30 [INF] Creating system restore point: Before applying temporary_files_cleanup
2025-06-29 16:33:46.954 +05:30 [WRN] Failed to create system restore point. Error: Checkpoint-Computer : This command cannot be run due to the following error: the service cannot be started because it 
is disabled or does not have enabled devices associated with it.
At line:1 char:1
+ Checkpoint-Computer -Description 'Before applying temporary_files_cle ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidOperation: (:) [Checkpoint-Computer], ArgumentException
    + FullyQualifiedErrorId : ServiceDisabled,Microsoft.PowerShell.Commands.CheckpointComputerCommand
 

2025-06-29 16:33:47.227 +05:30 [INF] Successfully applied optimization: temporary_files_cleanup
2025-06-29 16:33:47.228 +05:30 [INF] Quick optimization completed. Applied: 3, Failed: 1, Duration: "00:00:15.6272410"
2025-06-29 16:51:50.071 +05:30 [INF] Stopping performance monitoring
