# PC Optimizer Pro Launcher with Auto-Elevation
# This script will automatically request administrator privileges

param(
    [switch]$AsAdmin
)

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin -and -not $AsAdmin) {
    Write-Host "PC Optimizer Pro requires Administrator privileges." -ForegroundColor Yellow
    Write-Host "Requesting elevation..." -ForegroundColor Cyan
    
    try {
        # Re-run this script with administrator privileges
        Start-Process PowerShell -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`" -AsAdmin" -Verb RunAs
        exit
    }
    catch {
        Write-Host "Failed to elevate privileges. Please run as administrator manually." -ForegroundColor Red
        Write-Host "Right-click PowerShell and select 'Run as administrator', then run this script." -ForegroundColor Yellow
        pause
        exit 1
    }
}

if ($isAdmin) {
    Write-Host "✅ Running with Administrator privileges" -ForegroundColor Green
} else {
    Write-Host "⚠️  Warning: Not running as Administrator" -ForegroundColor Yellow
    Write-Host "Some features may not work properly." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Starting PC Optimizer Pro..." -ForegroundColor Cyan
Write-Host ""

# Set location to the application directory
$appPath = Split-Path -Parent $PSCommandPath
Set-Location $appPath

# Run the application
try {
    dotnet run --project "PCOptimizerApp.csproj"
}
catch {
    Write-Host "Error starting application: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "Make sure .NET 8 is installed and the application is built successfully." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
