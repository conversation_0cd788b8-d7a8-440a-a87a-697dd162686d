﻿#pragma checksum "..\..\..\..\Views\LiveOptimizationPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8142AB619CFBD06F2B227C2C0289266F41A92971"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PCOptimizerApp.Views {
    
    
    /// <summary>
    /// LiveOptimizationPage
    /// </summary>
    public partial class LiveOptimizationPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 66 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OverallProgressText;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartOptimizationButton;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PauseResumeButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentOptimizationIcon;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentOptimizationTitle;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentOptimizationStatus;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CurrentOptimizationProgress;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WhyThisMattersText;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpectedImpactText;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TechnicalDetailsText;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ProgressHistoryScroll;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl ProgressHistoryList;
        
        #line default
        #line hidden
        
        
        #line 208 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BootTimeImprovement;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BootTimeDetails;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppLoadImprovement;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppLoadDetails;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryImprovement;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryDetails;
        
        #line default
        #line hidden
        
        
        #line 232 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskImprovement;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskDetails;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MoneyValueText;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeSavedText;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Views\LiveOptimizationPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OptimizationsAppliedText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PCOptimizerApp;V1.0.0.0;component/views/liveoptimizationpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\LiveOptimizationPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Views\LiveOptimizationPage.xaml"
            ((PCOptimizerApp.Views.LiveOptimizationPage)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.Page_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OverallProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.StartOptimizationButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Views\LiveOptimizationPage.xaml"
            this.StartOptimizationButton.Click += new System.Windows.RoutedEventHandler(this.StartOptimizationButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PauseResumeButton = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\..\Views\LiveOptimizationPage.xaml"
            this.PauseResumeButton.Click += new System.Windows.RoutedEventHandler(this.PauseResumeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CurrentOptimizationIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CurrentOptimizationTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CurrentOptimizationStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.CurrentOptimizationProgress = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 9:
            this.WhyThisMattersText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.ExpectedImpactText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TechnicalDetailsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ProgressHistoryScroll = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 13:
            this.ProgressHistoryList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 14:
            this.BootTimeImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.BootTimeDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.AppLoadImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.AppLoadDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.MemoryImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.MemoryDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.DiskImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.DiskDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.MoneyValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.TimeSavedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.OptimizationsAppliedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

