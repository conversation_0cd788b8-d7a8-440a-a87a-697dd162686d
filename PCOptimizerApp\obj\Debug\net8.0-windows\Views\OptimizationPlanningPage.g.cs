﻿#pragma checksum "..\..\..\..\Views\OptimizationPlanningPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4085C98A7E3BD4FEE77B1BE1A2E05949E41C4F35"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PCOptimizerApp.Views {
    
    
    /// <summary>
    /// OptimizationPlanningPage
    /// </summary>
    public partial class OptimizationPlanningPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 65 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartOptimizationButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshPlanButton;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel OptimizationCategoriesPanel;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuProgressText;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar CpuProgressBar;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl CpuOptimizationsList;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryProgressText;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MemoryProgressBar;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MemoryOptimizationsList;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StorageProgressText;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar StorageProgressBar;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl StorageOptimizationsList;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GraphicsProgressText;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar GraphicsProgressBar;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl GraphicsOptimizationsList;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalOptimizationsCount;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletedOptimizationsCount;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PendingOptimizationsCount;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EstimatedImprovementText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PCOptimizerApp;V1.0.0.0;component/views/optimizationplanningpage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
            ((PCOptimizerApp.Views.OptimizationPlanningPage)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.Page_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StartOptimizationButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
            this.StartOptimizationButton.Click += new System.Windows.RoutedEventHandler(this.StartOptimizationButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.RefreshPlanButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\OptimizationPlanningPage.xaml"
            this.RefreshPlanButton.Click += new System.Windows.RoutedEventHandler(this.RefreshPlanButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.OptimizationCategoriesPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.CpuProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.CpuProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 7:
            this.CpuOptimizationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 8:
            this.MemoryProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.MemoryProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 10:
            this.MemoryOptimizationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 11:
            this.StorageProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.StorageProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 13:
            this.StorageOptimizationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 14:
            this.GraphicsProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.GraphicsProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 16:
            this.GraphicsOptimizationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 17:
            this.TotalOptimizationsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.CompletedOptimizationsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.PendingOptimizationsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.EstimatedImprovementText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

