﻿#pragma checksum "..\..\..\..\Views\OptimizationResultsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5D1E58BF9314B7A1748236E3AAC7772C4D8220B8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PCOptimizerApp.Views {
    
    
    /// <summary>
    /// OptimizationResultsPage
    /// </summary>
    public partial class OptimizationResultsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 65 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShareResultsButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OptimizeMoreButton;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BootTimeBefore;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BootTimeAfter;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BootTimeImprovement;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppLoadBefore;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppLoadAfter;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AppLoadImprovement;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryBefore;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryAfter;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MemoryImprovement;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskBefore;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskAfter;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DiskImprovement;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock EquivalentValue;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeSaved;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OptimizationsApplied;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl OptimizationSummaryList;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl FutureRecommendationsList;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CompletionMessage;
        
        #line default
        #line hidden
        
        
        #line 321 "..\..\..\..\Views\OptimizationResultsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FinishButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PCOptimizerApp;V1.0.0.0;component/views/optimizationresultspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\OptimizationResultsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Views\OptimizationResultsPage.xaml"
            ((PCOptimizerApp.Views.OptimizationResultsPage)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.Page_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ShareResultsButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\Views\OptimizationResultsPage.xaml"
            this.ShareResultsButton.Click += new System.Windows.RoutedEventHandler(this.ShareResultsButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OptimizeMoreButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\OptimizationResultsPage.xaml"
            this.OptimizeMoreButton.Click += new System.Windows.RoutedEventHandler(this.OptimizeMoreButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BootTimeBefore = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BootTimeAfter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BootTimeImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AppLoadBefore = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.AppLoadAfter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.AppLoadImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.MemoryBefore = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MemoryAfter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.MemoryImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.DiskBefore = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.DiskAfter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.DiskImprovement = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.EquivalentValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TimeSaved = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.OptimizationsApplied = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.OptimizationSummaryList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 20:
            this.FutureRecommendationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 21:
            this.CompletionMessage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FinishButton = ((System.Windows.Controls.Button)(target));
            
            #line 323 "..\..\..\..\Views\OptimizationResultsPage.xaml"
            this.FinishButton.Click += new System.Windows.RoutedEventHandler(this.FinishButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

