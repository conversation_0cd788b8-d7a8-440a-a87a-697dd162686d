﻿#pragma checksum "..\..\..\..\Views\SmartAnalysisPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C928A2417EFB58F3DD60B5566B1026B76F972668"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PCOptimizerApp.Views {
    
    
    /// <summary>
    /// SmartAnalysisPage
    /// </summary>
    public partial class SmartAnalysisPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 63 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AnalysisProgressSection;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar AnalysisProgressBar;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnalysisStepIcon;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnalysisStepName;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnalysisStepDescription;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border HardwareDetectionSection;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl HardwareDetectionList;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UsagePatternsSection;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl UsagePatternsList;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border RecommendationsSection;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyRecommendationsButton;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl RecommendationsList;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AnalysisSummarySection;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HardwareDetectionsCount;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UsagePatternsCount;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecommendationsCount;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\SmartAnalysisPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AnalysisDuration;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PCOptimizerApp;V1.0.0.0;component/views/smartanalysispage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SmartAnalysisPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 9 "..\..\..\..\Views\SmartAnalysisPage.xaml"
            ((PCOptimizerApp.Views.SmartAnalysisPage)(target)).Unloaded += new System.Windows.RoutedEventHandler(this.Page_Unloaded);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StartAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\Views\SmartAnalysisPage.xaml"
            this.StartAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.StartAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AnalysisProgressSection = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.AnalysisProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 5:
            this.AnalysisStepIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.AnalysisStepName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AnalysisStepDescription = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.HardwareDetectionSection = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.HardwareDetectionList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 10:
            this.UsagePatternsSection = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.UsagePatternsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 12:
            this.RecommendationsSection = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.ApplyRecommendationsButton = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\Views\SmartAnalysisPage.xaml"
            this.ApplyRecommendationsButton.Click += new System.Windows.RoutedEventHandler(this.ApplyRecommendationsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.RecommendationsList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 15:
            this.AnalysisSummarySection = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.HardwareDetectionsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.UsagePatternsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.RecommendationsCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.AnalysisDuration = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

