# Windows Performance Optimizer
# Applies common Windows optimizations to improve system responsiveness

param(
    [switch]$ApplyAll,
    [switch]$Interactive,
    [switch]$CreateRestorePoint,
    [switch]$VisualEffects,
    [switch]$PowerSettings,
    [switch]$Services,
    [switch]$StartupPrograms,
    [switch]$WindowsSearch,
    [switch]$Telemetry
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "This script requires Administrator privileges. Please run as Administrator."
    exit 1
}

Write-Host "⚡ Windows Performance Optimizer" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

# Create System Restore Point
if ($CreateRestorePoint -or $ApplyAll) {
    Write-Host "📌 Creating System Restore Point..." -ForegroundColor Yellow
    try {
        Checkpoint-Computer -Description "Performance Optimization - $(Get-Date)" -RestorePointType "MODIFY_SETTINGS"
        Write-Host "✅ System Restore Point created successfully" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  Could not create restore point: $($_.Exception.Message)" -ForegroundColor Yellow
    }
    Write-Host ""
}

function Confirm-Action {
    param([string]$Message)
    if ($Interactive) {
        $response = Read-Host "$Message (y/n)"
        return $response -eq 'y' -or $response -eq 'Y'
    }
    return $true
}

# Optimize Visual Effects for Performance
if ($VisualEffects -or $ApplyAll) {
    if (Confirm-Action "🎨 Optimize Visual Effects for Performance?") {
        Write-Host "🎨 Optimizing Visual Effects..." -ForegroundColor Green
        
        try {
            # Set to "Adjust for best performance"
            Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2 -Force
            
            # Disable specific visual effects
            $visualEffectsPath = "HKCU:\Control Panel\Desktop"
            Set-ItemProperty -Path $visualEffectsPath -Name "DragFullWindows" -Value "0" -Force
            Set-ItemProperty -Path $visualEffectsPath -Name "MenuShowDelay" -Value "0" -Force
            Set-ItemProperty -Path $visualEffectsPath -Name "UserPreferencesMask" -Value ([byte[]](0x90,0x12,0x03,0x80,0x10,0x00,0x00,0x00)) -Force
            
            # Disable window animations
            Set-ItemProperty -Path "HKCU:\Control Panel\Desktop\WindowMetrics" -Name "MinAnimate" -Value "0" -Force
            
            Write-Host "✅ Visual effects optimized for performance" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error optimizing visual effects: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Optimize Power Settings
if ($PowerSettings -or $ApplyAll) {
    if (Confirm-Action "⚡ Set High Performance Power Plan?") {
        Write-Host "⚡ Configuring Power Settings..." -ForegroundColor Green
        
        try {
            # Set to High Performance power plan
            $highPerfGuid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
            powercfg.exe /setactive $highPerfGuid
            
            # Disable USB selective suspend
            powercfg.exe /change usb-selective-suspend-setting Disabled
            
            # Set processor power management to 100%
            powercfg.exe /change processor-throttle-policy None
            
            Write-Host "✅ Power settings optimized" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error configuring power settings: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Optimize Windows Services
if ($Services -or $ApplyAll) {
    if (Confirm-Action "⚙️  Disable unnecessary Windows Services?") {
        Write-Host "⚙️  Optimizing Windows Services..." -ForegroundColor Green
        
        # List of services that can be safely disabled for performance
        $servicesToDisable = @(
            "Fax",
            "XblAuthManager",
            "XblGameSave",
            "XboxNetApiSvc",
            "XboxGipSvc",
            "WSearch",
            "TabletInputService",
            "WMPNetworkSvc",
            "RetailDemo",
            "RemoteRegistry",
            "WerSvc"
        )
        
        foreach ($service in $servicesToDisable) {
            try {
                $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
                if ($svc -and $svc.StartType -ne "Disabled") {
                    Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
                    Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
                    Write-Host "  ✓ Disabled: $service" -ForegroundColor Gray
                }
            } catch {
                Write-Host "  ⚠️  Could not disable: $service" -ForegroundColor Yellow
            }
        }
        
        Write-Host "✅ Windows services optimized" -ForegroundColor Green
    }
}

# Disable Windows Search Indexing
if ($WindowsSearch -or $ApplyAll) {
    if (Confirm-Action "🔍 Disable Windows Search Indexing (may affect search speed)?") {
        Write-Host "🔍 Configuring Windows Search..." -ForegroundColor Green
        
        try {
            # Stop and disable Windows Search service
            Stop-Service -Name "WSearch" -Force -ErrorAction SilentlyContinue
            Set-Service -Name "WSearch" -StartupType Disabled -ErrorAction SilentlyContinue
            
            # Disable indexing on all drives
            $drives = Get-WmiObject -Class Win32_Volume | Where-Object { $_.DriveLetter -and $_.DriveType -eq 3 }
            foreach ($drive in $drives) {
                $driveLetter = $drive.DriveLetter
                if ($driveLetter) {
                    fsutil behavior set DisableLastAccess 1
                    Write-Host "  ✓ Disabled indexing on drive $driveLetter" -ForegroundColor Gray
                }
            }
            
            Write-Host "✅ Windows Search indexing disabled" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error configuring Windows Search: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Disable Telemetry and Data Collection
if ($Telemetry -or $ApplyAll) {
    if (Confirm-Action "📊 Disable Windows Telemetry and Data Collection?") {
        Write-Host "📊 Disabling Telemetry..." -ForegroundColor Green
        
        try {
            # Disable telemetry
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\DataCollection" -Name "AllowTelemetry" -Value 0 -Force
            
            # Disable advertising ID
            if (!(Test-Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo")) {
                New-Item -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo" -Force
            }
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\AdvertisingInfo" -Name "DisabledByGroupPolicy" -Value 1 -Force
            
            # Disable location tracking
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Sensor\Overrides\{BFA794E4-F964-4FDB-90F6-51056BFE4B44}" -Name "SensorPermissionState" -Value 0 -Force
            
            # Disable feedback requests
            if (!(Test-Path "HKCU:\SOFTWARE\Microsoft\Siuf\Rules")) {
                New-Item -Path "HKCU:\SOFTWARE\Microsoft\Siuf\Rules" -Force
            }
            Set-ItemProperty -Path "HKCU:\SOFTWARE\Microsoft\Siuf\Rules" -Name "NumberOfSIUFInPeriod" -Value 0 -Force
            
            Write-Host "✅ Telemetry and data collection disabled" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error disabling telemetry: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Optimize Virtual Memory
if ($ApplyAll) {
    if (Confirm-Action "💾 Optimize Virtual Memory Settings?") {
        Write-Host "💾 Optimizing Virtual Memory..." -ForegroundColor Green
        
        try {
            # Get total RAM
            $totalRAM = (Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB
            
            # Set custom page file size (1.5x RAM for initial, 3x RAM for maximum)
            $initialSize = [math]::Round($totalRAM * 1.5 * 1024)  # Convert to MB
            $maximumSize = [math]::Round($totalRAM * 3 * 1024)    # Convert to MB
            
            # Configure page file
            $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges
            $cs.AutomaticManagedPagefile = $false
            $cs.Put()
            
            # Set page file on C: drive
            $pageFile = Get-WmiObject -Class Win32_PageFileSetting
            if ($pageFile) {
                $pageFile.Delete()
            }
            
            Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{name="C:\pagefile.sys"; InitialSize=$initialSize; MaximumSize=$maximumSize}
            
            Write-Host "✅ Virtual memory optimized (Initial: $initialSize MB, Maximum: $maximumSize MB)" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error optimizing virtual memory: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Clean System Temporary Files
if ($ApplyAll) {
    if (Confirm-Action "🧹 Clean System Temporary Files?") {
        Write-Host "🧹 Cleaning Temporary Files..." -ForegroundColor Green
        
        try {
            # Clean temp folders
            $tempFolders = @(
                "$env:TEMP",
                "$env:WINDIR\Temp",
                "$env:LOCALAPPDATA\Temp"
            )
            
            foreach ($folder in $tempFolders) {
                if (Test-Path $folder) {
                    Get-ChildItem -Path $folder -Recurse -Force -ErrorAction SilentlyContinue | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue
                    Write-Host "  ✓ Cleaned: $folder" -ForegroundColor Gray
                }
            }
            
            # Clean Windows Update cache
            Stop-Service -Name "wuauserv" -Force -ErrorAction SilentlyContinue
            Remove-Item -Path "$env:WINDIR\SoftwareDistribution\Download\*" -Recurse -Force -ErrorAction SilentlyContinue
            Start-Service -Name "wuauserv" -ErrorAction SilentlyContinue
            
            Write-Host "✅ Temporary files cleaned" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error cleaning temporary files: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Registry Optimizations
if ($ApplyAll) {
    if (Confirm-Action "🔧 Apply Registry Optimizations?") {
        Write-Host "🔧 Applying Registry Optimizations..." -ForegroundColor Green
        
        try {
            # Reduce menu delay
            Set-ItemProperty -Path "HKCU:\Control Panel\Desktop" -Name "MenuShowDelay" -Value 0 -Force
            
            # Improve file system performance
            Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "LargeCacheSize" -Value 1 -Force
            
            # Disable Last Access timestamp
            fsutil behavior set DisableLastAccess 1
            
            # Optimize network performance
            Set-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Multimedia\SystemProfile" -Name "NetworkThrottlingIndex" -Value 0xffffffff -Force
            
            Write-Host "✅ Registry optimizations applied" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error applying registry optimizations: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n🏁 Performance Optimization Complete!" -ForegroundColor Cyan
Write-Host "`n💡 Recommendations:" -ForegroundColor Yellow
Write-Host "• Restart your computer to apply all changes" -ForegroundColor Gray
Write-Host "• Run System-Diagnostics.ps1 to verify improvements" -ForegroundColor Gray
Write-Host "• Create regular system restore points" -ForegroundColor Gray
Write-Host "• Keep your system updated and run regular maintenance" -ForegroundColor Gray

if ($Interactive) {
    $restart = Read-Host "`nWould you like to restart now to apply changes? (y/n)"
    if ($restart -eq 'y' -or $restart -eq 'Y') {
        Write-Host "Restarting in 10 seconds..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
        Restart-Computer -Force
    }
}
