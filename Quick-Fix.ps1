# Quick Performance Fix Script
# Applies immediate performance improvements

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "⚠️ Running without Administrator privileges - some optimizations may be limited" -ForegroundColor Yellow
    Write-Host "For best results, restart PowerShell as Administrator" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "⚡ Quick PC Performance Fix" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan
Write-Host ""

# 1. Clear temporary files
Write-Host "🧹 Cleaning temporary files..." -ForegroundColor Green
try {
    $tempFolders = @("$env:TEMP", "$env:WINDIR\Temp")
    $totalCleaned = 0
    
    foreach ($folder in $tempFolders) {
        if (Test-Path $folder) {
            $filesBefore = (Get-ChildItem -Path $folder -Recurse -Force -ErrorAction SilentlyContinue | Measure-Object).Count
            Remove-Item -Path "$folder\*" -Recurse -Force -ErrorAction SilentlyContinue
            $filesAfter = (Get-ChildItem -Path $folder -Recurse -Force -ErrorAction SilentlyContinue | Measure-Object).Count
            $cleaned = $filesBefore - $filesAfter
            $totalCleaned += $cleaned
            Write-Host "  ✓ Cleaned $cleaned files from $folder" -ForegroundColor Gray
        }
    }
    Write-Host "✅ Temporary files cleaned: $totalCleaned files removed" -ForegroundColor Green
} catch {
    Write-Host "❌ Error cleaning temporary files" -ForegroundColor Red
}

# 2. Clear DNS cache
Write-Host "`n🌐 Clearing DNS cache..." -ForegroundColor Green
try {
    ipconfig /flushdns | Out-Null
    Write-Host "✅ DNS cache cleared" -ForegroundColor Green
} catch {
    Write-Host "❌ Error clearing DNS cache" -ForegroundColor Red
}

# 3. Disable visual effects for performance
Write-Host "`n🎨 Optimizing visual effects..." -ForegroundColor Green
try {
    if (Test-Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects") {
        Set-ItemProperty -Path "HKCU:\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects" -Name "VisualFXSetting" -Value 2 -Force
        Write-Host "✅ Visual effects optimized for performance" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error optimizing visual effects" -ForegroundColor Red
}

# 4. Set high performance power plan
Write-Host "`n⚡ Setting high performance power plan..." -ForegroundColor Green
try {
    $highPerfGuid = "8c5e7fda-e8bf-4a96-9a85-a6e23a8c635c"
    powercfg.exe /setactive $highPerfGuid 2>$null
    Write-Host "✅ High performance power plan activated" -ForegroundColor Green
} catch {
    Write-Host "❌ Error setting power plan" -ForegroundColor Red
}

# 5. Disable unnecessary startup programs (safe ones)
Write-Host "`n🚀 Optimizing startup programs..." -ForegroundColor Green
try {
    $startupPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    if (Test-Path $startupPath) {
        $startupItems = Get-ItemProperty -Path $startupPath -ErrorAction SilentlyContinue
        
        # Safe to disable startup items (common bloatware)
        $safeToDisable = @("Spotify", "Skype", "Steam", "Discord", "Adobe", "iTunes")
        
        foreach ($item in $safeToDisable) {
            if ($startupItems.PSObject.Properties.Name -like "*$item*") {
                $propertyName = $startupItems.PSObject.Properties.Name | Where-Object { $_ -like "*$item*" } | Select-Object -First 1
                if ($propertyName) {
                    Remove-ItemProperty -Path $startupPath -Name $propertyName -ErrorAction SilentlyContinue
                    Write-Host "  ✓ Disabled startup item: $propertyName" -ForegroundColor Gray
                }
            }
        }
        Write-Host "✅ Startup programs optimized" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Error optimizing startup programs" -ForegroundColor Red
}

# 6. Disable telemetry
Write-Host "`n📊 Disabling telemetry..." -ForegroundColor Green
try {
    if (Test-Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\DataCollection") {
        Set-ItemProperty -Path "HKLM:\SOFTWARE\Policies\Microsoft\Windows\DataCollection" -Name "AllowTelemetry" -Value 0 -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Telemetry disabled" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ Could not disable telemetry (requires Administrator)" -ForegroundColor Yellow
}

# 7. Memory cleanup
Write-Host "`n💾 Optimizing memory..." -ForegroundColor Green
try {
    [GC]::Collect()
    [GC]::WaitForPendingFinalizers()
    [GC]::Collect()
    Write-Host "✅ Memory garbage collection completed" -ForegroundColor Green
} catch {
    Write-Host "❌ Error during memory optimization" -ForegroundColor Red
}

# 8. Show current system status
Write-Host "`n📊 Current System Status:" -ForegroundColor Cyan
try {
    $os = Get-WmiObject -Class Win32_OperatingSystem
    $computer = Get-WmiObject -Class Win32_ComputerSystem
    
    # Fix: Both values need to be in the same units
    $totalRAM = [math]::Round($os.TotalVisibleMemorySize / 1024 / 1024, 2)  # Convert KB to GB
    $freeRAM = [math]::Round($os.FreePhysicalMemory / 1024 / 1024, 2)       # Convert KB to GB
    $usedRAM = $totalRAM - $freeRAM
    $memoryUsage = [math]::Round(($usedRAM / $totalRAM) * 100, 2)
    
    Write-Host "Total RAM: $totalRAM GB"
    Write-Host "Used RAM: $usedRAM GB ($memoryUsage%)"
    Write-Host "Free RAM: $freeRAM GB"
    
    if ($memoryUsage -lt 70) {
        Write-Host "✅ Memory usage is good" -ForegroundColor Green
    } elseif ($memoryUsage -lt 85) {
        Write-Host "⚠️ Memory usage is moderate" -ForegroundColor Yellow
    } else {
        Write-Host "🔴 Memory usage is high" -ForegroundColor Red
    }
    
    # Check disk space
    $disk = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "C:" }
    if ($disk) {
        $totalSpace = [math]::Round($disk.Size / 1GB, 2)
        $freeSpace = [math]::Round($disk.FreeSpace / 1GB, 2)
        $usedSpace = $totalSpace - $freeSpace
        $diskUsage = [math]::Round(($usedSpace / $totalSpace) * 100, 2)
        
        Write-Host "`nDisk C: Usage:"
        Write-Host "Total: $totalSpace GB, Used: $usedSpace GB ($diskUsage%), Free: $freeSpace GB"
        
        if ($diskUsage -lt 80) {
            Write-Host "✅ Disk space is good" -ForegroundColor Green
        } elseif ($diskUsage -lt 90) {
            Write-Host "⚠️ Disk space is getting low" -ForegroundColor Yellow
        } else {
            Write-Host "🔴 Disk space is critically low" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "❌ Error retrieving system status" -ForegroundColor Red
}

Write-Host "`n🏁 Quick Performance Fix Complete!" -ForegroundColor Cyan
Write-Host "`n💡 Recommendations:" -ForegroundColor Yellow
Write-Host "• Restart your computer to apply all changes"
Write-Host "• Run Windows Update to ensure you have the latest updates"
Write-Host "• Consider running the full optimization suite for more improvements"
Write-Host "• Monitor your system performance over the next few days"

Write-Host "`nFor comprehensive optimization, run the full PC-Optimizer.ps1 script." -ForegroundColor Gray
