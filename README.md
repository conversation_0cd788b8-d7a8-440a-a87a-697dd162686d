# Windows Performance Optimization Toolkit

A comprehensive PowerShell-based toolkit to diagnose and optimize Windows system performance.

## Features

- **System Diagnostics** - Analyze current system performance and identify bottlenecks
- **Performance Optimization** - Apply proven Windows optimizations automatically
- **Startup Management** - Control which programs start with Windows
- **Disk Cleanup** - Remove temporary files and free up disk space
- **Memory Optimization** - Optimize RAM usage and clear memory caches
- **Registry Optimization** - Safe registry tweaks for better performance

## Quick Start

1. **Run as Administrator** - Most scripts require elevated privileges
2. **System Check** - Start with `System-Diagnostics.ps1` to assess current state
3. **Apply Optimizations** - Run `Performance-Optimizer.ps1` for automatic improvements
4. **Custom Cleanup** - Use individual scripts as needed

## Scripts Overview

### 📊 System-Diagnostics.ps1
Comprehensive system analysis including:
- CPU and memory usage
- Disk space and fragmentation
- Running processes and services
- Startup programs impact
- Network performance
- System temperature (if available)

### ⚡ Performance-Optimizer.ps1
Automated performance improvements:
- Disable unnecessary visual effects
- Optimize power settings
- Configure virtual memory
- Disable background apps
- Optimize Windows Search
- Configure Windows Updates

### 🧹 Disk-Cleanup.ps1
Advanced cleanup operations:
- Temporary files removal
- Browser cache cleanup
- Windows Update cache
- System file cleanup
- Empty Recycle Bin
- Clear thumbnail cache

### 🚀 Startup-Manager.ps1
Startup optimization:
- List all startup programs
- Disable unnecessary startup items
- Prioritize essential applications
- Create startup backup/restore

### 💾 Memory-Optimizer.ps1
RAM optimization:
- Clear system memory cache
- Optimize virtual memory settings
- Close memory-intensive processes
- Configure page file settings

### 🔧 Advanced-Hardware-Optimizer.ps1
Hardware-aware sophisticated optimizations:
- SSD vs HDD specific optimizations
- CPU manufacturer specific tweaks (Intel/AMD)
- RAM size-based memory management
- GPU specific driver optimizations
- Network adapter optimizations

### 💿 SSD-Optimizer.ps1
SSD-specific advanced optimizations:
- Disable HDD-designed features (defrag, superfetch)
- Enable SSD-specific features (TRIM)
- Optimize write operations and wear leveling
- Configure optimal page file for flash storage

### 🔍 Hardware-Detection.ps1
Analyze hardware and suggest optimizations:
- Detect storage types (SSD vs HDD)
- Identify CPU architecture and features
- Analyze RAM configuration
- Suggest hardware-specific optimizations

## Usage Examples

```powershell
# Run system diagnostics
.\System-Diagnostics.ps1

# Apply all optimizations
.\Performance-Optimizer.ps1 -ApplyAll

# Clean disk with confirmation
.\Disk-Cleanup.ps1 -Interactive

# Manage startup programs
.\Startup-Manager.ps1 -ShowAll

# Advanced hardware-aware optimizations
.\Advanced-Hardware-Optimizer.ps1 -ApplyAll

# SSD-specific optimizations only
.\SSD-Optimizer.ps1

# Detect hardware and show optimization suggestions
.\Hardware-Detection.ps1
```

## Advanced Hardware-Aware Features

### 🎯 **SSD vs HDD Optimizations**
The toolkit automatically detects your storage type and applies appropriate optimizations:

**For SSD systems:**
- Disables defragmentation (prevents wear)
- Enables TRIM support
- Optimizes prefetch for flash storage
- Disables hibernation to save space
- Configures smaller page file

**For HDD systems:**
- Enables scheduled defragmentation
- Optimizes SuperFetch for mechanical drives
- Configures larger cache settings

### 🖥️ **CPU-Specific Optimizations**
**Intel CPUs:**
- Enables Turbo Boost optimization
- Optimizes SpeedStep settings

**AMD CPUs:**
- Enables Cool'n'Quiet optimization
- Optimizes performance scaling

**Multi-core systems:**
- Optimizes thread scheduling
- Enables all cores for boot

### 💾 **RAM-Based Optimizations**
**High RAM (16GB+):**
- Disables paging executive
- Increases system cache
- Reduces page file size

**Low RAM (<8GB):**
- Optimizes memory management
- Increases page file size
- Disables memory-intensive features

## Safety Features

- **Backup Creation** - All changes create restore points
- **Confirmation Prompts** - Interactive mode for safety
- **Logging** - Detailed logs of all operations
- **Rollback Options** - Undo changes if needed

## Requirements

- Windows 10/11
- PowerShell 5.1 or higher
- Administrator privileges (for most operations)

## Warning

Always create a system restore point before running optimization scripts. While these scripts are designed to be safe, system modifications can have unintended consequences.

## License

This toolkit is provided as-is for educational and optimization purposes.
