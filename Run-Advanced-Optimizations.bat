@echo off
echo Advanced Hardware-Aware PC Optimization
echo =======================================
echo.
echo This will apply sophisticated optimizations based on your hardware:
echo.
echo SSD Optimizations:
echo - Disable defragmentation (reduces wear)
echo - Enable TRIM support
echo - Optimize prefetch for flash storage
echo - Disable hibernation
echo - Configure optimal page file
echo.
echo CPU/RAM/GPU Optimizations:
echo - Hardware-specific driver tweaks
echo - Memory management based on RAM size
echo - Processor-specific optimizations
echo.
echo IMPORTANT: Run as Administrator for full functionality
echo.
pause

echo Running SSD-specific optimizations...
powershell.exe -ExecutionPolicy Bypass -File "SSD-Optimizer.ps1"

echo.
echo Running advanced hardware-aware optimizations...
powershell.exe -ExecutionPolicy Bypass -File "Advanced-Hardware-Optimizer.ps1" -ApplyAll -CreateBackup

echo.
echo Advanced optimization complete!
echo Your PC is now optimized for your specific hardware configuration.
pause
