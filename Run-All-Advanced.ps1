# Run All Advanced Optimizations
# Executes all the new hardware-aware optimization scripts

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "🔒 Administrator privileges required!" -ForegroundColor Red
    Write-Host "Restarting as Administrator..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`""
    exit
}

Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

Write-Host "🚀 Running All Advanced Hardware-Aware Optimizations" -ForegroundColor Cyan
Write-Host "====================================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Hardware Detection
Write-Host "Step 1: Hardware Detection and Analysis" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
& ".\Hardware-Detection.ps1"

Write-Host "`nPress any key to continue to SSD optimizations..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 2: SSD Optimizations
Write-Host "`nStep 2: SSD-Specific Optimizations" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
& ".\SSD-Optimizer.ps1"

Write-Host "`nPress any key to continue to advanced hardware optimizations..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Step 3: Advanced Hardware Optimizations
Write-Host "`nStep 3: Advanced Hardware-Aware Optimizations" -ForegroundColor Green
Write-Host "==============================================" -ForegroundColor Green
& ".\Advanced-Hardware-Optimizer.ps1" -ApplyAll -CreateBackup

Write-Host "`n🎉 All Advanced Optimizations Complete!" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Your PC has been optimized based on your specific hardware configuration:" -ForegroundColor Green
Write-Host "• SSD-specific optimizations applied" -ForegroundColor Gray
Write-Host "• CPU-specific tweaks based on manufacturer" -ForegroundColor Gray
Write-Host "• RAM optimizations based on system memory" -ForegroundColor Gray
Write-Host "• GPU-specific driver optimizations" -ForegroundColor Gray
Write-Host "• Advanced system configuration changes" -ForegroundColor Gray
Write-Host ""
Write-Host "🔄 Restart your computer to apply all changes." -ForegroundColor Yellow
Write-Host ""
Read-Host "Press Enter to exit"
