# SSD-Specific Optimization Script
# Sophisticated optimizations for systems with SSDs

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "Administrator privileges required. Please run as Administrator."
    exit 1
}

Write-Host "💿 SSD-Specific Advanced Optimizations" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Detect SSDs
Write-Host "`nDetecting storage devices..." -ForegroundColor Yellow
try {
    $physicalDisks = Get-PhysicalDisk
    $ssds = $physicalDisks | Where-Object { $_.MediaType -eq "SSD" -or $_.BusType -eq "NVMe" }
    
    if ($ssds.Count -eq 0) {
        # Fallback detection method
        $diskDrives = Get-WmiObject -Class Win32_DiskDrive
        $ssdDrives = $diskDrives | Where-Object { $_.Model -like "*SSD*" -or $_.Model -like "*NVMe*" }
        
        if ($ssdDrives.Count -eq 0) {
            Write-Host "❌ No SSDs detected. These optimizations are only for SSD systems." -ForegroundColor Red
            exit 1
        } else {
            Write-Host "✅ Detected $($ssdDrives.Count) SSD(s) via model name" -ForegroundColor Green
        }
    } else {
        Write-Host "✅ Detected $($ssds.Count) SSD(s)" -ForegroundColor Green
        foreach ($ssd in $ssds) {
            Write-Host "  📀 $($ssd.FriendlyName) - $([math]::Round($ssd.Size / 1GB, 0)) GB ($($ssd.MediaType))" -ForegroundColor Gray
        }
    }
} catch {
    Write-Host "⚠️  Using fallback SSD detection..." -ForegroundColor Yellow
}

Write-Host "`n🔧 Applying SSD-Specific Optimizations..." -ForegroundColor Green

# 1. Disable Defragmentation for SSDs
Write-Host "`n1. Disabling defragmentation for SSDs..." -ForegroundColor Yellow
try {
    # Disable scheduled defrag
    schtasks /Change /TN "Microsoft\Windows\Defrag\ScheduledDefrag" /Disable 2>$null
    Write-Host "   ✅ Scheduled defragmentation disabled" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not disable defragmentation" -ForegroundColor Yellow
}

# 2. Enable TRIM support
Write-Host "`n2. Enabling TRIM support..." -ForegroundColor Yellow
try {
    fsutil behavior set DisableDeleteNotify 0
    Write-Host "   ✅ TRIM support enabled" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not enable TRIM" -ForegroundColor Yellow
}

# 3. Disable Last Access Time updates
Write-Host "`n3. Disabling Last Access Time updates..." -ForegroundColor Yellow
try {
    fsutil behavior set DisableLastAccess 1
    Write-Host "   ✅ Last Access Time updates disabled (reduces SSD writes)" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not disable Last Access Time" -ForegroundColor Yellow
}

# 4. Optimize Prefetch for SSD
Write-Host "`n4. Optimizing Prefetch for SSD..." -ForegroundColor Yellow
try {
    # Reduce prefetch for SSDs (they don't need aggressive prefetching)
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" -Name "EnablePrefetcher" -Value 1 -Force
    # Disable SuperFetch (now called SysMain) for SSDs
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\Session Manager\Memory Management\PrefetchParameters" -Name "EnableSuperfetch" -Value 0 -Force
    Write-Host "   ✅ Prefetch optimized for SSD" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not optimize prefetch settings" -ForegroundColor Yellow
}

# 5. Disable Hibernation
Write-Host "`n5. Disabling hibernation..." -ForegroundColor Yellow
try {
    powercfg.exe /hibernate off
    Write-Host "   ✅ Hibernation disabled (saves SSD space and write cycles)" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not disable hibernation" -ForegroundColor Yellow
}

# 6. Optimize Windows Search for SSD
Write-Host "`n6. Optimizing Windows Search for SSD..." -ForegroundColor Yellow
try {
    # Disable indexing for better SSD performance
    Stop-Service -Name "WSearch" -Force -ErrorAction SilentlyContinue
    Set-Service -Name "WSearch" -StartupType Disabled
    Write-Host "   ✅ Windows Search indexing disabled" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not optimize Windows Search" -ForegroundColor Yellow
}

# 7. Optimize Page File for SSD
Write-Host "`n7. Optimizing page file for SSD..." -ForegroundColor Yellow
try {
    # Get RAM size
    $ramGB = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 0)
    
    # For SSDs, use smaller page file to reduce wear
    $initialSizeMB = [math]::Min([math]::Round($ramGB * 0.5 * 1024), 4096)  # Max 4GB initial
    $maximumSizeMB = [math]::Min([math]::Round($ramGB * 1.5 * 1024), 8192)  # Max 8GB maximum
    
    # Disable automatic page file management
    $cs = Get-WmiObject -Class Win32_ComputerSystem -EnableAllPrivileges
    $cs.AutomaticManagedPagefile = $false
    $cs.Put() | Out-Null
    
    # Remove existing page files
    Get-WmiObject -Class Win32_PageFileSetting | ForEach-Object { $_.Delete() }
    
    # Create optimized page file for SSD
    Set-WmiInstance -Class Win32_PageFileSetting -Arguments @{
        name = "C:\pagefile.sys"
        InitialSize = $initialSizeMB
        MaximumSize = $maximumSizeMB
    } | Out-Null
    
    Write-Host "   ✅ Page file optimized for SSD ($initialSizeMB MB - $maximumSizeMB MB)" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not optimize page file" -ForegroundColor Yellow
}

# 8. Disable System Restore (optional - saves SSD space)
Write-Host "`n8. System Restore optimization..." -ForegroundColor Yellow
$response = Read-Host "   Disable System Restore to save SSD space? (y/n) [Recommended: n]"
if ($response -eq 'y' -or $response -eq 'Y') {
    try {
        Disable-ComputerRestore -Drive "C:\"
        Write-Host "   ✅ System Restore disabled" -ForegroundColor Green
    } catch {
        Write-Host "   ⚠️  Could not disable System Restore" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ℹ️  System Restore kept enabled (recommended for safety)" -ForegroundColor Blue
}

# 9. Optimize NTFS for SSD
Write-Host "`n9. Optimizing NTFS for SSD..." -ForegroundColor Yellow
try {
    # Disable 8.3 filename creation (reduces writes)
    fsutil behavior set Disable8dot3 1
    
    # Optimize NTFS memory usage
    Set-ItemProperty -Path "HKLM:\SYSTEM\CurrentControlSet\Control\FileSystem" -Name "NtfsMemoryUsage" -Value 2 -Force
    
    Write-Host "   ✅ NTFS optimized for SSD" -ForegroundColor Green
} catch {
    Write-Host "   ⚠️  Could not optimize NTFS" -ForegroundColor Yellow
}

# 10. Disable Windows Write Cache Buffer Flushing (for SSDs with capacitors)
Write-Host "`n10. Optimizing write cache for SSD..." -ForegroundColor Yellow
try {
    # This can improve performance on SSDs with power loss protection
    $response = Read-Host "    Disable write cache buffer flushing? (Only safe for SSDs with capacitors) (y/n) [Recommended: n]"
    if ($response -eq 'y' -or $response -eq 'Y') {
        $drives = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }
        foreach ($drive in $drives) {
            $driveLetter = $drive.DeviceID.Replace(":", "")
            fsutil behavior set DisableWriteAutoDetect 1
        }
        Write-Host "    ✅ Write cache buffer flushing disabled" -ForegroundColor Green
    } else {
        Write-Host "    ℹ️  Write cache buffer flushing kept enabled (safer)" -ForegroundColor Blue
    }
} catch {
    Write-Host "    ⚠️  Could not optimize write cache" -ForegroundColor Yellow
}

Write-Host "`n🎉 SSD Optimization Complete!" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

Write-Host "`n📊 Applied Optimizations:" -ForegroundColor Green
Write-Host "• Disabled defragmentation (prevents unnecessary wear)"
Write-Host "• Enabled TRIM support (maintains performance)"
Write-Host "• Disabled Last Access Time updates (reduces writes)"
Write-Host "• Optimized prefetch settings for SSD"
Write-Host "• Disabled hibernation (saves space and writes)"
Write-Host "• Optimized Windows Search indexing"
Write-Host "• Configured optimal page file size"
Write-Host "• Optimized NTFS settings for SSD"

Write-Host "`n💡 Benefits:" -ForegroundColor Yellow
Write-Host "• Reduced unnecessary write operations (extends SSD lifespan)"
Write-Host "• Improved performance through SSD-specific optimizations"
Write-Host "• Freed up SSD space by removing unnecessary features"
Write-Host "• Optimized caching for flash storage characteristics"

Write-Host "`n🔄 Restart your computer to apply all SSD optimizations." -ForegroundColor Cyan
