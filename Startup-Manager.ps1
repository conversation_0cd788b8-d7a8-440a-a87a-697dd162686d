# Startup Manager Script
# Manage Windows startup programs to improve boot time

param(
    [switch]$ShowAll,
    [switch]$DisableSelected,
    [switch]$Interactive,
    [switch]$CreateBackup,
    [string]$BackupPath = "$env:USERPROFILE\Desktop\StartupBackup.reg"
)

# Check for Administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Administrator privileges recommended for full startup management functionality."
}

Write-Host "🚀 Windows Startup Manager" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan
Write-Host ""

function Get-StartupPrograms {
    $startupPrograms = @()
    
    # Registry locations for startup programs
    $registryPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce",
        "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
        "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce"
    )
    
    foreach ($path in $registryPaths) {
        try {
            if (Test-Path $path) {
                $items = Get-ItemProperty -Path $path -ErrorAction SilentlyContinue
                if ($items) {
                    $items.PSObject.Properties | Where-Object { $_.Name -notlike "PS*" } | ForEach-Object {
                        $startupPrograms += [PSCustomObject]@{
                            Name = $_.Name
                            Command = $_.Value
                            Location = $path
                            Type = "Registry"
                            Impact = Get-StartupImpact $_.Name
                        }
                    }
                }
            }
        } catch {
            Write-Warning "Could not access registry path: $path"
        }
    }
    
    # Startup folder programs
    $startupFolders = @(
        "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Startup",
        "$env:ProgramData\Microsoft\Windows\Start Menu\Programs\Startup"
    )
    
    foreach ($folder in $startupFolders) {
        if (Test-Path $folder) {
            Get-ChildItem -Path $folder -ErrorAction SilentlyContinue | ForEach-Object {
                $startupPrograms += [PSCustomObject]@{
                    Name = $_.Name
                    Command = $_.FullName
                    Location = $folder
                    Type = "Folder"
                    Impact = Get-StartupImpact $_.Name
                }
            }
        }
    }
    
    # Task Scheduler startup programs
    try {
        $scheduledTasks = Get-ScheduledTask | Where-Object { 
            $_.State -eq "Ready" -and 
            $_.Principal.UserId -ne "SYSTEM" -and
            ($_.Triggers | Where-Object { $_.CimClass.CimClassName -eq "MSFT_TaskBootTrigger" -or $_.CimClass.CimClassName -eq "MSFT_TaskLogonTrigger" })
        }
        
        foreach ($task in $scheduledTasks) {
            $startupPrograms += [PSCustomObject]@{
                Name = $task.TaskName
                Command = $task.Actions[0].Execute
                Location = "Task Scheduler"
                Type = "Scheduled Task"
                Impact = Get-StartupImpact $task.TaskName
            }
        }
    } catch {
        Write-Warning "Could not retrieve scheduled tasks"
    }
    
    return $startupPrograms
}

function Get-StartupImpact {
    param([string]$ProgramName)
    
    # Known high-impact programs
    $highImpact = @(
        "Adobe", "Creative", "Photoshop", "Acrobat", "Steam", "Origin", "Skype", "Spotify", "iTunes", "QuickTime"
    )
    
    # Known low-impact programs
    $lowImpact = @(
        "Windows Security", "Audio", "Driver", "Intel", "NVIDIA", "AMD", "Realtek", "Antivirus"
    )
    
    # Essential programs (should not be disabled)
    $essential = @(
        "Windows Security", "ctfmon", "explorer", "winlogon", "Security Health", "Windows Defender"
    )
    
    $nameLower = $ProgramName.ToLower()
    
    if ($essential | Where-Object { $nameLower -like "*$($_.ToLower())*" }) {
        return "Essential"
    } elseif ($highImpact | Where-Object { $nameLower -like "*$($_.ToLower())*" }) {
        return "High"
    } elseif ($lowImpact | Where-Object { $nameLower -like "*$($_.ToLower())*" }) {
        return "Low"
    } else {
        return "Medium"
    }
}

function Show-StartupPrograms {
    param([array]$Programs)
    
    Write-Host "📋 Current Startup Programs:" -ForegroundColor Green
    Write-Host "=============================" -ForegroundColor Green
    
    $Programs | Sort-Object Impact, Name | Format-Table -Property @(
        @{Name="ID"; Expression={$Programs.IndexOf($_) + 1}; Width=3},
        @{Name="Name"; Expression={$_.Name}; Width=30},
        @{Name="Impact"; Expression={$_.Impact}; Width=8},
        @{Name="Type"; Expression={$_.Type}; Width=15},
        @{Name="Location"; Expression={
            if ($_.Location.Length -gt 40) {
                $_.Location.Substring(0, 37) + "..."
            } else {
                $_.Location
            }
        }; Width=40}
    ) -AutoSize
    
    # Summary
    $totalPrograms = $Programs.Count
    $highImpact = ($Programs | Where-Object { $_.Impact -eq "High" }).Count
    $essential = ($Programs | Where-Object { $_.Impact -eq "Essential" }).Count
    
    Write-Host "`n📊 Summary:" -ForegroundColor Yellow
    Write-Host "Total Startup Programs: $totalPrograms"
    Write-Host "High Impact Programs: $highImpact"
    Write-Host "Essential Programs: $essential"
    
    if ($highImpact -gt 5) {
        Write-Host "⚠️  Consider disabling some high-impact programs to improve boot time" -ForegroundColor Yellow
    } elseif ($totalPrograms -gt 20) {
        Write-Host "⚠️  Many startup programs detected. Consider reducing for better performance" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Startup program count looks reasonable" -ForegroundColor Green
    }
}

function Disable-StartupProgram {
    param(
        [PSCustomObject]$Program,
        [switch]$CreateBackupFirst
    )
    
    if ($Program.Impact -eq "Essential") {
        Write-Host "⚠️  Cannot disable essential program: $($Program.Name)" -ForegroundColor Red
        return $false
    }
    
    try {
        switch ($Program.Type) {
            "Registry" {
                if ($CreateBackupFirst) {
                    # Export registry key before deletion
                    $regPath = $Program.Location -replace "HKLM:", "HKEY_LOCAL_MACHINE" -replace "HKCU:", "HKEY_CURRENT_USER"
                    reg export $regPath "$env:TEMP\startup_backup_$($Program.Name).reg" /y | Out-Null
                }
                
                Remove-ItemProperty -Path $Program.Location -Name $Program.Name -Force
                Write-Host "✅ Disabled: $($Program.Name)" -ForegroundColor Green
                return $true
            }
            
            "Folder" {
                if ($CreateBackupFirst) {
                    # Move file to backup location instead of deleting
                    $backupFolder = "$env:TEMP\StartupBackup"
                    if (!(Test-Path $backupFolder)) {
                        New-Item -Path $backupFolder -ItemType Directory -Force | Out-Null
                    }
                    Move-Item -Path $Program.Command -Destination $backupFolder -Force
                } else {
                    Remove-Item -Path $Program.Command -Force
                }
                Write-Host "✅ Disabled: $($Program.Name)" -ForegroundColor Green
                return $true
            }
            
            "Scheduled Task" {
                Disable-ScheduledTask -TaskName $Program.Name -ErrorAction Stop
                Write-Host "✅ Disabled: $($Program.Name)" -ForegroundColor Green
                return $true
            }
        }
    } catch {
        Write-Host "❌ Failed to disable $($Program.Name): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $false
}

# Create backup of startup configuration
if ($CreateBackup) {
    Write-Host "💾 Creating Startup Configuration Backup..." -ForegroundColor Yellow
    
    try {
        $registryPaths = @(
            "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Run",
            "HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
        )
        
        foreach ($path in $registryPaths) {
            $fileName = "$env:TEMP\startup_backup_$(($path -split '\\')[-1]).reg"
            reg export $path $fileName /y | Out-Null
        }
        
        # Combine all backups into one file
        $combinedBackup = @()
        $combinedBackup += "Windows Registry Editor Version 5.00"
        $combinedBackup += ""
        
        Get-ChildItem -Path "$env:TEMP\startup_backup_*.reg" | ForEach-Object {
            $content = Get-Content $_.FullName | Select-Object -Skip 1
            $combinedBackup += $content
            $combinedBackup += ""
            Remove-Item $_.FullName -Force
        }
        
        $combinedBackup | Out-File -FilePath $BackupPath -Encoding UTF8
        Write-Host "✅ Backup created: $BackupPath" -ForegroundColor Green
        
    } catch {
        Write-Host "❌ Failed to create backup: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Get all startup programs
$startupPrograms = Get-StartupPrograms

if ($ShowAll -or !$DisableSelected) {
    Show-StartupPrograms $startupPrograms
}

# Interactive mode for disabling programs
if ($Interactive -or $DisableSelected) {
    Write-Host "`n🔧 Startup Program Management" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    
    if ($Interactive) {
        while ($true) {
            Write-Host "`nOptions:" -ForegroundColor Yellow
            Write-Host "1. Show all programs"
            Write-Host "2. Disable specific programs"
            Write-Host "3. Auto-disable high-impact programs"
            Write-Host "4. Exit"
            
            $choice = Read-Host "Select option (1-4)"
            
            switch ($choice) {
                "1" {
                    Show-StartupPrograms $startupPrograms
                }
                "2" {
                    Show-StartupPrograms $startupPrograms
                    $programIds = Read-Host "Enter program IDs to disable (comma-separated, e.g., 1,3,5)"
                    
                    if ($programIds) {
                        $ids = $programIds -split "," | ForEach-Object { $_.Trim() }
                        foreach ($id in $ids) {
                            if ($id -match '^\d+$' -and [int]$id -le $startupPrograms.Count -and [int]$id -gt 0) {
                                $program = $startupPrograms[[int]$id - 1]
                                $confirm = Read-Host "Disable '$($program.Name)'? (y/n)"
                                if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                                    Disable-StartupProgram $program -CreateBackupFirst
                                }
                            }
                        }
                        # Refresh the list
                        $startupPrograms = Get-StartupPrograms
                    }
                }
                "3" {
                    $highImpactPrograms = $startupPrograms | Where-Object { $_.Impact -eq "High" }
                    if ($highImpactPrograms.Count -gt 0) {
                        Write-Host "`nHigh-impact programs that can be safely disabled:" -ForegroundColor Yellow
                        $highImpactPrograms | ForEach-Object { Write-Host "• $($_.Name)" }
                        
                        $confirm = Read-Host "`nDisable all high-impact programs? (y/n)"
                        if ($confirm -eq 'y' -or $confirm -eq 'Y') {
                            foreach ($program in $highImpactPrograms) {
                                Disable-StartupProgram $program -CreateBackupFirst
                            }
                            $startupPrograms = Get-StartupPrograms
                        }
                    } else {
                        Write-Host "No high-impact programs found to disable" -ForegroundColor Green
                    }
                }
                "4" {
                    break
                }
                default {
                    Write-Host "Invalid option. Please select 1-4." -ForegroundColor Red
                }
            }
        }
    }
}

# Provide recommendations
Write-Host "`n💡 Startup Optimization Recommendations:" -ForegroundColor Yellow
Write-Host "=========================================" -ForegroundColor Yellow

$recommendations = @()

# Check for specific problematic programs
$problematicPrograms = $startupPrograms | Where-Object { 
    $_.Name -like "*Adobe*" -or 
    $_.Name -like "*Creative*" -or 
    $_.Name -like "*Steam*" -or 
    $_.Name -like "*Skype*" -or 
    $_.Name -like "*iTunes*" 
}

if ($problematicPrograms.Count -gt 0) {
    $recommendations += "• Consider disabling: $($problematicPrograms.Name -join ', ')"
}

if ($startupPrograms.Count -gt 15) {
    $recommendations += "• You have $($startupPrograms.Count) startup programs. Consider reducing to 10 or fewer."
}

$highImpactCount = ($startupPrograms | Where-Object { $_.Impact -eq "High" }).Count
if ($highImpactCount -gt 3) {
    $recommendations += "• You have $highImpactCount high-impact startup programs. Try to keep this under 3."
}

if ($recommendations.Count -eq 0) {
    $recommendations += "✅ Your startup configuration looks good!"
}

foreach ($rec in $recommendations) {
    Write-Host $rec -ForegroundColor Gray
}

Write-Host "`n📈 Performance Tips:" -ForegroundColor Green
Write-Host "• Disable programs you don't use immediately at startup"
Write-Host "• Keep antivirus and essential system programs enabled"
Write-Host "• You can always manually start programs when needed"
Write-Host "• Use Task Manager > Startup to see impact ratings"
Write-Host "• Restart your computer to see boot time improvements"

Write-Host "`n🏁 Startup Management Complete!" -ForegroundColor Cyan
