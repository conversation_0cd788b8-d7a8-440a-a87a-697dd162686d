# System Diagnostics Script
# Comprehensive Windows system performance analysis

param(
    [switch]$Detailed,
    [switch]$ExportReport,
    [string]$OutputPath = "$env:USERPROFILE\Desktop\SystemReport.txt"
)

# Ensure running as Administrator for accurate results
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Warning "Some diagnostic features require Administrator privileges. Consider running as Administrator for complete results."
}

Write-Host "🔍 Windows System Performance Diagnostics" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

$Report = @()

# System Information
Write-Host "📋 System Information" -ForegroundColor Green
$OS = Get-WmiObject -Class Win32_OperatingSystem
$Computer = Get-WmiObject -Class Win32_ComputerSystem
$Processor = Get-WmiObject -Class Win32_Processor

$SystemInfo = @"
Computer Name: $($Computer.Name)
Operating System: $($OS.Caption) $($OS.Version)
Architecture: $($OS.OSArchitecture)
Total RAM: $([math]::Round($Computer.TotalPhysicalMemory / 1GB, 2)) GB
Processor: $($Processor.Name)
Logical Processors: $($Processor.NumberOfLogicalProcessors)
"@

Write-Host $SystemInfo
$Report += "SYSTEM INFORMATION"
$Report += "==================="
$Report += $SystemInfo
$Report += ""

# CPU Usage
Write-Host "`n🖥️  CPU Performance" -ForegroundColor Green
$CPUUsage = Get-WmiObject -Class Win32_Processor | Measure-Object -Property LoadPercentage -Average
Write-Host "Average CPU Usage: $([math]::Round($CPUUsage.Average, 2))%"

if ($CPUUsage.Average -gt 80) {
    Write-Host "⚠️  HIGH CPU USAGE DETECTED!" -ForegroundColor Red
} elseif ($CPUUsage.Average -gt 50) {
    Write-Host "⚠️  Moderate CPU usage" -ForegroundColor Yellow
} else {
    Write-Host "✅ CPU usage is normal" -ForegroundColor Green
}

$Report += "CPU PERFORMANCE"
$Report += "==============="
$Report += "Average CPU Usage: $([math]::Round($CPUUsage.Average, 2))%"
$Report += ""

# Memory Usage
Write-Host "`n💾 Memory Performance" -ForegroundColor Green
$Memory = Get-WmiObject -Class Win32_OperatingSystem
$TotalRAM = [math]::Round($Memory.TotalVisibleMemorySize / 1MB, 2)
$FreeRAM = [math]::Round($Memory.FreePhysicalMemory / 1MB, 2)
$UsedRAM = $TotalRAM - $FreeRAM
$MemoryUsagePercent = [math]::Round(($UsedRAM / $TotalRAM) * 100, 2)

$MemoryInfo = @"
Total RAM: $TotalRAM GB
Used RAM: $UsedRAM GB ($MemoryUsagePercent%)
Free RAM: $FreeRAM GB
"@

Write-Host $MemoryInfo

if ($MemoryUsagePercent -gt 85) {
    Write-Host "⚠️  HIGH MEMORY USAGE DETECTED!" -ForegroundColor Red
} elseif ($MemoryUsagePercent -gt 70) {
    Write-Host "⚠️  Moderate memory usage" -ForegroundColor Yellow
} else {
    Write-Host "✅ Memory usage is normal" -ForegroundColor Green
}

$Report += "MEMORY PERFORMANCE"
$Report += "=================="
$Report += $MemoryInfo
$Report += ""

# Disk Usage
Write-Host "`n💿 Disk Performance" -ForegroundColor Green
$Disks = Get-WmiObject -Class Win32_LogicalDisk | Where-Object { $_.DriveType -eq 3 }

foreach ($Disk in $Disks) {
    $TotalSize = [math]::Round($Disk.Size / 1GB, 2)
    $FreeSpace = [math]::Round($Disk.FreeSpace / 1GB, 2)
    $UsedSpace = $TotalSize - $FreeSpace
    $UsagePercent = [math]::Round(($UsedSpace / $TotalSize) * 100, 2)
    
    $DiskInfo = "Drive $($Disk.DeviceID) - Total: $TotalSize GB, Used: $UsedSpace GB ($UsagePercent%), Free: $FreeSpace GB"
    Write-Host $DiskInfo
    
    if ($UsagePercent -gt 90) {
        Write-Host "⚠️  Drive $($Disk.DeviceID) is critically full!" -ForegroundColor Red
    } elseif ($UsagePercent -gt 80) {
        Write-Host "⚠️  Drive $($Disk.DeviceID) is getting full" -ForegroundColor Yellow
    }
    
    $Report += "DISK PERFORMANCE - Drive $($Disk.DeviceID)"
    $Report += "========================"
    $Report += $DiskInfo
    $Report += ""
}

# Top CPU Processes
Write-Host "`n🔥 Top CPU Consuming Processes" -ForegroundColor Green
$TopCPUProcesses = Get-Process | Sort-Object CPU -Descending | Select-Object -First 10 Name, CPU, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet / 1MB, 2)}}
$TopCPUProcesses | Format-Table -AutoSize

$Report += "TOP CPU CONSUMING PROCESSES"
$Report += "==========================="
$Report += ($TopCPUProcesses | Out-String)

# Top Memory Processes
Write-Host "`n🧠 Top Memory Consuming Processes" -ForegroundColor Green
$TopMemoryProcesses = Get-Process | Sort-Object WorkingSet -Descending | Select-Object -First 10 Name, @{Name="Memory(MB)";Expression={[math]::Round($_.WorkingSet / 1MB, 2)}}, CPU
$TopMemoryProcesses | Format-Table -AutoSize

$Report += "TOP MEMORY CONSUMING PROCESSES"
$Report += "=============================="
$Report += ($TopMemoryProcesses | Out-String)

# Startup Programs
Write-Host "`n🚀 Startup Programs Analysis" -ForegroundColor Green
try {
    $StartupPrograms = Get-WmiObject -Class Win32_StartupCommand | Select-Object Name, Command, Location
    Write-Host "Total Startup Programs: $($StartupPrograms.Count)"
    
    if ($StartupPrograms.Count -gt 15) {
        Write-Host "⚠️  High number of startup programs detected! Consider disabling unnecessary ones." -ForegroundColor Yellow
    }
    
    if ($Detailed) {
        $StartupPrograms | Format-Table -AutoSize
    }
    
    $Report += "STARTUP PROGRAMS"
    $Report += "================"
    $Report += "Total Startup Programs: $($StartupPrograms.Count)"
    if ($Detailed) {
        $Report += ($StartupPrograms | Out-String)
    }
    $Report += ""
} catch {
    Write-Host "Unable to retrieve startup programs information"
}

# Windows Services
Write-Host "`n⚙️  Windows Services Analysis" -ForegroundColor Green
$Services = Get-Service
$RunningServices = $Services | Where-Object { $_.Status -eq "Running" }
$StoppedServices = $Services | Where-Object { $_.Status -eq "Stopped" }

Write-Host "Total Services: $($Services.Count)"
Write-Host "Running Services: $($RunningServices.Count)"
Write-Host "Stopped Services: $($StoppedServices.Count)"

$Report += "WINDOWS SERVICES"
$Report += "================"
$Report += "Total Services: $($Services.Count)"
$Report += "Running Services: $($RunningServices.Count)"
$Report += "Stopped Services: $($StoppedServices.Count)"
$Report += ""

# Network Performance
Write-Host "`n🌐 Network Performance" -ForegroundColor Green
try {
    $NetworkAdapters = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object { $_.NetConnectionStatus -eq 2 }
    Write-Host "Active Network Adapters: $($NetworkAdapters.Count)"
    
    $Report += "NETWORK PERFORMANCE"
    $Report += "==================="
    $Report += "Active Network Adapters: $($NetworkAdapters.Count)"
    $Report += ""
} catch {
    Write-Host "Unable to retrieve network information"
}

# Performance Recommendations
Write-Host "`n💡 Performance Recommendations" -ForegroundColor Yellow
Write-Host "==============================" -ForegroundColor Yellow

$Recommendations = @()

if ($CPUUsage.Average -gt 70) {
    $Recommendations += "• High CPU usage detected. Check task manager for resource-heavy applications."
}

if ($MemoryUsagePercent -gt 80) {
    $Recommendations += "• High memory usage detected. Consider closing unnecessary applications or adding more RAM."
}

foreach ($Disk in $Disks) {
    $UsagePercent = [math]::Round((($Disk.Size - $Disk.FreeSpace) / $Disk.Size) * 100, 2)
    if ($UsagePercent -gt 85) {
        $Recommendations += "• Drive $($Disk.DeviceID) is nearly full. Run disk cleanup or move files to free up space."
    }
}

if ($StartupPrograms.Count -gt 15) {
    $Recommendations += "• Many startup programs detected. Disable unnecessary ones to improve boot time."
}

if ($Recommendations.Count -eq 0) {
    $Recommendations += "✅ System appears to be running well! Consider regular maintenance for optimal performance."
}

foreach ($Rec in $Recommendations) {
    Write-Host $Rec
}

$Report += "PERFORMANCE RECOMMENDATIONS"
$Report += "==========================="
$Report += ($Recommendations -join "`n")

# Export Report
if ($ExportReport) {
    try {
        $Report | Out-File -FilePath $OutputPath -Encoding UTF8
        Write-Host "`n📄 Report exported to: $OutputPath" -ForegroundColor Green
    } catch {
        Write-Host "`n❌ Failed to export report: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n🏁 Diagnostics Complete!" -ForegroundColor Cyan
Write-Host "Run with -Detailed for more information or -ExportReport to save results." -ForegroundColor Gray
