<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC Optimizer Pro - Advanced System Optimization</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e2e 0%, #2d2d44 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar Navigation */
        .sidebar {
            width: 280px;
            background: rgba(45, 45, 68, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 212, 255, 0.2);
            padding: 20px;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo h1 {
            color: #00d4ff;
            font-size: 24px;
            font-weight: 700;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .logo p {
            color: #888;
            font-size: 12px;
            margin-top: 5px;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 10px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px;
            text-decoration: none;
            color: #ccc;
            border-radius: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover, .nav-link.active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 212, 255, 0.1));
            color: #00d4ff;
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.3);
        }

        .nav-icon {
            font-size: 20px;
            margin-right: 15px;
            width: 25px;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* Top Bar */
        .top-bar {
            background: rgba(45, 45, 68, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px 30px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Dashboard Content */
        .dashboard {
            padding: 30px;
            flex: 1;
            overflow-y: auto;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        /* Performance Score Card */
        .performance-card {
            background: rgba(45, 45, 68, 0.8);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .performance-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00d4ff, #00ff88, #00d4ff);
            border-radius: 20px;
            z-index: -1;
            animation: borderGlow 3s ease-in-out infinite;
        }

        @keyframes borderGlow {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .performance-score {
            text-align: center;
            margin-bottom: 20px;
        }

        .score-circle {
            width: 150px;
            height: 150px;
            margin: 0 auto 20px;
            position: relative;
        }

        .score-number {
            font-size: 48px;
            font-weight: 700;
            color: #00ff88;
            text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        }

        /* System Metrics */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(30, 30, 46, 0.8);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.1);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #888;
            font-size: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            margin-top: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4ff, #00ff88);
            border-radius: 3px;
            transition: width 1s ease;
        }

        /* Hardware Detection */
        .hardware-section {
            background: rgba(45, 45, 68, 0.8);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }

        .section-title {
            font-size: 20px;
            margin-bottom: 20px;
            color: #00d4ff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .hardware-list {
            list-style: none;
        }

        .hardware-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            background: rgba(30, 30, 46, 0.5);
            border-radius: 10px;
            border-left: 4px solid #00ff88;
        }

        .hardware-name {
            font-weight: 600;
        }

        .hardware-status {
            color: #00ff88;
            font-size: 12px;
        }

        /* Optimization Cards */
        .optimization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .optimization-card {
            background: rgba(45, 45, 68, 0.8);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .optimization-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.2);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #00d4ff;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 25px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: #00ff88;
        }

        .toggle-knob {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 21px;
            height: 21px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        .toggle-switch.active .toggle-knob {
            transform: translateX(25px);
        }

        .card-description {
            color: #ccc;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .optimization-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #888;
        }

        /* Action Buttons */
        .action-buttons {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #00d4ff, #00ff88);
            color: white;
            box-shadow: 0 10px 25px rgba(0, 212, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(0, 212, 255, 0.4);
        }

        .btn-secondary {
            background: rgba(45, 45, 68, 0.8);
            color: #00d4ff;
            border: 1px solid #00d4ff;
        }

        .btn-secondary:hover {
            background: rgba(0, 212, 255, 0.1);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease;
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(45, 45, 68, 0.5);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.5);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="logo">
                <h1>PC Optimizer Pro</h1>
                <p>Advanced System Optimization</p>
            </div>
            
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active">
                        <span class="nav-icon">🏠</span>
                        Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">🔍</span>
                        System Analysis
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">⚡</span>
                        Quick Optimize
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">🧹</span>
                        Disk Cleanup
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">🚀</span>
                        Startup Manager
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">💾</span>
                        Memory Optimizer
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">💿</span>
                        SSD Optimizer
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">
                        <span class="nav-icon">🔧</span>
                        Advanced Settings
                    </a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="system-status">
                    <div class="status-item">
                        <div class="status-dot"></div>
                        <span>System Optimized</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot" style="background: #00d4ff;"></div>
                        <span>Backup Created</span>
                    </div>
                    <div class="status-item">
                        <div class="status-dot" style="background: #ffaa00;"></div>
                        <span>15.79 GB RAM</span>
                    </div>
                </div>
                <div style="color: #888; font-size: 14px;">
                    Last optimized: 2 minutes ago
                </div>
            </div>

            <!-- Dashboard Content -->
            <div class="dashboard">
                <div class="dashboard-grid">
                    <!-- Performance Score -->
                    <div class="performance-card fade-in">
                        <h2 style="color: #00d4ff; margin-bottom: 20px;">System Performance</h2>
                        <div class="performance-score">
                            <div class="score-circle">
                                <div class="score-number">94</div>
                                <div style="color: #888;">Excellent</div>
                            </div>
                        </div>
                        
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-value" style="color: #00ff88;">26%</div>
                                <div class="metric-label">CPU Usage</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 26%;"></div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #00d4ff;">74%</div>
                                <div class="metric-label">RAM Usage</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 74%;"></div>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-value" style="color: #ffaa00;">42%</div>
                                <div class="metric-label">Disk Usage</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 42%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Hardware Detection -->
                    <div class="hardware-section fade-in">
                        <h3 class="section-title">
                            <span>🖥️</span>
                            Detected Hardware
                        </h3>
                        <ul class="hardware-list">
                            <li class="hardware-item">
                                <div>
                                    <div class="hardware-name">Lexar 512GB SSD</div>
                                    <div class="hardware-status">SSD Optimized</div>
                                </div>
                                <span style="color: #00ff88;">✓</span>
                            </li>
                            <li class="hardware-item">
                                <div>
                                    <div class="hardware-name">15.79 GB RAM</div>
                                    <div class="hardware-status">High RAM Config</div>
                                </div>
                                <span style="color: #00ff88;">✓</span>
                            </li>
                            <li class="hardware-item">
                                <div>
                                    <div class="hardware-name">Multi-Core CPU</div>
                                    <div class="hardware-status">Optimized</div>
                                </div>
                                <span style="color: #00ff88;">✓</span>
                            </li>
                            <li class="hardware-item">
                                <div>
                                    <div class="hardware-name">Dedicated GPU</div>
                                    <div class="hardware-status">Driver Optimized</div>
                                </div>
                                <span style="color: #00ff88;">✓</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Optimization Cards -->
                <h2 style="color: #00d4ff; margin-bottom: 20px;">Active Optimizations</h2>
                <div class="optimization-grid">
                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">SSD Optimization</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Optimized for your Lexar 512GB SSD. Disabled defragmentation, enabled TRIM support, and reduced write operations.
                        </div>
                        <div class="optimization-stats">
                            <span>Performance: +15%</span>
                            <span>Lifespan: Extended</span>
                        </div>
                    </div>

                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">High RAM Optimization</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Optimized for 15.79GB RAM system. Disabled paging executive, increased system cache, reduced page file size.
                        </div>
                        <div class="optimization-stats">
                            <span>Memory: +20%</span>
                            <span>Responsiveness: Improved</span>
                        </div>
                    </div>

                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">Visual Effects</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Disabled unnecessary visual effects and animations for better performance while maintaining usability.
                        </div>
                        <div class="optimization-stats">
                            <span>CPU: -10%</span>
                            <span>GPU: -15%</span>
                        </div>
                    </div>

                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">Startup Optimization</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Optimized startup programs and services. Disabled 5 unnecessary startup items for faster boot times.
                        </div>
                        <div class="optimization-stats">
                            <span>Boot Time: -30%</span>
                            <span>Programs: 5 disabled</span>
                        </div>
                    </div>

                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">Power Management</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Set to high performance power plan with optimized CPU and GPU power management for maximum performance.
                        </div>
                        <div class="optimization-stats">
                            <span>Performance: Maximum</span>
                            <span>Turbo Boost: Enabled</span>
                        </div>
                    </div>

                    <div class="optimization-card fade-in">
                        <div class="card-header">
                            <div class="card-title">Network Optimization</div>
                            <div class="toggle-switch active">
                                <div class="toggle-knob"></div>
                            </div>
                        </div>
                        <div class="card-description">
                            Optimized TCP/IP settings, enabled receive side scaling, and configured optimal network adapter settings.
                        </div>
                        <div class="optimization-stats">
                            <span>Throughput: +25%</span>
                            <span>Latency: Reduced</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <button class="btn btn-secondary">
            <span>🔄</span>
            Create Backup
        </button>
        <button class="btn btn-primary">
            <span>⚡</span>
            Optimize Now
        </button>
    </div>

    <script>
        // Simulate real-time updates
        setInterval(() => {
            // Random CPU usage between 20-35%
            const cpuUsage = Math.floor(Math.random() * 15) + 20;
            document.querySelector('.metric-card:nth-child(1) .metric-value').textContent = cpuUsage + '%';
            document.querySelector('.metric-card:nth-child(1) .progress-fill').style.width = cpuUsage + '%';
            
            // Random RAM usage between 70-78%
            const ramUsage = Math.floor(Math.random() * 8) + 70;
            document.querySelector('.metric-card:nth-child(2) .metric-value').textContent = ramUsage + '%';
            document.querySelector('.metric-card:nth-child(2) .progress-fill').style.width = ramUsage + '%';
        }, 3000);

        // Toggle switch functionality
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', () => {
                toggle.classList.toggle('active');
            });
        });

        // Navigation active state
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
                link.classList.add('active');
            });
        });
    </script>
</body>
</html>
