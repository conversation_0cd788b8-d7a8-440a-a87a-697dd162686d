# How to Use the PC Performance Optimization Toolkit

## Quick Start - Immediate Performance Boost

**Option 1: Run the Quick Fix (Safest)**
1. Right-click on `Quick-Fix.ps1` and select "Run with PowerShell"
2. Or open PowerShell as Administrator and run:
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force
   cd "d:\OneDrive\personal\Coding\PCfast"
   .\Quick-Fix.ps1
   ```

**Option 2: Use the Batch Files (Easiest)**
1. Right-click on `Quick-Optimize.bat` and select "Run as administrator"
2. Follow the prompts

**Option 3: Full Interactive Optimization**
1. Right-click on `Run-PC-Optimizer.bat` and select "Run as administrator"
2. Choose from the menu options

## What Each Script Does

### 🔍 System-Diagnostics.ps1
- Analyzes CPU, memory, and disk usage
- Lists top resource-consuming processes
- Checks startup programs
- Provides performance recommendations
- Creates a detailed report

**Usage:**
```powershell
.\System-Diagnostics.ps1 -ExportReport
```

### ⚡ Performance-Optimizer.ps1
- Disables unnecessary visual effects
- Sets high-performance power plan
- Optimizes Windows services
- Disables telemetry
- Configures virtual memory
- Creates system restore point

**Usage:**
```powershell
.\Performance-Optimizer.ps1 -ApplyAll -CreateRestorePoint
```

### 🧹 Disk-Cleanup.ps1
- Removes temporary files
- Clears browser caches
- Empties recycle bin
- Cleans Windows Update cache
- Removes thumbnails and logs

**Usage:**
```powershell
.\Disk-Cleanup.ps1 -DeepClean -IncludeBrowsers -EmptyRecycleBin
```

### 🚀 Startup-Manager.ps1
- Lists all startup programs
- Shows impact ratings
- Allows selective disabling
- Creates backups before changes

**Usage:**
```powershell
.\Startup-Manager.ps1 -Interactive -CreateBackup
```

### 💾 Memory-Optimizer.ps1
- Clears system caches
- Optimizes memory services
- Configures page file
- Closes memory-hungry processes

**Usage:**
```powershell
.\Memory-Optimizer.ps1 -ClearCaches -OptimizeServices -ConfigurePageFile
```

### 🔧 PC-Optimizer.ps1 (Master Script)
- Runs all optimizations in proper order
- Interactive menu system
- Comprehensive logging
- Creates restore points

**Usage:**
```powershell
.\PC-Optimizer.ps1 -Interactive
# or for automatic optimization:
.\PC-Optimizer.ps1 -RunAll
```

## Immediate Actions You Can Take Right Now

### 1. Manual Performance Fixes (No Scripts Needed)

**Task Manager Cleanup:**
1. Press `Ctrl + Shift + Esc` to open Task Manager
2. Click "More details" if needed
3. Go to "Startup" tab
4. Disable unnecessary programs (anything you don't need immediately at startup)
5. Go to "Processes" tab and end any high-CPU processes you don't need

**Windows Settings:**
1. Press `Win + I` to open Settings
2. Go to System > Display > Advanced display settings > Graphics settings
3. Choose "Performance" mode
4. Go to System > Power & battery
5. Set power mode to "Best performance"

**Storage Cleanup:**
1. Press `Win + R`, type `cleanmgr`, press Enter
2. Select your C: drive
3. Check all boxes and click OK
4. Click "Clean up system files" for more options

### 2. Registry Tweaks (Advanced Users)

Open Registry Editor (`Win + R`, type `regedit`):

**Disable Search Indexing:**
- Navigate to: `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\WSearch`
- Change `Start` value to `4` (Disabled)

**Improve Menu Response:**
- Navigate to: `HKEY_CURRENT_USER\Control Panel\Desktop`
- Set `MenuShowDelay` to `0`

**Disable Visual Effects:**
- Navigate to: `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Explorer\VisualEffects`
- Set `VisualFXSetting` to `2`

## Safety Recommendations

1. **Always create a system restore point before making changes**
2. **Run as Administrator for full functionality**
3. **Close important work before running optimizations**
4. **Restart your computer after optimizations**
5. **Keep backups of important data**

## Troubleshooting

**If scripts won't run:**
1. Right-click PowerShell and "Run as Administrator"
2. Run: `Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force`
3. Navigate to the script folder: `cd "d:\OneDrive\personal\Coding\PCfast"`
4. Run the desired script

**If you see errors:**
- Most errors are safe to ignore
- Check if you're running as Administrator
- Some features require specific Windows versions

**If your PC feels slower:**
- Restart your computer
- Restore from the system restore point created earlier
- Contact support or restore from backup

## Expected Results

After running the optimizations, you should see:
- Faster boot times (10-30% improvement)
- More responsive applications
- Better memory usage
- Freed disk space
- Improved overall system performance

The improvements may take a restart to fully take effect.

## Maintenance Schedule

- **Weekly:** Run Quick-Fix.ps1
- **Monthly:** Run full PC-Optimizer.ps1
- **Quarterly:** Run System-Diagnostics.ps1 and review
- **As needed:** Use individual scripts for specific issues
